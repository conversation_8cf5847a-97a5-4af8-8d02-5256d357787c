﻿/* -------------------------------------------
    Single Product Page
---------------------------------------------- */
// issue : this may be included in shop page and product page or ...
.cart-added-alert {
    display: none;
    margin-bottom: 1rem;
    .container > &:first-child {
        margin-top: -1rem;
    }
    span {
        color: $dark-color;
    }
    .btn-success, span {
        vertical-align: middle;
    }
}
//Product Navigation
.product-details .product-navigation {
    padding: 0.2rem 2px .3rem;
}
// Product Gallery
.product-gallery {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 3rem;
    position: sticky;
    &.product-gallery-sticky {
        top: 2rem;
        padding-bottom: 3rem;
        transition: top .3s;
        .sticky-header-active & {
            top: 8rem;
        }
    }
    // Masonry Style
    &.row > * {
        display: flex;
        align-items: center;
    }
    &.row .product-image-full {
        right: 2rem;
    }
}
// Product Image
.product-image-full {
    display: block;
    position: absolute;
    padding: 1rem;
    right: 1rem;
    bottom: 1rem;
    color: $grey-color;
    font-size: 2rem;
    line-height: 1;
    opacity: 0;
    transition: opacity .3s;
    z-index: 1;
    :hover > & {
        opacity: 1;
    }
}
// Product Navigation
.product-navigation {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    padding: 2rem 2px 1.1rem;
    .breadcrumb {
        margin: 0  0 1rem 2rem;
        font-size: inherit;
    }
    .product-nav {
        margin-bottom: 1rem;
    }
}
.product-nav {
    display: flex;
    position: relative;
    color: $grey-color;
    i {
        vertical-align: middle;
        font-size: 1.9rem;
        line-height: 0;
    }
    li + li {
        margin-left: 2rem;
    }
    .product-nav-prev >a,
    .product-nav-next >a {
        display: flex;
        align-items: center;
    }
    .product-nav-prev i {
        margin-right: 2px;
    }
    .product-nav-next i {
        margin-left: 2px;
    }
    .product-name {
        padding-right: 0;
        color: $body-color;
        font-size: 1.3rem;
        font-weight: 400;
        white-space: normal;
    }
    li:hover .product-nav-popup {
        opacity: 1;
        visibility: visible;
        transform: none;
    }
}
.product-nav-popup {
    position: absolute;
    top: 126%;
    right: 16px;
    z-index: 30;
    width: 120px;
    padding: 0 5px 5px;
    line-height: 1.5;
    text-align: center;
    background-color: $bg-secondary-white-color;
    box-shadow: 1px 1px 7px rgba(0,0,0,.1);
    visibility: hidden;
    opacity: 0;
    transform: scale(.9);
    transform-origin: top;
    transition: opacity .3s, transform .3s;
    // Triangle
    &::before {
        content: '';
        position: absolute;
        top: -8px;
        right: 18px;
        width: 16px;
        height: 16px;
        transform: rotate(45deg);
        background-color: $bg-secondary-white-color;
        box-shadow: inherit;
    }
    img {
        position: relative;
        padding-top: 5px;
        background-color: $bg-secondary-white-color;
    }
}
.product-nav-prev .product-nav-popup::before {
    right: 6.6rem;
}
// Product Tabs
.product-tabs {
    .nav-link {
        padding: 1rem 2.5rem;
        line-height: 1.2;
    }
    .nav-item { // issue. pixel perfect
        margin-right: 0;
    }
    .tab-pane {
        padding: 1.5rem .2rem;
       // padding-left: 2px; // issue. pixel perfect
        line-height: 1.86; // issue. pixel perfect
    }
    .product-footer + & {
        margin-top: 2.5rem;
    }
}
.product-tabs .tab-pane,
.product-status {
    .list-type li {
        padding-left: 3rem;
    }
}
.product-status {
    line-height: 2;
}
#product-tab-description, .card-description {
    .description-title {
        font-size: 2rem;
        line-height: 24px;
        & ~ p {
            line-height: 1.86;
        }
    }
    li {
        position: relative;
        padding-left: 2.4rem;
        &::before {
            position: absolute;
            display: block;
            left: 3px;
            top: 2px;
            content: '\e960';
            font-family: 'riode';
            font-weight: 700;
            font-size: 1.3rem;
            color: $dark-color;
        }
    }
    .table {
        tbody {
            line-height: 1.8;
        }
        th, td {
            padding: 10px;
            text-align: left;
            vertical-align: top;
            border-bottom: 1px solid #e9e9e9;
        }
    }
    @include mq(md) {
        .pl-md-6 {
            padding-left: 3rem !important;
        }
    }
    .btn-play {
        display: flex;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate( -50%, -50% );
        align-items: center;
        justify-content: center;
        padding: 0;
        font-size: 35px;
        width: 78px;
        height: 78px;
        border-radius: 50%;
        background-color: $dark-color;
        box-shadow: 0 5px 10px rgba(0,0,0,.2);
        color: $white-color;
    }
    .icon-box-icon {
        font-size: 3rem;
        border-color: #cdcdcd;
    }
    .icon-box-content p {
        font-size: 1.3rem;
        color: $grey-color;
    }
    .divider {
        margin: 1.3rem 0 1.3rem;
        height: 4.4rem;
    }
    figure img {
        vertical-align: middle;
    }
}
#product-tab-description, .card-body {
    .icon-box-icon {
        width: 5.1rem;
        height: 5.1rem;
        margin-right: 2rem;
        .d-icon-truck { font-size: 3.6rem; }
    }
    .icon-box-title { margin-bottom: .2rem; }
}
#product-tab-description .icon-box-wrap {
    max-width: 559px;
}
#product-tab-additional, .card-additional {
    label {
        display: inline-block;
        min-width: 20rem;
        color: $dark-color;
    }
    p { display: inline-block; margin-bottom: 0 }
}
#product-tab-size-guide, .card-sizeguide {
    display: block;
    .size-image {
        flex: 0 0 36%;
        text-align: center;
        padding: 0 10px;
    }
    .size-table {
        flex: 1;
        font-size: 14px;
        text-transform: uppercase;
        color: #21293c;
        thead th {
            padding: 30px 10px 30px 0px;
            font-weight: 600;
            background: #f4f4f2;
            text-align: left;
        }
        tbody {
            tr > * {
                padding: 12px 10px 12px 0px;
                font-weight: 700; 
                text-align: left;
            }
            tr:nth-child(2n) {
                background-color: $border-color-secondary-light;
            }
        }
    }
}
#product-tab-reviews, .card-reviews {
    .comments-list {
        li { padding-bottom: 3rem; margin-bottom: 3rem; border-bottom: 1px solid $border-color}
    }
    .comments {
        padding: 1rem;
    }
    .comment p { margin-bottom: 0; }
    .comment-rating {
        right: 0;
        top: 3rem;
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }
    .ratings-full { margin-right: 0; }
    .reply {
        margin-bottom: 0;
        p {
            margin-bottom: 2.5rem;
            font-size: 1.3rem;
            color: $body-color;
        }
        .btn {
            padding: 1.2em 2.97em;
            i {
                font-size: 1.9rem;
                margin-left: 1rem;
            }
        }
    }
    .form-checkbox {
        font-size: 1.3rem;
    }
   .form-control-label {
       padding-left: 0;
       &::before {
           display: inline-block;
           position: relative;
           transform: none;
            vertical-align: middle;
            margin-right: 0.5rem;
       }
   }
   .btn.btn-link {
       &.active, &:hover {
           color: $primary-color;
       }
   }
}
#product-tab-reviews {
    padding-top: 3rem;
}
// tabinside
.product-details {
    .collapse::after {
        color: $primary-color;
    }
}
.accordion-border .card {
    padding: 0 1.3rem;
}
// Rating Form (new)
.rating-form{
	display: flex;
	align-items: center;
    flex-wrap: wrap;
    margin-bottom: 2.2rem;
    label { margin-right: 1rem; }
}
.rating-stars {
	display: flex;
	position: relative;
	height: 14px;
	font-size: 1.4rem;
	a {
		color: $grey-color;
		text-indent: -9999px;
		letter-spacing: 1px;
		width: 16px;
        letter-spacing: .2em;
	}
	a:before {
		content: '';
		position: absolute;
		left: 0;
		height: 14px;
		line-height: 1;
        font-family: 'riode';
		text-indent: 0;
		overflow: hidden;
		white-space: nowrap;
	}
	a.active:before,
	a:hover:before {
		content: "\e955\e955\e955\e955\e955";
        color: $secondary-color;
	}
	.star-1 {
		z-index: 10;
	}
	.star-2 {
		z-index: 9;
	}
	.star-3 {
		z-index: 8;
	}
	.star-4 {
		z-index: 7;
	}
	.start-5 {
		z-index: 6;
	}
	.star-1:before {
		width: 20%;
	}
	.star-2:before {
		width: 40%;
	}
	.star-3:before {
		width: 60%;
	}
	.star-4:before {
		width: 80%;
	}
	.star-5:before {
		content: "\e955\e955\e955\e955\e955";
	}
}
// Product Sticky
.product-sticky-content {
    &:not(.fixed) {
        .sticky-product-details {
            display: none;
        }
        .container { padding: 0; }
    }
    &.fixed {
        padding: 1rem 0;
        .container {
            display: flex;
            align-items: center;
        }
        .product-form { flex: 1; }
        .product-form-group { justify-content: flex-start; }
        .product-form,
        .product-form-group > * { margin-bottom: 0 }
        .product-form > label { display: none }
        .input-group { margin-right: .8rem }
    }
}
.sticky-product-details {
    display: flex;
    align-items: center;
    img { 
        display: block;
        width: 9rem;
        height: 9rem;
    }
    .product-image {
        margin-right: 1rem;
        max-width: 9rem;
    }
    .product-title { 
        margin-bottom: .5rem;
        font-weight: 700;
    }
    .product-price { font-weight: 600; }
    .product-title, .product-price {
        font-size: 2rem;
    }
    .product-price, .ratings-container { margin-bottom: 0 }
    .product-info {
        display: flex;
    }
    .product-price { margin-right: 2rem; }
}
// Product Page Sidebar
aside {
    .service-list {
        padding: 0 2rem;
        border: 1px solid $border-color-light;
        > * {
            justify-content: flex-start;
            padding: 2.2rem 0;
        }
        > :not(:last-child) {
            border-bottom: 1px solid $border-color;
        }
        i {
            margin-left: 5px;
            font-size: 3.2rem;
        }
        .icon-box-title {
            // margin-bottom: .3rem;
            font-size: 1.5rem;
            letter-spacing: 0;
            line-height: 1.2;
        }
        p {
            line-height: 1.2;
        }
        .icon-box1 i {
            margin-left: 0;
            font-size: 3.7rem;
        }
    }
    .banner-content {
        left: 5%;
        top: 12%;
        width: 90%;
    }
    .banner-subtitle {
        font-weight: 500;
        font-size: 16px;
        color: #777777;
    }
    .banner-title {
        font-size: 2.3rem;
        line-height: 1.4em;
    }
    .owl-nav-top .owl-nav {
        top: -5.1rem;
        i {
            padding: .1rem;
            font-size: 1.3rem;
            &::before {
                font-weight: 600;
            }
        }
    }
}
// product page reviews
.review-form-section { 
    .review-medias {
        margin-top: 3rem;
        margin-bottom: 1rem;
        >.btn {
            display: flex;
            align-items: center;
        }
    }
    .file-input {
        position: relative;
        margin-right: 2rem;
        padding: 0;
        width: auto;
        background-repeat: no-repeat;
        background-size: cover;
    }
    .file-input-wrapper {
        display: block;
        width: 80px;
        height: 80px;
        background-repeat: no-repeat;
        background-size: cover;
    }
    input[type="file"] {
        width: 0 !important;
        height: 0 !important;
        overflow: hidden;
        opacity: 0;
    }
    .btn-action {
        position: absolute;
        right: -10px;
        top: -10px;
        height: 22px;
        width: 22px;
        display: flex;
        justify-content: center;
        line-height: 22px;
        font-size: 10px;
        border-radius: 50%;
        background-color: $bg-secondary-white-color;
        box-shadow: 0 0 5px rgb(0 0 0 / 15%);
        cursor: pointer;
        transition: box-shadow .3s;
        &:before {
            font-family: "Font Awesome 5 Free";
            font-weight: 600;
        }
    }
    .btn-upload::before {
        content: "\f304";
    }
    .btn-remove {
        top: auto;
        bottom: -10px;
        &::before {
            content: "\f00d";
            font-size: 12px;
        }
    }   
}
.review-medias {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
}
.avg-rating-container {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    mark {
        font-size: 6rem;
        font-weight: 700;
        letter-spacing: -.025em;
        line-height: 1;
        color: $primary-color;
        background: none;
        margin-right: 1.8rem;
    }
    .rating-reviews:hover {
        color: $grey-color;
    }
}
.ratings-item {
    display: flex;
    align-items: center;
    .ratings-container {
        margin-right: 1.8rem;
    }
    .progress-value {
        margin-left: 2rem;
    }
}
.rating-percent {
    background: $border-color-light;
    height: 0.8rem;
    width: 20rem;
    max-width: 100%;
    border-radius: 1rem;
    span {
        display: block;
        height: 100%;
        border-radius: inherit;
        background: $grey-color;
    }
}
.comments {
    .toolbox {
        padding: 0 0 1rem;
        margin-bottom: 3rem;
        border-bottom: 1px solid $border-color;
        .btn {
            font-size: 1.3rem;
            padding: 0.82em 1.08em;
        }
    }
    .toolbox .toolbox-sort .form-control {
        min-width: 15rem;
    }
    .toolbox-pagination {
        border: none;
    }
    .page-item {
        margin-bottom: 0;
    }
    .file-input-wrappers {
        display: flex;
        > * {
            width: 6rem;
            height: 6rem;
            margin-right: 1rem;
            cursor: zoom-in;
        }
        img {
            height: 100%;
        }
    }
    .feeling {
        i {
            font-size: 1.4rem;
        }
    }
    .btn-play {
        display: flex;
        justify-content: center;
        align-items: center;
        i {
            width: 3rem;
            height: 3rem;
            line-height: 3rem;
            text-align: center;
            border-radius: 50%;
            color: $white-color;
            background: rgba(0, 0, 0, .7);
        }
    }
}
.review-form-section {
    .review-form-wrapper {
        position: fixed;
        top: 0;
        bottom: 0;
        left: -100%;
        background-color: $bg-secondary-white-color;
        z-index: 1101;
        box-shadow: 0px 2px 9px rgb(0 0 0 / 10%);
        padding: 3rem;
        transition: right .3s;
        overflow: auto
    }
    .review-overlay {
        position: fixed;
        left: 0;
        width: 100vw;
        top: -10vh;
        height: 120vh;
        background: rgba(0,0,0,0.3);
        z-index: 1100;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s;
    }
    &.opened {
        .review-form-wrapper {
            right: 0;
        }
        .review-overlay {
            opacity: 1;
            visibility: visible;
        }
    }
}
// Responsive
@include mq(sm, max) {
    #product-tab-reviews, .card-body {
        .comments-list {
            & > ul {
                padding: 0;
            }
        }
        .comment-body {
            padding-left: 1.5rem;
        }
    }
    #product-tab-additional label { min-width: 13rem; }
}
@include mq(sm) {
    #product-tab-size-guide, .card-sizeguide {
        thead th:first-child, .size-table tbody th {
            padding-left: 3rem;
        }
    }
    #product-tab-size-guide {
        display: flex;
    }
}
@include mq(md) {
    // .product-gallery.sticky-sidebar {
    //     padding-bottom: 7rem;
    // }
}
@include mq(lg) {
    .product-form select {
        width: 20rem;
    }
    .product-single .product-details {
        padding-left: 1rem;
    }
    // Issue : Product Gallery Type
    .product-details.row {
        padding-left: 0;
        > :last-child {
            padding-left: 2rem;
        }
    }
}
// @include mq(lg, max) {
//     #product-tab-reviews {
//         .toolbox-item:first-child a {
//             border: 2px solid #e4eaec;
//         }
//     }
//}
// Product FullWidth
@include mq(xxl) {
    .container-fluid {
        .product-thumbs.owl-carousel {
            width: calc(100% + 20px);
            margin: 0 -10px;
        }
        .product-thumb {
            margin: 0 10px;
        }
        .product-thumbs-wrap {
            margin-top: 20px;
        }
        .product-thumbs .owl-prev {
            left: 10px;
        }
        .product-thumbs .owl-next {
            right: 10px;
        }
        .pg-vertical {
            .product-thumb {
                margin: 0 0 20px;
            }
            .product-thumbs-wrap {
                margin: 0 20px 0 0;
            }
            .product-single-carousel {
                max-width: calc(100% - 129px);
            }
        }
    }
}
@media (min-width: 992px) and (max-width: 1199px) {
    aside .service-list {
        padding: 0 1rem;
    }
}
@media (min-width: 768px) and (max-width: 1035px) {
    .product-single #product-tab-size-guide {
        display: block;
    }
}
@include mq(lg, max) {
    aside .service-list .icon-box-side{
        flex-direction: row;
        .icon-box-icon {
            padding: 0;
            margin: 0 2rem 0 0;
        }
        .icon-box-content {
            text-align: left;
        }
    }
}
// Product Sticky Both
.product-sticky-both {
    .btn-cart {
        margin-bottom: 1rem;
    }
    .product-action { 
        display: flex;
    }
    .btn-wishlist {
        margin-bottom: 1rem;
    }
}
@include mq(lg) {
    .product-sticky-both .btn-cart {
        max-width: 100%;
    }
    .product-sticky-both .product-form {
        .select-box,
        .p-relative {
            margin-right: 0;
            width: 100%;
        }
        select {
            flex: 1;
            width: 100%;
        }
    } 
}
@include mq(lg, max) {
    #product-tab-description, .card-body {
        .icon-box-side {
            flex-direction: row;
        }
        .icon-box-icon {
            display: inline-flex;
            margin-bottom: 0;
            padding: 0;
        }
        .icon-box-content {
            text-align: left;
        }
    }
    .review-form-wrapper {
        width: 45rem;
    }
}
@include mq(sm, max) {
    .review-form-wrapper {
        width: 30rem;
        .file-input-wrapper {
            width: 6rem;
            height: 6rem;
        }
        .file-input {
            margin-right: 1rem;
        }
    }
}
//dark-theme
.dark-theme {
    .product-nav .product-name,
    .avg-rating-title,
    .progress-value {
        color: #999;
    }
    .size-image img {
        filter: invert(1);
    }
    .size-table {
        color: #fff;
        thead th {
            background: #999;
        }
        tbody {
            tr:nth-child(2n) {
                background-color: #999;
            }
        }
    }
    .rating-percent {
        background: $grey-color;
        span {
            background: $white-color;
        }
    }
}