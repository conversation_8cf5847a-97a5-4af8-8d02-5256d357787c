/* 
Demo 31 Variables
*/
@include set(
    (
        base: (
            _container-width: 1520px,
        ),
        header: (
            _link-active-color: #fff,
            middle: (
				padding-top: 0rem,
				padding-bottom: 0rem,
				font-weight: 600,
                color: #fff,
                background-color: $primary-color,
                logo: (
                    margin-right: 0,
                    margin-bottom: false
                )
            ),
            // Logo
            logo: (
                max-width: 250px,
                background-color: $secondary-color,
                padding: 26.5px 26px
            ),
            main-nav: (
                margin-left: 3.9rem
            ),
            wishlist: (
                margin-right: 2.1rem,
                icon: (
                    font-size: 2.7rem
                )
            ),
            cart: (
                icon: (
                    display: block,
                    font-size: 2.6rem,
                    color: false,
                ),
                toggle: (
                    padding: false,
                    font-size: 2.7rem,
                ),
            ),
            call: (
                label: (
                    color: #fff,
                    font-size: 1.1rem,
                    line-height: 1.5rem,
                    font-weight: 300,
                    letter-spacing: -.025em,
                ),
                icon: (
                    margin: 0,
                    font-size: 2.5rem
                )
            ),
            login: (
                icon: (
                    font-size: 2.5rem,
                    margin: 0px 10px 0px 0px,
                )
            ),
            sticky: (
                padding: 0,
                background: $primary-color
            ),
            mmenu-toggle: (
                color: #fff,
            ),
        ),
        menu: (
            // Ancestor
            ancestor: (
                _gap: 2.47rem,
                text-transform: initial,
                font-size: 1.4rem,
                font-family: $font-family,
                font-weight: 600,
                letter-spacing: normal,
                _active-color: #fff,
            ),
        ),
        product: (
            label: (
                new: (
                    color: false,
                    background: $secondary-color
                ),
                sale: (
                    color: false,
                    background: $primary-color
                ),
            )
        ),
        product-single: (
            name: (
                font-family: $font-family,
            ),
        ),
        post: (
            padding: 2rem,
            box-shadow: 0px 0px 20px 0px rgba(100,100,100,0.1),
            background-color: #fff,
            body: (
                padding: 25px 0px 10px 3px
            ),
            calendar: (
                color: #fff,
                background: rgba(40,121,254,0.8),
                border-color: transparent,
            ),
            title: (
                text-transform: initial,
                font-size: 1.8rem,
                font-weight: 700,
                letter-spacing: -0.025rem,
            ),
            content: (
                line-height: 1.7,
            ),
        ),
        footer: (
            top: (
                padding: 3.5rem 0 3.1rem,
                border-bottom: false
            ),
            middle: (
                border-bottom: false,
                padding: 63px 0 17px,
                widget: (    
                    title: (
                        text-transform: initial,
                        font-size: 1.5rem,
                        color: #fff,
                        font-family: $font-family,
                        letter-spacing: .7px,
                        font-weight: 600,
                        margin-bottom: 1.1rem,
                        line-height: 1.1
                    ),
                    label: (
                        color: #777,
                        font-family: $font-family,
                        margin-bottom: 1.1rem,
                        font-weight: 700,
                        letter-spacing: 0,
                        line-height: 1.2
                    ),
                    body: (
                        font-size: 1.3rem,
                        color: #a6a6a6,
                        font-family: $font-family,
                    ),
                    list-item: (
                        display: flex,
                        flex-direction: column,
                        margin-bottom: 11px,
                        letter-spacing: -.1px,
                    )
                )
            ),
            bottom: (
                padding: 3rem 0 2.9rem 0,
                border-top: 1px solid #333,
            ),
            social-link: (
                width: 4rem,
                height: 4rem,
                font-size: 1.9rem,
                color: #FFFFFF,
                border-color: #6381B3,
                margin-right: 10px
            ),
            newsletter: (
                title: (
                    font-size: 1.8rem,
                    letter-spacing: -.02em,
                    font-family: $font-family
                ),
                desc: (
                    font-size: 1.4rem,
                    line-height: 18px,
                    letter-spacing: -.3px,
                    opacity: .8
                ),
                form: (
                    max-width: 60rem
                ),
                btn: (
                    font-size: 13px
                )
            ),
            copyright: (
                font-weight: 400,
                letter-spacing: 0,
            ),
        )
    )
);