/* -------------------------------------------
    Helper
---------------------------------------------- */
// Width
.w-100 {
    width: 100% !important;
}
.x-50 {
    left: 50% !important;
    transform: translateX(50%) !important;
    &.y-50 {
        transform: translate( -50%, -50% ) !important;
    }
}
.y-50 {
    top: 50% !important;
    transform: translateY(-50%) !important;
}
// Display
.d-none {
    display: none !important;
}
.d-block {
    display: block !important;
}
.d-inline-block {
    display: inline-block !important;
}
.d-flex {
    display: flex !important;
}
.justify-content-center {
    justify-content: center !important;
}
.align-items-center {
    align-items: center !important;
}
.flex-wrap {
    flex-wrap: wrap !important;
}
.overflow-hidden {
    overflow: hidden !important;
}
@include mq(lg) {
    .d-lg-none {
        display: none !important;
    }
    .d-lg-block {
        display: block !important;
    }
}
// Font Family
.font-primary {
    font-family: $font-family !important;
}
.font-secondary {
    font-family: $second-font-family !important;
}
.font-tertiary {
    font-family: $third-font-family !important;
}
// Font Weight
.font-weight-bold {
    font-weight: 700 !important;
}
.font-weight-semi-bold {
    font-weight: 600 !important;
}
.font-weight-normal {
    font-weight: 400 !important;
}
// Text Transform
.text-uppercase {
    text-transform: uppercase !important;
}
.text-none {
    text-transform: none !important;
}
.text-center {
    text-align: center!important;
}
.text-left  {
    text-align: left !important;
}
.text-right {
    text-align: right !important;
}
// Text Color
.text-white {
    color: #fff !important;
}
.text-grey {
    color: $grey-color !important;
}
.text-body {
    color: $body-color !important;
}
.text-dark {
    color: $dark-color !important;
}
.text-primary {
    color: $primary-color !important;
}
// Letter Spacing
.ls-s {
    letter-spacing: -.01em !important;
}
.ls-m {
    letter-spacing: -.025em !important;
}
.ls-l {
    letter-spacing: -.05em !important;
}
// Line Height
.lh-1 {
    line-height: 1 !important;
}
// Background
.bg-white {
    background-color: #fff !important;
}
.bg-dark {
    background-color: $dark-color !important;
}