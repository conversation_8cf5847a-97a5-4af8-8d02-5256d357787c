/*
Demo 8
*/
@include set(
    (
        base: (
            _container-width: 1460px,
            title: (
                margin-bottom: 3rem,
                font-family: $second-font-family,
                font-size: 3rem,
                font-weight: 700,
                letter-spacing: 0,
                text-transform: uppercase,
                color: #444,
                desc: (
                    font-family: $font-family,
                    margin-bottom: 2rem,
                    color: #444
                )
            ),
            wrapper: (
                title: (
                    margin-bottom: 1.1rem
                )
            )
        ),
        header: (
            font-family: $second-font-family,
            top: (
                background: transparent,
                border-bottom: 1px solid #2D2E31,
                color: $grey-color,
                text-transform: uppercase,
                letter-spacing: 0,
                _links-gap: 1.8rem
            ),
            middle: (
                padding-top: 2.9rem,
                padding-bottom: 2.9rem,
                background: transparent,
                border-bottom: 1px solid #2D2E31,
                color: $white-color,
                login: (
                    padding-top: .3rem,
                    padding-bottom: 0,
                    icon: (
                        font-size: 2.1rem
                    )
                ),
                logo: (
                    margin-bottom: 0,
                    margin-right: 0
                )
            ),
            wishlist: (
                icon: (
                    padding-top: .4rem,
                    font-size: 2.2rem
                )
            ),
            search: (
                toggle: (
                    padding: 1.1rem 0 .7rem
                )
            ),
            cart: (
                toggle: (
                    padding:.6rem 1.1rem .3rem 0
                )
            ),
            mmenu-toggle: (
                color: $grey-color
            )
        ),
        menu: (
            ancestor: (
                padding: 2rem 0,
                font-size: 1.4rem,
                font-weight: 600,
                color: $white-color,
                text-transform: uppercase,
                letter-spacing: 0,
                _gap: 3.1rem
            ),
            megamenu: (
                font-family: $font-family
            ),
            submenu: (
                font-family: $font-family
            )
        ),
        mobile-menu: (
            letter-spacing: .025em,
        ),
        post: (
            detail: (
                padding: 2.3rem 0 2.2rem
            ),
            meta: (
                margin-bottom: .4rem
            ),
            title: (
                margin-bottom: 2rem
            ),
            btn: (
                _icon-gap: .5rem
            )
        ),
        footer: (
            background: #1a1f20,
            middle: (
                padding: 8rem 0 2rem,
                widget: (
                    title: (
                        text-transform: none
                    )
                )
            ),
            bottom: (
                padding: 3.4rem 0 3.2rem
            ),
            about: (
                logo: (
                    margin-top: -.5rem,
                    margin-bottom: 2.5rem,
                ),
                p: (
                    max-width: 29rem,
                    margin-bottom: 2.7rem,
                    letter-spacing: -.000 7em
                )
            ),
            social-link: (
                border: 1px solid $grey-color,
                line-height: 2.9rem
            )
        )
    )
)