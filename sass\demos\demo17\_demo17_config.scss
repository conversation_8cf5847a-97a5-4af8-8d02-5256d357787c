/* 
Demo 17 Variables
*/
@include set(
    (
        base: (
            title: (
                color: #2
                letter-spacing: -.025em,
                margin-bottom: 2.7rem,
                text-transform: none
            )
        ),
        header: (
            top: (
                background: #37c,
                border-bottom: none
            ),
            middle: (
                color: #2
                font-weight: 600,
                font-size: 1.4rem,
                padding-top: 2.8rem,
                login: (
                    margin-right: 3.2rem,
                    margin-top: .5rem,
                    icon: (
                        font-size: 2.7rem
                    )
                )
            ),
            bottom: (
                color: #2
                border-top: 1px solid #e1e1e1,
                border-bottom: 1px solid #e1e1e1,
                font-weight: 700,
                padding-bottom: 0,
            ),
            cart: (
                toggle: (
                    padding: 0.7rem 0 0.4rem
                )
            ),
            sticky: (
                padding-top: 1.8rem,
                padding-bottom: 1.6rem
            )
        ),
        menu: (
            ancestor: (
                padding: 2rem 0,
                font-weight: 600,
                color: #2
                _gap: 3.1rem
            ),
        ),
        product: (
            body: (
                padding-top: 1.5rem,
                padding-bottom: 2rem
            ),
            price: (
                color: #2
            ),
        ),
        footer: (
            font-family: $font-family,
            middle: (
                padding: 7.5rem 0 2.1rem,
                font-family: $font-family,
                font-weight: 400,
                border-color: #333,
                widget: (
                    margin-bottom: 2.9rem,
                    title: (
                        margin-bottom: 1.3rem,
                        font-weight: 600,
                    ),
                    body: (
                        padding: 0.4rem 0 0,
                        color: #999,
                    ),
                    list-item: (
                        margin-bottom: 1.5rem
                    )
                ),
            ),
            bottom: (
                padding: 3.3rem 0
            ),
            copyright: (
                font-family: $font-family,
                letter-spacing: -.01em,
            ),
            newsletter: (
                title: (
                    margin-bottom: .9rem
                ),
                desc: (
                    line-height: 1.54,
                    color: #666
                )
            )
        ),
    )
)