@charset 'UTF-8';
/*
1. config
2. mixins
3. plugins
4. base
5. components
6. header
7. footer
8. demo
*/
/* 1. config */
@import 'config/variables';
/* 2. mixins */ 
@import 'mixins/breakpoints'; 
@import 'mixins/core';
@import 'mixins/buttons';
/* 3. plugins */
@import 'components/slider';
@import 'components/nouislider';
@include set(
    (
        base: (
            title: (
                font-size: false,
                text-transform: false,
            ),
            list-circle: (
                icon: (
                    font-size: 2rem
                )
            )
        ),
        widget: (
            title: (
                text-transform: false
            )
        )
    )
);
/* 4. base */
@import 'base/base';
@import 'base/helper';
@import 'base/type';
@import 'base/layout';
@import 'base/grid';
@import 'base/spacing';
/* 5, components */
@import 'components/accordion';
@import 'components/alerts';
@import 'components/animation';
@import 'components/banners';
@import 'components/blog';
@import 'components/buttons';
@import 'components/categories';
@import 'components/counter';
@import 'components/elements';
@import 'components/font-icons';
@import 'components/forms';
@import 'components/icon-boxes';
@import 'components/icons';
@import 'components/instagram';
@import 'components/member';
@import 'components/minipopup';
@import 'components/overlay';
@import 'components/page-header';
@import 'components/pagination';
@import 'components/popups';
@import 'components/products';
@import 'components/product-single';
@import 'components/social-icons';
@import 'components/sidebar';
@import 'components/sidebar-shop';
@import 'components/store';
@import 'components/tabs';
@import 'components/testimonials';
@import 'components/tooltip';
@import 'components/titles';
@import 'components/widgets';
/* 6. header */
@import 'base/header/header';
@import 'base/header/dropdown';
@import 'base/header/menu';
/* 7. footer */
@import 'base/footer/footer';
/* 8. Pages */
@import 'pages/about';
@import 'pages/buttons';
@import 'pages/categories';
@import 'pages/coming';
@import 'pages/contact';
@import 'pages/error';
@import 'pages/products';
@import 'pages/cta';
@import 'pages/titles';
@import 'pages/instagrams';
@import 'pages/blog';
@import 'pages/shop';
@import 'pages/product-single';
@import 'pages/post-single';
code,
mark {
    border-radius: 4px;
    padding: 2px 5px;
    font-size: 1.2em;
    color: $primary-color;
    @include css( background, base, _grey-section-bg );
}
code + code {
    margin-left: .5em;
}
mark {
    background: yellow;
    font-weight: 700;
}
pre {
    max-height: 30rem;
    padding: 1rem 2rem;
    margin-bottom: 2rem;
    border: 1px solid #e1e1e1;
    border-radius: 5px;
    background: #f5f7f9;
    font-size: 1.2em;
    overflow: auto;
    code,
    mark {
        border: 1px solid #e1e1e1;
        font-size: 1em;
    }
}
.page-header {
    color: #222;
}
.page-header .widget-search {
    max-width: 50rem;
    width: 100%;
    padding: 0 2rem;
}
.page-header input.input-search {
    padding-left: 1.5rem;
    border-radius: 3rem;
    background: $white-color;
    border: 0;
    color: #222;
}
.page-header .btn-search {
    font-size: 1.5rem;
    min-width: 5rem;
    color: #222;
}
.toolbox-wrap .widget-body {
    margin-bottom: 0;
}
.filter-items.search-ul li {
    padding-left: 3px;
    &:hover, &.show {
        padding-left: 2.2rem;
    }
}
.filter-items li {
    font-size: 1.4rem;
}
.collapsed+.widget-body {
    display: none;
}
.search-pane {
    position: relative;
    padding-top: 3rem;
    margin-bottom: 5rem;
    border-top: 3px solid #e1e1e1;
}
.search-result {
    padding: 0;
    margin: 0;
    border: 0;
    box-shadow: none;
}
.sidebar + .main-content > .title {
    transition: padding-left  .3s;
}
.sidebar.closed + .main-content > .title {
    padding-left: 14rem;
}
.sidebar-toggle-remain.sidebar.closed .toggle-remain i::before {
    content: "\e933";
}
.shop-sidebar .sidebar-toggle-btn {
    padding-left: 0;
}
.search-count {
    display: block;
    position: absolute;
    top: 2rem;
    right: 2rem;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    color: #fff;
    background: #2b579a;
    font-size: 2rem;
    text-align: center;
    vertical-align: middle;
    line-height: 3rem;
    box-shadow: 0 0 1rem #2b579a;
}
.page-content {
    min-height: 640px;
}
.table {
    margin-bottom: 3rem;
    th,
    td {
        border: 1px solid $border-color;
        padding: .5rem 1rem;
    }
    th {
        text-align: left;
        padding: 1rem;
        @include css( background, base, _grey-section-bg );
    }
}
@include mq(xs, max) {
    .table {
        tr {
            display: block;
            margin-bottom: 2rem;
            td {
                display: block;
                width: 100%;
                border: 0;
                &:first-child {
                    background: #e1e1e1;
                }
                &:last-child {
                    border: 1px solid #e1e1e1;
                }
            }
        }
        th {
            display: none;
        }
    }
}
.document-content a {
    color: #007bff;
    &:hover {
        color: #0056b3;
        text-decoration: underline;
    }
}
h6 {
    margin-bottom: .5rem;
}
