/* -------------------------------------------
    Account
---------------------------------------------- */
.account {
    line-height: 2.15;
    p {
        font-family: $font-family;
        line-height: 2.15;
    }
    label {
        display: block;
        padding-left: .2rem;
        margin-bottom: 1rem;
        font-size: 1.3rem;
        line-height: 1;
    }
    .form-control {
        font-size: 1.3rem;
        font-family: $font-family;
        transition: background-color .3s, border-color .3s;
        color: #999;
        &:not(:focus) {
            background-color: #f6f7f9;
        }
        &:focus {
            border-color: $primary-color;
        }
    }
    .nav-tabs {
        width: 23.7%;
        border-left: none;
    }
    .nav-link {
        transition: color .3s, padding .3s;
        &::before {
            content: '\f274';
            display: block;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            opacity: 0;
            visibility: hidden;
            font-family: 'LineAwesome';
            font-size: 1.4rem;
            transition: opacity .3s, visibility .3s; 
        }
    }
    .nav-item {
        border-bottom: 1px solid #eee;
        color: #222;
        a {
            display: block;
            padding: 1.4rem 2px;
            margin: 0;
            font-weight: 400;
            font-size: 1.4rem;
            letter-spacing: 0;
            line-height: 1;
            text-transform: none;
        }
        &:hover {
            .nav-link { color: $primary-color; }
        }
        &.show .nav-link,
        .nav-link.active {
            padding-left: 2.2rem;
            color: $primary-color;
            &::before {
                visibility: visible;
                opacity: 1;
            }
        }
    }
    .tab-content {
        width: auto;
        flex: 1;
        padding-left: 3rem;
    }
    .tab-pane { padding-top: .3rem; }
}
@include mq(md, max) {
    .account {
        .nav-tabs { width: 100%; }
        .tab-content { padding-left: 0; }
    }
}
.card-address {
    background: #f6f7f9;
    border: 1px solid $border-color;
    p { margin-bottom: 1rem; }
    .card-body { padding: 2.5rem; }
}