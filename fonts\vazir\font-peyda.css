@font-face {
  font-family: peyda;
  src: url('PeydaWeb-Black.eot');
  src: url('PeydaWeb-Black.eot?#iefix') format('embedded-opentype'),
       url('PeydaWeb-Black.woff2') format('woff2'),
       url('PeydaWeb-Black.woff') format('woff'),
       url('PeydaWeb-Black.ttf') format('truetype');
       font-weight: normal;

}
@font-face {
  font-family: peyda;
  src: url('PeydaWeb-Bold.eot');
  src: url('PeydaWeb-Bold.eot?#iefix') format('embedded-opentype'),
       url('PeydaWeb-Bold.woff2') format('woff2'),
       url('PeydaWeb-Bold.woff') format('woff'),
       url('PeydaWeb-Bold.ttf') format('truetype');
       font-weight: bold;

}
@font-face {
  font-family: peyda;
  src: url('PeydaWeb-ExtraBold.eot');
  src: url('PeydaWeb-ExtraBold.eot?#iefix') format('embedded-opentype'),
       url('PeydaWeb-ExtraBold.woff2') format('woff2'),
       url('PeydaWeb-ExtraBold.woff') format('woff'),
       url('PeydaWeb-ExtraBold.ttf') format('truetype');
       font-weight: bolder;

}
@font-face {
  font-family: peyda;
  src: url('peydaWeb-extralight.eot');
  src: url('peydaWeb-extralight.eot?#iefix') format('embedded-opentype'),
       url('peydaWeb-extralight.woff2') format('woff2'),
       url('peydaWeb-extralight.woff') format('woff'),
       url('peydaWeb-extralight.ttf') format('truetype');
       font-weight: 100;

}
@font-face {
  font-family: peyda;
  src: url('peydaWeb-light.eot');
  src: url('peydaWeb-light.eot?#iefix') format('embedded-opentype'),
       url('peydaWeb-light.woff2') format('woff2'),
       url('peydaWeb-light.woff') format('woff'),
       url('peydaWeb-light.ttf') format('truetype');
       font-weight: 200;

}
@font-face {
  font-family: peyda;
  src: url('PeydaWeb-Medium.eot');
  src: url('PeydaWeb-Medium.eot?#iefix') format('embedded-opentype'),
       url('PeydaWeb-Medium.woff2') format('woff2'),
       url('PeydaWeb-Medium.woff') format('woff'),
       url('PeydaWeb-Medium.ttf') format('truetype');
       font-weight: 400;

}
@font-face {
  font-family: peyda;
  src: url('PeydaWeb-Regular.eot');
  src: url('PeydaWeb-Regular.eot?#iefix') format('embedded-opentype'),
       url('PeydaWeb-Regular.woff2') format('woff2'),
       url('PeydaWeb-Regular.woff') format('woff'),
       url('PeydaWeb-Regular.ttf') format('truetype');
       font-weight: 500;

}
@font-face {
  font-family: peyda;
  src: url('PeydaWeb-SemiBold.eot');
  src: url('PeydaWeb-SemiBold.eot?#iefix') format('embedded-opentype'),
       url('PeydaWeb-SemiBold.woff2') format('woff2'),
       url('PeydaWeb-SemiBold.woff') format('woff'),
       url('PeydaWeb-SemiBold.ttf') format('truetype');
       font-weight: 600;

}
@font-face {
  font-family: peyda;
  src: url('PeydaWeb-Thin.eot');
  src: url('PeydaWeb-Thin.eot?#iefix') format('embedded-opentype'),
       url('PeydaWeb-Thin.woff2') format('woff2'),
       url('PeydaWeb-Thin.woff') format('woff'),
       url('PeydaWeb-Thin.ttf') format('truetype');
}