/*
 Demo 4
    - Home
    - Product page
*/
/* Header */
.welcome-msg { padding: 1.35rem 0; }
.header-top .divider { margin-left: 2rem; }
.search-toggle {
    padding: 1.3rem 0 .9rem;
    i { font-size: 2rem; }
}
.header-middle .icon-box-icon { font-size: 2.3rem; }
.cart-dropdown {
    i { font-size: 2.3rem; }
    &.type2 .cart-count {
        top: 0;
        right: -.7rem;
    }
}
/* Intro Section */
.intro-slider {
    .banner-content { left: 8.3%; }
    .banner-subtitle { 
        margin-bottom: 1.3rem;
        font-size: 2em; 
        letter-spacing: -.000 5em; 
    }
    .owl-dots { bottom: 3.5rem; }
    .owl-animated-out { animation-duration: .5s; }
    .btn i { margin-left: .9rem; }
}
.intro-slide1 .banner-title { 
    margin-bottom: 3.9rem;
    font-size: 3.4rem;
    line-height: 1.21; 
}
.intro-slide2 .banner-title {
    font-size: 4em;
    line-height: .9; 
    letter-spacing: -0.028em; 
}
.banner-radius { border-radius: .3rem; }
.intro-banner1 {
    .banner-content { right: 2rem; }
    .banner-title { 
        font-size: 2em; 
        letter-spacing: -.000 5em;
    }
}
.intro-banner2 {
    .banner-title { font-size: 3em; }
    p { line-height: 1.72; }
}
.intro-slide {
    &.overlay-dark figure::after { transition-duration: .5s, .5s, .5s; }
    &.overlay-zoom img { transition-duration: .5s; }
}
.intro-section {
    .category img { border-radius: 0; }
    .category-name { 
        font-size: 1.8rem; 
        letter-spacing: -.02em;
    }
    .height-x2 { height: 530px; }
    .height-x1 { height: 265px; }
}
.title.with-link {
    padding-top: 2.6rem;
    line-height: 2; 
}
/* Category Section */
.category-section {
    .category-content { bottom: 2rem; }
    .btn {
        transform: none;
        opacity: 1;
        left: auto;
        right: auto;
        transition: color .3s, background-color .3s, border-color .3s;
    }
    .banner-subtitle {
        font-size: 2em;
        letter-spacing: .025em;
    }
    .banner-title {
        margin-bottom: .3rem;
        font-size: 3.6em;
        letter-spacing: -.0138em;
    }
    h5 { 
        margin-bottom: 3.8rem;
        font-size: 2em; 
    }
}
/* Video Banner Section */
.video-banner {
    padding: 8.8rem 0 7.2rem;
    margin-top: 5.7rem;
    .banner-subtitle { font-size: 1.6em; line-height: 1.13; }
    .banner-title { 
        font-size: 3em; 
        line-height: 1.4; 
        letter-spacing: .016em;
    }
    .btn-play {
        width: 61px;
        height: 61px;
        border: 1px solid #fff;
        i {
            color: #fff;
            font-size: 3.5rem;
        }
    }
}
/* Post */
.post {
    //.post-details { padding-top: 2.3rem; }
    .btn { color: $body-color; }
}
/* Banner CTA */
.banner-section { padding-top: .7rem; }
.banner-cta {
    padding: 4.7rem 2rem 5.1rem;
    .banner-subtitle { 
        margin-bottom: .8rem;
        font-size: 2.4em; 
    }
    .banner-title { 
        margin-bottom: 1.9rem;
        font-size: 4em; 
    }
    .btn { 
        padding: 1.25em 2.7em; 
        font-size: 1.4em;
        i { 
            margin: 0 0 .1rem .9rem; 
            font-size: 1.428em;
        }
    }
}
/* Product Widgets */
.home .widget-products .widget-title {
    padding: 3rem 0rem 1.5rem;
    font-size: 2rem;
    letter-spacing: -.03em;
    text-transform: none;
}
/* footer */
.footer-info {
    max-width: 60rem;
    // margin-top: -8px;
}
.footer-bottom {
    display: block;
    text-align: center;
}
.widget-newsletter {
    .input-wrapper-inline {
        height: 5rem;
    }
    .form-control { 
        padding-left: 1.3rem;
        padding-right: 1.3rem;
    }
    .btn { 
        padding-left: 1.15em; 
        padding-right: 1.15em;
        i { margin-top: -.3rem; }
    }
}
.social-link {
    font-size: 1.5rem;
}
//Shop page
.sidebar-content {
    .widget-title { padding-bottom: 1.9rem; }
    .owl-nav-top .owl-nav { 
        right: -.1rem;
        top: -3.8rem; 
        i { font-size: 1.3rem; }
    }
}
//Shop page
.product-single .ratings-container { margin-bottom: 1.6rem; }
.owl-theme.owl-carousel .owl-nav + .owl-dots {
    margin-top: 0;
}
//Responsive
@include mq(xl, max) {
    .main-nav {
        flex: 1;
        margin: 0 !important;
        .menu >li {
            margin-right: 3rem;
        }
    }
}
@include mq(lg, max) {
    .header-middle .header-left  { flex: none; }
    .intro-section {
        .height-x2 { height: 400px; }
        .height-x1 { height: 200px; }
    }
    .footer-middle { padding-bottom: 5.5rem; }
}
@include mq(md, max) {
    .category-banner { font-size: .7rem;  }
}
@include mq(sm, max) {
    .category, .banner { font-size: .9rem; }
}
@include mq(xs, max) {
    .banner-cta { font-size: .7rem; }
}