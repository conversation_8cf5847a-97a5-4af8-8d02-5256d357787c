/*
 *
 *  De<PERSON><PERSON> custom
*/
// Base
// Header
.header-top {
    .social-link {
        font-size: 1.4rem;
    }
    .delimiter {
        margin: 0 .3rem;
    }
    .divider {
        margin: 0 0.3rem 0 2rem;
    }
}
.header-middle {
    .icon-box-title {
        line-height: 1.2
    }
    .cart-toggle i.d-icon-bag {
        margin-bottom: 0px;
    }
    .cart-label > .cart-price {
        color: $primary-color;
    }
    .header-search input::placeholder {
        letter-spacing: 0;
    }
}
// Home
.intro-section {
    img {
        min-height: 25rem;
        object-fit: cover;
    }
}
.intro-slider {
    .banner-subtitle {
        font-size: 2.6rem
    }
    .banner-content {
        padding: 0 1.5rem;
    }
    .banner-title {
        font-size: 6em;
    }
    img {
        min-height: 50rem;
        object-fit: cover;
    }
    &.owl-carousel .owl-nav {
        button {
            font-size: 45px;
            color: #FFFFFF99;
            font-weight: 400;
            width: 1em;
            height: 1em;
            border: none;
            i {
                display: none;
            }
            &.owl-prev {
                font-family: 'riode';
                left: 4%;
                &::before {
                    content: "\e982";
                }
            }
            &.owl-next {
                font-family: 'riode';
                right: 4%;
                &::before {
                    content: "\e983";
                }
            }
            &:hover {
                color: $primary-color !important;
                background-color: transparent !important;
            }
        }
    }
}
.select-menu ul a {
    padding-left: 0;
}
.filter-tabs-wrapper {
    position: relative;
    padding: 3.5rem 5rem 3.2rem;
    max-width: 96rem;
    background-color: #232323;
    border-radius: 5px;
    margin-top: -7.3rem;
    z-index: 1;
    .tab-pane {
        padding: 2.5rem 0 0;
    }
    .toolbox, .tab-pane, .tab-content {
        background-color: inherit;
    }
    .toolbox {
        padding: 0;
        flex-wrap: nowrap;
        margin-left: -2.5px;
        margin-right: -2.5px;
    }
    .nav-tabs {
        border-bottom-color: #363636;
        border-width: 2px;
        .nav-item .nav-link {
           color: #fff;
           padding: .9rem 2.2rem;
           border-width: 4px;
           margin: 0 22.5px -2px;
           letter-spacing: -.35px;
       }
    }
    .btn-filter {
        border-radius: 3px;
        width: 14.4rem;
        padding: 0;
        height: 50px;
        margin-left: 2.5px;
        margin-right: 2.5px;
    }
    .select-menu.toolbox-item:not(.toolbox-show) {
        margin-left: 2.5px;
        margin-right: 2.5px;
    }
    .select-menu-toggle {
        font-size: 1.4rem;
        font-weight: 400;
        width: 17.38rem;
        letter-spacing: -.35px;
        color: #666;
        padding: .95em 3.05em .95em 1.07em;
        border-radius: 3px;
        border: 1px solid #ccc;
        background-color: #fff;
    }
    .select-menu::before {
        color: #666;
        z-index: 1;
    }
    .toolbox-item, .btn-filter {
        margin-bottom: 2rem;
    }
    .input-wrapper {
        max-width: 60rem;
        width: 100%;
        position: relative;
        .form-control {
            font-size: 1.4rem;
            height: 4.9rem;
            padding: 0 4rem 0 2rem;
            border-radius: 3px;
            border: 1px solid #e1e1e1;
            background-color: #fff;
            &::placeholder {
                color: #999;
                font-style: normal;
            }
        }
        .btn-search {
            position: absolute;
            top: 0;
            right: 0;
            width: 4.9rem;
            height: 4.9rem;
            padding: 0;
            background-color: $primary-color;
            border-color: $primary-color;
            color: #fff;
            border-radius: 0 3px 3px 0;
            &:hover {
                background-color: #a6df1a;
                border-color: #a6df1a;
                border-radius: 0 3px 3px 0;
            }
            i {
                font-size: 1.4rem;
                margin-left: 0;
            }
        }
    }
}
// Intro Banners
.banners-group {
    font-size: 1rem;
    position: relative;
    .banner-images-wrapper {
        img {
            border-radius: 4px;
            display: inline-block;
        }
        figure {
            text-align: center;
        }
    }
    .banner-title {
        font-size: 3.6em;
        line-height: 1.2;
    }
    p {
        letter-spacing: -.2px;
        line-height: 1.8;
    }
    .decoration {
        position: absolute;
    }
    .deco-top.deco-1 {
        top: .4%;
        left: 14.45%;
    }
    .deco-top.deco-2 {
        top: -20.2%;
        left: -.8%;
    }
    .deco-bottom.deco-1 {
        bottom: -22.8%;
        right: -9.7%;
    }
    .deco-bottom.deco-2 {
        bottom: -17%;
        right: -3.6%;
    }
    .banner-group2 {
        position: relative;
        .banner {
            position: absolute;
        }
        .banner-right-section, .banner-left-section {
            position: relative;
        }
        .banner-left-section {
            min-height: 42.5rem;
        }
        .banner-content {
            position: absolute;
        }
        .banner-subtitle {
            font-size: 2em;
            letter-spacing: -.5px;
            margin-bottom: .8rem;
        }
        .banner-title {
            font-size: 3em;
        }
    }
    .banner-left-section {
        z-index: 1;
        box-shadow: 0px 0px 35px 0px rgba(0, 0, 0, 0.15);
        .banner-content {
            left: 41.5%;
        }
        .banner-title {
            font-size: 4em;
        }
        .banner-subtitle {
            font-size: 1.8em;
            letter-spacing: 1.8px;
        }
        p {
            color: #afafaf;
            font-size: 1.4rem;
            letter-spacing: -.2px;
        }
    }
    .banner-right-section {
        z-index: 2;
        .banner-content {
            left: 7.6%;
        }
        img {
            min-height: 34rem;
            object-fit: cover;
        }
        .btn-link:hover {
            color: #fff;
        }
    }
}
.category {
    .category-name {
        letter-spacing: -.02em;
        font-weight: 700;
    }
    .category-content {
        background-color: rgba(51,51,51,.9);
    }
    &:hover .category-content {
        background-color: #333;
    }
}
.banners-group1 {
    .banner-title {
        margin-bottom: 1.7rem;
    }
    p {
        line-height: 1.86em;
    }
    .input-wrapper {
        input.form-control {
            background-color: $lighter-color;
            border-color: $lighter-color;
            font-size: 1.3rem;
            padding-left: 1.8rem;
            height: 4.8rem;
            border-radius: 3px;
        }
        .btn {
            font-size: 1.3rem;
            height: 4.8rem;
            padding: 0 1.8rem;
            background-color: #111;
            &:hover {
                background-color: #343434;
            }
            i {
                font-size: 1.6rem;
                margin-left: .8rem;
                margin-bottom: 0px;
            }
        }
    }
    .category-link {
        position: absolute;
        left: 2rem;
        bottom: 3rem;
        color: #fff;
        font-size: 2em;
        font-weight: 700;
        line-height: 1em;
        letter-spacing: -0.5px;
    }
    .banner-left-section {
        .banner-title {
            font-size: 3rem;
        }
        .banner-subtitle {
            margin-bottom: 1.9rem;
            span {
                font-size: 1.4rem;
                letter-spacing: -.35px;
                margin-left: 61%;
            }
        }
        p {
            line-height: 1.8;
        }
    }
    .deco-top.deco-1 {
        top: 44.7%;
        left: 3.55%;
    }
    .deco-top.deco-2 {
        top: 24.6%;
        left: -13.8%;
    }
    .deco-bottom.deco-1 {
        bottom: -24.8%;
        right: -7.3%;
    }
    .deco-bottom.deco-2 {
        bottom: -15.1%;
        right: -3.6%;
    }
    .category {
        cursor: pointer;
        border-radius: 4px;
    }
}
.instagram {
    border-radius: 0;
    img {
        max-height: 19.7rem;
    }
}
.instagram-section .owl-carousel {
    margin-bottom: -9.8rem;
}
// footer
.footer {
    .widget-about {
        p {
            line-height: 2.3em;
        }
        .widget-title {
            margin-bottom: .9rem;
        }
    }
}
.main.border-top {
    border-top: 1px solid #e1e1e1;
}
.shop-page {
    .title {
        font-size: 2.4rem;
    }
}
.sidebar-fixed .filter-actions:first-child, .toolbox.sticky-toolbox:not(.fixed) {
    padding-top: 3px;
}
// Responsive
@include mq(md) {
    .banners-group {
        .banner-desc-wrapper {
            max-width: 41.7%;
            flex: 0 0 41.7%;
        }
        .banner-images-wrapper {
            max-width: 58.3%;
            flex: 0 0 58.3%;
        }
    }
    .banner-group2 {
        display: flex;
        .banner-right-section {
            width: 35.77%;
        }
        .banner-left-section {
            width: 64.23%;
        }
    }
    .banner-right-section .banner {
        min-width: 43rem;
        top: 50%;
        transform: translateY(-50%);
    }
}
@include mq(lg) {
    .banner-right-section .banner {
        min-width: 68rem;
    }
}
@include mq(xl, max) {
    .banners-group .banner-right-section img {
        max-width: 56rem;
    }
}
@include mq(lg, max) {
    .banners-group .banner-right-section img {
        max-width: 43rem;
        width: 100%;
    }
    .filter-tabs-wrapper {
        .toolbox {
            flex-wrap: wrap;
        }
        .select-menu-toggle {
            width: 100%;
        }
        .toolbox-item {
            width: 48%;
        }
    }
}
@include mq(md, max) {
    .banners-group {
        .banner-right-section {
            img {
                max-width: 100%;
            }
            .banner-content {
                left: 2.5rem;
            }
        }
        .banner-group2 .banner {
            position: static
        }
        .banner-left-section .banner-content {
            left: 1rem;
        }
    }
    .banners-group1 .category-link {
        left: 1rem;
        font-size: 1.4em;
    }
    .banner-left-section .banner-subtitle span {
        float: right;
        margin-left: 0;
    }
    .decoration {
        &.deco-1 {
            max-width: 10%;
        }
        &.deco-2 {
            max-width: 20%;
        }
    }
    .footer-middle .widget {
        margin-bottom: 3rem;
    }
    .footer-middle .widget-info {
        margin-bottom: 0;
    }
}
@include mq(sm, max) {
    .filter-tabs-wrapper {
        .toolbox-item {
            width: 98%;
        }
    }
    .filter-tabs-wrapper .nav-tabs .nav-item .nav-link {
        margin-left: 5px;
        margin-right: 5px;
    }
}
@include mq(xs, max) {
    .header-middle .d-icon-phone {
        display: none;
    }
    .intro-slider .banner-content {
        font-size: .9rem;
    }
}