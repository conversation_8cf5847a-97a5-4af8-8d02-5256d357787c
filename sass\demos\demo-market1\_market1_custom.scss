﻿/* Market 1 */
/* Base */
img {
    vertical-align: middle;
    object-fit: cover;
}
/* Header */
.welcome-msg {
    padding-left: .2rem;
}
.header-middle {
    .icon-box {
        margin-right: 1.7rem;
    }
    .divider {
        margin-right: 1.8rem;
    }
}
.header-search.hs-expanded {
    .select-box, input.form-control, .btn-search {
        background-color: transparent;
    }
}
.header-bottom .category-icon {
    padding: 2.8rem 1.62rem 1rem;
    border: none;
    .category-content {
        margin-top: .8rem;
    }
    .category-name {
        font-weight: 400;
        font-size: 1.3rem;
        color: $white-color;
        letter-spacing: 0;
        white-space: nowrap;
    }
}
.category-menu {
    &::-webkit-scrollbar {
        width: .7rem;
        height: .7rem;
    }
    &::-webkit-scrollbar-thumb {
        background: rgba(255,255,255,0.2);
        margin-right: 2px;
        border-radius: 7px;
        cursor: pointer;
    }
    &::-webkit-scrollbar-track {
        margin-bottom: .8rem;
    }
}
/* Intro section */
.intro-section {
    padding-bottom: 3.1rem;
}
.intro-slide {
    &.banner img {
        min-height: 45rem;
    }
    .banner-content {
        left: 7.8%;
    }
    .banner-subtitle {
        font-size: 2.8em;
    }
    .banner-title {
        font-size: 4.8em;
    }
    .banner-price-info {
        font-size: 2.2em;
        span {
            font-size: 1.09em;
            font-weight: 800;
        }
    }    
    .btn  {
        margin-bottom: .3rem;
        font-size: 1.4em;
        i {
            margin-left: .8rem;
        }
    }
}
.intro-slide1 {
    .banner-subtitle {
        margin-bottom: .8rem;
    }
    .banner-title {
        margin-bottom: 1.4rem;
    }
    .banner-price-info {
        margin-bottom: 3.4rem;
        font-size: 2.8em;
        span {
            font-size: 1.2143em;
        }
    }
}
.intro-slide2 {
    .banner-content {
        right: 9.72%;
    }
    .banner-subtitle {
        margin-bottom: .7rem;
    }
    .banner-title {
        margin-bottom: 1.7rem;
    }
    .banner-price-info {
        margin-bottom: .2rem;
    }
}
.intro-slide3 {
    .banner-title {
        margin-bottom: 1.3rem;
    }
    .banner-price-info {
        margin-bottom: .2rem;
    }
}
.banner-radius {
    border-radius: .3rem;
}
.intro-banner {
    img {
        min-height: 21.5rem;
    }
    .banner-content {
        left: 6%;
        margin-top: .3rem;
    }
    .banner-title {
        margin-bottom: .2rem;
        font-size: 2.4rem;
    }
    .product-count {
        font-size: 1.4rem;
    }
    .divider {
        margin: 1.4rem 0 1.8rem;
    }
}
.main .divider {
    display: block;
    width: 3.6rem;
    height: .3rem;
}
.owl-dot-inner .owl-dots {
    bottom: 2.5rem;
    .owl-dot span {
        background-color: $white-color;
        border-color: $white-color;
    }
}
.service-wrapper {
    border: 1px solid $border-color-light;
    border-radius: .4rem;
    .owl-stage-outer {
        margin: 0 .1rem;
    }
    .owl-stage {
        margin: 0 -.1rem;
    }
    .owl-item:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 50%;
        right: 0px;
        transform: translateY(-50%);
        width: 1px;
        height: 37px;
        background: #eee;
    }
    .icon-box-side {
        padding: 3.5rem 0 3rem;
    }
    .icon-box-title {
        margin-bottom: .4rem;
    }
    .icon-box-icon {
        margin: 0 2rem .4rem 0;
    }
    .d-icon-truck {
        font-size: 4.7rem;
    }
    .d-icon-service, .d-icon-secure {
        font-size: 3.9rem;
    }
    .d-icon-money {
        font-size: 3.2rem;
    }
}
/* Product wrapper */
.with-link {
    a {
        font-size: 1.4rem;
    }
    i {
        margin-left: .7rem;
        font-size: 2rem;
    }
}
.product-deals-wrapper {
    .product-single {
        border: 1px solid #e1e1e1;
        border-radius: .4rem;
        padding: 1rem;
        .product-name {
            margin-bottom: .8rem;
            font-size: 2rem;
            line-height: 1.2;
        }
        .product-price {
            color: #333;
            font-size: 2.6rem;
        }
        .ratings-container {
            margin-bottom: 2.1rem;
            font-size: 1.3rem;
        }
        .ratings::before {
            color: $secondary-color;
        }
        .product-form {
            margin-bottom: .7rem;
            line-height: 2.4rem;
            & > label {
                margin-right: .7rem;
                font-size: 1.3rem;
            }
            .btn-cart:disabled {
                background-color: #eee;
                color: #222;
            }
        }
        .product-variations>a:not(.size-guide) {
            width: auto;
            height: 2.6rem;
        }
    }
    .grid-type {
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 4));
        margin: -1rem;
        & > * {
            padding: 1rem;
        }
        .product-single-wrap {
            grid-row-end: span 2;
            grid-column-end: span 2;
        }
        .product-wrap {
            margin-bottom: 0;
            grid-row-end: span 1;
            grid-column-end: span 1;
        }
    }
}
@include mq(xxl) {
    .product-deals-wrapper {
        .col-md-6:first-child {
            max-width: calc( 50% + 1rem );
            flex: 0 0 calc( 50% + 1rem );
        }
        .col-md-6:last-child {
            max-width: calc( 50% - 1rem );
            flex: 0 0 calc( 50% - 1rem );
        }
    }
}
@include mq(md) {
    .home {
        .product-list-sm, 
        .product-list-sm .product-media,
        .product-list-sm figure,
        .product-list-sm img {
            height: 100%;
        }
    }
}
.countdown-container {
    position: absolute;
    bottom: 2rem;
    max-width: 73%;
    padding: 0.7rem 1rem;
    border-radius: .3rem;
    background-color: #444;
}
.home {
    .product-list-sm {
        margin-bottom: 0;
        .product-details {
            max-width: calc( 50% - 1rem );
            flex: 0 0 calc( 50% - 1rem );
        }
        .product-price {
            font-size: 1.6rem;
        }
        .ratings-container {
            margin-bottom: 1rem;
        }
    }
    .old-price {
        font-weight: 400;
    }
}
.count-text {
    font-size: 1.3rem;
    color: $dark-color;
    line-height: 1.86;
}
.rating-reviews {
    font-size: 1.3rem;
}
/* Category ellipse */
.category.category-ellipse {
    display: flex;
    .category-media {
        flex: 0 0 10rem;
        max-width: 10rem;
        padding: 0;
        box-shadow: none;
        transition: transform .3s;
    }
    img {
        transform: none;
    }
    &:hover {
        .category-media {
            transform: scale( 1.15 );
        }
    }
}
/* Category Banner */
.category-wrapper {
    img {
        min-height: 18rem;
    }
    .banner-content {
        left: 6.8%;
    }
    .banner-subtitle {
        margin: 0.5rem 0 0.6rem;
        font-size: 1.4rem;
    }
    .banner-title {
        margin-bottom: 1.8rem;
        font-size: 2.2rem;
    }
    .divider {
        margin-bottom: 1.7rem;
    }
    .category-banner1 {
        .banner-subtitle, .divider {
            opacity: .8;
        }
    }
}
/* Vendor */
.vendor-details .ratings-container {
    font-size: 1.3rem;
}
.vendor-products.grid-type {
    &.gutter-xs {
        margin: -2.5px;
        & > * {
            padding: 2.5px;
        }
    }
    .vendor-product:first-child {
        grid-column-end: span 2;
        grid-row-end: span 2;
    }
    .product-media {
        border-radius: .3rem;
        overflow: hidden;
        height: 100%;
        img {
            height: 100%;
        }
    }
}
/* Product Wrapper */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, calc(100% / 11));
    margin: -1rem;
    & > * {
        padding: 1rem;
    }
    .banner-wrapper {
        grid-column-end: span 3;
        grid-row-end: span 6;
        .banner {
            height: 100%;
            min-height: 30rem;
        }
    }
    .product {
        grid-column-end: span 2;
        grid-row-end: span 3;
    }
    .product-details {
        padding: 1rem 0 0;
    }
    .banner-subtitle {
        margin-bottom: .9rem;
        font-size: 1.6rem;
        opacity: .8;
    }
    .banner-title {
        margin-bottom: 1.7rem;
        font-size: 3rem;
    }
    .banner-price-info {
        margin-bottom: 1.8rem;
        font-size: 1.8rem;
        strong {
            margin-left: .4rem;
            font-size: 2rem;
        }
    }
    .btn-outline {
        padding: 0.92em 2.1em;
        i { 
            margin-left: .7rem;
        }
    }
}
.content-top .banner-content {
    top: 3.7rem;
    left: 8.3%;
}
.content-bottom .banner-content {
    bottom: 3.5rem;
    left: 8.3%;
}
.btn-dark.btn-more:hover {
    color: $primary-color;
}
/* Category grid */
.category-grid {
    .banner-content {
        left: 4rem;
    }
    img {
        min-height: 24rem;
    }
    .banner-subtitle {
        margin-bottom: 1.1rem;
        font-size: 1.8rem;
    }
    .banner-title {
        margin-bottom: 2.8rem;
        font-size: 2.8rem;
        line-height: 1.2;
    }
    .banner-price-info {
        font-size: 1.8rem;
        strong {
            margin-left: .4rem;
            font-size: 2rem;
        }
    }
}
.text-right .banner-content {
    right: 4rem;
}
/* Banner */
.banner-section .banner {
    display: flex;
    padding: 4.9rem 7.2rem 4.6rem;
    align-items: center;
}
.content-left  {
    .banner-title {
        margin-bottom: .4rem;
        font-size: 3em;
    }
    p {
        margin-bottom: 2.9rem;
        font-size: 1.8em;
    }
}
.content-right {
    margin: 0 0 .4rem auto;
    max-width: 36rem;
    .item-subtitle {
        margin-bottom: .4rem;
        font-size: 2rem;
    }
    .item-title {
        margin-bottom: 1.4rem;
        font-size: 4em;
        font-weight: 800;
    }
    .owl-nav {
        .owl-next, .owl-prev {
            border: none;
            font-size: 2.4rem;
            &:not(.disabled) {
                color: $grey-color;
                &:hover {
                    background-color: transparent;
                    color: $primary-color;
                }
            }
        }
        .owl-next {
            right: -.2rem;
        }
        .owl-prev {
            left: -.2rem;
        }
    }
}
/* Newsetter section */
.newsletter-section {
    .banner {
        padding: 2.55% 1% 3.8% 3.74%;
    }
    .banner-title {
        margin-bottom: .8rem;
        font-size: 3rem;
    }
    p {
        max-width: 49rem;
    }
    .input-wrapper-inline {
        max-width: 46.3rem;
        height: 4.9rem;
        .form-control {
            padding-left: 1.5rem;
            padding-right: 1.5rem;
            border-radius: 4px 0 0 4px;
            border: 1px solid #454545;
            border-left: 0;
            height: 49px;
            color: $grey-color;
            font-size: 1.3rem;
            &::placeholder {
                color: inherit;
            }
        }
    }
    .btn-primary {
        padding: 1.02em 1.42em;
        border-radius: 0 .4rem .4rem 0;
    }
}
/* Brand wrapper */
.brand-wrapper .gutter-xs {
    margin: -1px;
    & > * {
        padding: 1px;
    }
    img {
        width: 100%;
    }
}
/* product */
.recent-product {
    .owl-stage-outer {
        padding-bottom: 3rem;
        margin-bottom: -3rem;
    }
    figure {
        transition: box-shadow .3s;
        &:hover {
            box-shadow: 0px 20px 20px -16px rgba(0,0,0,0.5);
        }
    } 
}
/* Footer */
.footer-bottom .social-link {
    line-height: 2.5rem;
}
.logo-footer {
    margin-bottom: .1rem;
}
.widget-newsletter {
    .btn {
        padding: 1em 2.15em;
    }
    .input-wrapper-inline {
        height: 4.6rem;
    }
}
/* Responsive */
@include mq(xl, max) {
    .product-deals-wrapper .grid-type {
        grid-template-columns: repeat(auto-fill, calc(100% / 3));
    }
}
@include mq(lg, max) {
    .service-wrapper .icon-box-icon {
        margin: 0 0 1rem 0;
    }
    .product-deals-wrapper .grid-type {
        grid-template-columns: repeat(auto-fill, calc(100% / 2));
    }
    .footer .widget-newsletter {
        text-align: center;
    }
    .products-grid {
        grid-template-columns: repeat(auto-fill, calc(100% / 4));
        .banner-wrapper {
            grid-column-end: span 2;
            grid-row-end: span 2;
        }
        .product {
            grid-column-end: span 1;
            grid-row-end: span 1;
        }
    }
    .banner-section .banner {
        padding: 5rem 3rem;
    }
}
@include mq(md, max) {
    .banner {
        font-size: .8rem;
    }
    .product-deals-wrapper .grid-type {
        grid-template-columns: repeat(auto-fill, calc(100% / 3));
    }
    .home .product-list-sm {
        display: block;
        .product-media, .product-details {
            flex: 0 0 100%;
            max-width: 100%;
        }
        .product-details {
            padding-top: 2rem;
        }
        .product-media {
            margin: 0;
        }
    }
    .grid figure {
        height: auto;
    }
    .products-grid {
        grid-template-columns: repeat(auto-fill, calc(100% / 3));
    }
    .banner-section .banner {
        display: block;
        background-position: 34%;
        .content-right {
            margin: 4rem 0 0 ;
            max-width: 100%;
        }
    } 
    .newsletter-section .banner {
        padding: 10% 5%;
    }
}
@include mq(sm, max) {
    .market .header-middle {
        .icon-box, .compare, .wishlist {
            display: none;
        }
    }
    .product-deals-wrapper .grid-type {
        grid-template-columns: repeat(auto-fill, calc(100% / 2));
    }
    .home .product-list-sm .product-name a {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .products-grid {
        grid-template-columns: repeat(auto-fill, calc(100% / 2));
    }
}
@include mq(xs, max) {
    .banner {
        font-size: .8rem;
    }
    .banner-section .banner {
        padding-left: 2rem;
        padding-right: 2rem;
    }
    .btn-product-icon.btn-compare {
        display: none;
    }
    .with-link {
        flex-direction: column;
        a {
            margin: 1rem 0 0;
        }
    }
}
/* Shop page */
.breadcrumb-nav .breadcrumb {
    padding: 1.6rem 0;
}
.breadcrumb li:not(:last-child) a {
    color: $body-color;
    opacity: .5;
    transition: opacity .3s;
    &:hover {
        opacity: 1;
    }
    &::after {
        color: $body-color;
    }
}
.breadcrumb li:last-child {
    color: #666;
}
.shop-boxed-banner {
    padding: 4.7rem 8.7% 5.4rem;
    .banner-title {
        max-width: 33.5rem;
        font-size: 4.2em;
        line-height: .96;
    }
    .btn {
        border-color: #222 !important;
        i {
            font-size: 1.9rem;
        }
    }
}
.category-wrap {
    .category-icon {
        padding: 1.8rem .5rem;
        border-color: $border-color-light;
        i {
            margin-bottom: 1rem;
            font-size: 5rem;
        }
        .category-name {
            color: #666;
            font-size: 1.3rem;
            font-weight: 400;
        }
        &:hover {
            border-color: $primary-color;
        }
    }
}
.filter-clean {
    font-size: 1.3rem;
}
.select-item {
    letter-spacing: 0;
    i {
        margin-left: 0;
        padding: .5rem 0rem .7rem 0.8rem;
    }
}
/* Product Page */
.product-navigation {
    padding-top: 1.2rem;
    padding-bottom: 1.3rem;
}
.market1-product, .vendor {
    .product-single .product-details {
        padding-top: 1.6rem;
    }
    .title {
        font-size: 2.4rem;
    }
    .product-list-sm {
        padding: 0 .3rem;
        .product-media {
            flex: 0 0 10rem;
            max-width: 10rem;
            margin: 0 0 0 1rem;
        }
    }
    .widget-products .widget-title {
        margin-bottom: .3rem;
    }
    .owl-nav-top .owl-nav {
        top: -3.8rem;
        right: 0;
    }
}
#product-tab-description figure {
    max-width: 55.9rem;
}