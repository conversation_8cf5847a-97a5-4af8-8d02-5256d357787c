@charset 'UTF-8';
/*
1. config
2. mixins
3. plugins
4. base
5. components
6. header
7. footer
8. pages
9. demo
*/
/* 1. config */
@import '../../config/variables';
//Color 
$primary-color: #519071;
$secondary-color: #35413b;
/* 2. mixins */
@import '../../mixins/breakpoints'; 
@import '../../mixins/core';
@import '../../mixins/buttons';
// Demo Variables
@import 'demo16_config';
/* 3. plugins */
@import '../../components/slider';
@import '../../components/nouislider';
/* 4. base */
@import '../../base/base';
@import '../../base/helper';
@import '../../base/type';
@import '../../base/layout';
@import '../../base/grid';
@import '../../base/spacing';
/* 5, components */
@import '../../components/alerts';
@import '../../components/animation';
@import '../../components/banners';
@import '../../components/blog';
@import '../../components/buttons';
@import '../../components/categories';
@import '../../components/comments';
@import '../../components/accordion';
@import '../../components/font-icons';
@import '../../components/forms';
@import '../../components/icon-boxes';
@import '../../components/icons';
@import '../../components/minipopup';
@import '../../components/overlay';
@import '../../components/page-header';
@import '../../components/pagination';
@import '../../components/popups';
@import '../../components/products';
@import '../../components/product-single';
@import '../../components/sidebar';
@import '../../components/sidebar-shop';
@import '../../components/social-icons';
@import '../../components/tabs';
@import '../../components/tooltip';
@import '../../components/titles';
@import '../../components/widgets';
/* 6. header */
@import '../../base/header/header';
@import '../../base/header/dropdown';
@import '../../base/header/menu';
/* 7. footer */
@import '../../base/footer/footer';
/* 8. Pages */
@import '../../pages/shop';
@import '../../pages/product-single';
/* 8. Demos */
@import 'demo16_custom';