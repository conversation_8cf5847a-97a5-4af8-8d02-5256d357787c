/* -------------------------------------------
    Coming Soon
    - Coming soon countdown
    - Coming soon section
---------------------------------------------- */
// Coming Soon countdown
.countdown-coming {
    text-align: left;
    .countdown-row {
        display: flex;
        justify-content: center;
        line-height: 1;
    }
    .countdown-amount {
        display: block;
        padding: 0 4.2rem;
        font-size: 3rem;
        color: #222;
        letter-spacing: -.025em;
    }
    .countdown-period {
        display: block;
        font-weight: 300;
        font-size: 1.6rem;
        text-align: center;
        text-transform: lowercase;
    }
    .countdown-section:not(:first-child)::before {
        content: ':';
        position: absolute;
        color: #ccc;
        font-size: 3rem;
        line-height: .9;
        transform: translate(-3px);
    }
}
@media (max-width: 479px), (min-width: 768px) and (max-width: 991px) {
    .countdown-coming .countdown-amount {
        padding: 0 2.4rem;
    }
}
// Coming Soon Section
.coming-section {
    .row {
        min-height: 100vh;
        @include only-for-ie() {
            height: 1px;
        }
    }
    h1 {
        font-size: 5rem;
        font-weight: 900;
    }
    p {
        font-weight: 300;
        line-height: 2.15;
        letter-spacing: -.01em;
    }
    form {
        position: relative;
    }
    .form-control {
        font-weight: 300;
        border-color: #dadada;
        padding: 1.25rem 13rem 1.25rem 1.5rem;
    }
    .btn {
        position: absolute;
        right: 0;
        top: 0;
        padding: 1.6rem 1.5rem 1.6rem 2.5rem;
        &:hover {
            background: $primary-color;
            color: #fff;
        }
    }
    .social-link {
        border-width: 1px;
        line-height: 28px;
        margin-right: 5px;
    }
}
@include mq(xs, max) {
    .coming-section {
        padding-bottom: 1px;
    }
}