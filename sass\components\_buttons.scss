﻿/* -------------------------------------------
    Buttons
        - پس زمینه های مختلف
        - دکمه های طرح کلی
        - دکمه های گرادیان
        - دکمه های لینک ساده
        - سایز دکمه ها
        - اشکال و سایه
        - With Icon & Animation
        - On Background
---------------------------------------------- */
// Button
.btn {
    display: inline-block;
    border: 2px solid #e4eaec;
    background-color: #e4eaec;
    color: #222;
    outline: 0;
    padding: 1.22em 2.78em;
    font: {
        weight: 700;
        size: 1.4rem;
        family: $font-family;
    }
    letter-spacing: 0;
    line-height: 1.2;
    text-transform: uppercase;
    text-align: center;
    transition: color .3s, border-color .3s, background-color .3s, box-shadow .3s;
    white-space: nowrap;
    cursor: pointer;
    &:hover,
    &:active,
    &:focus {
        color: #222;
        border-color:   darken( #e4eaec , 7% );
        background-color: darken( #e4eaec , 7% );  
    }
    i {
        display: inline-block;
        vertical-align: middle;
        margin-left: .4rem;
        line-height: 0;
        font-size: 1.9rem;
        &::before {
            margin: 0;
        }
    }
}
.btn-solid {
    background-color: #fff;
    border-color: #fff;
    color: #222;
    &:hover,
    &:active,
    &:focus {
        border-color:   #222;
        background-color: #222;
        color: #fff;
    }
}
// Outline Button
.btn-outline {
    border: 2px solid #e4eaec;
    color: $text-grey-color;
    background-color: transparent;
    &:hover,
    &:active,
    &:focus {
        border-color: #666;
        background-color: #666;
        color: #fff;
    }
    &.btn-outline-light {
        padding: 1.07em 2.21em; 
        border-width: 1px;
    }
    &.btn-bg {
        color: #fff;
    }
}
.btn-gradient {
    position: relative;
    color: #fff;
    border: none;
    overflow: hidden;
    transition: background .25s, color .25s, border .25s;
    &::after {
        content: "";
        position: absolute;
        top: -50%;
        left: 0;
        right: 0;
        bottom: -50%;
        background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, .4) 50%, rgba(255, 255, 255, 0) 80%);
        transform: skewX(-45deg) translateX(-100%);
        transition: transform .8s;
    }
    &:hover::after {
        transform: skewX(-45deg) translateX(100%);
    }
    &.btn-primary {
        background: linear-gradient(135deg, #3b8df1, #5449ff);
    }
    &.btn-secondary {
        background: linear-gradient(135deg, #ffa35f, #ef5454);
    }
    &.btn-alert {
        background: linear-gradient(135deg, #f85283, #b33c9e);
    }
    &.btn-success {
        background: linear-gradient(135deg, #a0ec69, #87cf47);
    }
    &.btn-dark {
        background: linear-gradient(135deg, #666, #222);
    }
}
// Icon Button
.btn-icon-left ,
.btn-icon-right,
.btn-reveal-right ,
.btn-reveal-left {
    // display: inline-flex;
    // align-items: center;
    i {
        line-height: 0;
    }
}
.btn-icon-right {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    i {
        margin-left: .7rem;
    }
}
.btn-icon-left  {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    i {
        margin-right: .7rem;
        margin-left: 0;
    }
}
// Reveal Button
.btn-reveal-right  {
    i {
        opacity: 0;
        margin-left: -1em;
        transform: translateX(.5em);
        margin-right: 0;
        transition: transform .3s, opacity .3s, margin .3s;
    }
    &:hover,
    &:active,
    &:focus {
        i {
            opacity: 1;
            margin-left: 0;
            transform: translateX(0);
            margin-right: .7rem;
        }
    }
}
.btn-reveal-left {
    i {
        opacity: 0;
        margin-right: -1em;
        transform: translateX(-.5em);
        margin-left: 0;
        transition: transform .3s, opacity .3s, margin .3s;
    }
    &:hover,
    &:active,
    &:focus {
        i {
            opacity: 1;
            margin-left: .7rem;
            transform: translateX(0);
            margin-right: 0;
        }
    }
}
// Slide Button
.btn-slide-left  {
    &:hover,
    &:active,
    &:focus {
        i {
            animation: .5s ease slideLeft;
        }
    }
}
.btn-slide-right {
    &:hover,
    &:active,
    &:focus {
        i {
            animation: .5s ease slideRight;
        }
    }
}
.btn-slide-up {
    &:hover,
    &:active,
    &:focus {
        i {
            animation: .5s ease slideUp;
        }
    }
}
.btn-slide-down {
    &:hover,
    &:active,
    &:focus {
        i {
            animation: .5s ease slideDown;
        }
    }
}
.btn-infinite {
    &:hover {
        i {
            animation-iteration-count: infinite;
        }
    }
}
@keyframes slideLeft {
    0% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(-.5em);
    }
    100% {
        transform: translateX(0);
    }
}
@keyframes slideRight {
    0% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(.5em);
    }
    100% {
        transform: translateX(0);
    }
}
@keyframes slideUp {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-.5em);
    }
    100% {
        transform: translateY(0);
    }
}
@keyframes slideDown {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(.5em);
    }
    100% {
        transform: translateY(0);
    }
}
// Shadow Button
.btn-shadow-lg {
    box-shadow: 0 13px 20px -10px rgba(0,0,0,.15);
    &:hover,
    &:active,
    &:focus {
        box-shadow: 0 13px 20px -10px rgba(0,0,0,.2);
    }
}
.btn-shadow {
    box-shadow: 0 15px 20px -15px rgba(0,0,0,.15);
    &:hover,
    &:active,
    &:focus {
        box-shadow: 0 15px 20px -15px rgba(0,0,0,.2);
    }
}
.btn-shadow-sm {
    box-shadow: 0 17px 20px -20px rgba(0,0,0,.15);
    &:hover,
    &:active,
    &:focus {
        box-shadow: 0 17px 20px -20px rgba(0,0,0,.2);
    }
}
// Underline Button
.btn-underline {
    &:hover,
    &:active,
    &:focus {
        &::after {
            transform: scaleX(1);
        }
    }
    &::after {
        display: block;
        margin-left: auto;
        margin-right: auto;
        margin-top: 1px;
        width: 100%;
        border-bottom: 2px solid;
        transform: scaleX(0);
        transition: transform .3s;
        content: '';
    }
    i {
        margin-bottom: 2px;
    }
    &.btn-underline-visible {
        &::after {
            transform: scaleX(1);
        }
    }
    &.btn-underline-width-sm {
        &::after {
            height: 1px;
            border-bottom: 0;
        }
    }
}
.btn-underline.sm::after { width: 46%; }
.btn-underline.lg::after { 
    margin-left: -16.5%;
    margin-right: -16.5%;
    width: 133%;
}
// Link Button 
.btn-link {
    padding: 0;
    color: #222;
    border: 0;
    background-color: transparent;
    &:hover,
    &:active,
    &:focus {
        background-color: transparent;
    }
}
// Other Button Styles
.btn-disabled {
    cursor: not-allowed;
    background-color: #e4eaec !important;
    color: #999 !important;
    border-color: #e4eaec !important;
    box-shadow: none !important;
    &::before,
    &::after {
        content: none !important;
    }
}
.btn {
    &.btn-rounded {
        border-radius: 3px;
        &::before,
        &::after {
            border-radius: 3px;
        }
    }
    &.btn-block {
        display: block;
        padding-left: 0;
        padding-right: 0;
        width: 100%;
    }
    &.btn-ellipse {
        border-radius: 3rem;
    }
    &.btn-lg {
        padding: 1.5em 3.42em;
    }
    &.btn-md {
        padding: 1.07em 2.15em;
        font-size: 1.3rem;
    }
    &.btn-sm {
        padding: 0.92em 1.61em;
        font-size: 1.3rem;
    }
}
.btn.btn-link {
    padding: 0;
    @include only-for-ie() {
        overflow: visible;
    }
}
// 8. Button Color Styles
.btn-primary {
    @include button-variant($primary-color);
}
.btn-secondary {
    @include button-variant($secondary-color);
}
.btn-alert { 
    @include button-variant($alert-color);
}
.btn-success  {
    @include button-variant($success-color);
}
.btn-dark {
    @include button-variant($dark-color);
}
.btn-white {
    color: #222;
    background-color: #fff;
    border-color: #fff;
    &:hover,
    &:active,
    &:focus {
        color: $white-color;
        border-color: $primary-color;
        background-color: $primary-color; 
    }
    &.btn-outline {
        color: #fff;
        background-color: transparent;
        border-color: #fff;
        &:hover,
        &:active,
        &:focus {
            color: $primary-color;
            background-color: #fff;
        }
    }
    &.btn-link {
        color: #fff;
        background-color: transparent;
        &:hover,
        &:active,
        &:focus {
            color: #fff;
            background-color: transparent;
        }
    }
}
//dark-theme 
.dark-theme {
    .btn-white:not(:hover),
    .btn-outline:not(:hover) {
        border-color: #666;
        color: #fff;
    }
    .btn-link:not(.btn-primary) {
        color: #d7d7d7
    }    
    .btn-dark {
        @include button-variant(#050505);
    }
}