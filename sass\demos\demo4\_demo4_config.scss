/* 
Demo 4
*/
@include set(
    (
        header: (
            top: (
                border-bottom: false,
                background: #F2F3F5,
                letter-spacing: 0
            ),
            middle: (
                font-size: 1.4rem,
                padding-top: 3.2rem,
                padding-bottom: 3.2rem,
                logo: (
                    margin-right: 7.7rem
                )
            ),
            help: (
                icon: (
                    margin-top: .1rem
                )
            ),
            contact: (
                icon: (
                    margin-top: .1rem
                )
            ),
            wishlist: (
                icon: (
                    font-size: 2.3rem
                )
            ),
            main-nav: (
                margin-left: .2rem
            ),
            cart: (
                toggle: (
                    padding: .5rem 0 .8rem
                )
            )
        ),
        base: (
            title: (
                font-weight: 700,
                line-height: 2.5,
                margin-bottom: 2rem,
                letter-spacing: -.025em,
                border: (
                    color: #eee,
                    width: 1px
                )
            )
        ),
        menu: (
            ancestor: (
                letter-spacing: -.000 7em
            )
        ),
        // post: (
        //     body: (
        //         padding: 1.9rem 0 2rem
        //     ),
        //     info: (
        //         margin-bottom: .6rem
        //     ),
        //     title: (
        //         margin-bottom: 1.5rem,
        //         font-size: 1.4rem,
        //         font-family: $second-font-family,
        //         font-weight: 400
        //     ),
        //     btn: (
        //         _icon-gap: .5rem
        //     )
        // ),
        post: (
            meta: (
                margin-bottom: .5rem
            ),
            title: (
                margin-bottom: 1.7rem,
                line-height: 1.2
            ),
            btn: (
                _icon-gap: .9rem
            )
        ),
        widget: (
            title: (
                border-bottom: 1px solid $border-color-light
            )
        ),
        footer: (
            background: transparent,
            _link-active-color: $primary-color,
            middle: (
                padding: 7rem 0 2.6rem,
                border-top: 1px solid #e2e2e2,
                border-bottom: 1px solid #e2e2e2,
                widget: (
                    title: (
                        margin-bottom: .8rem,
                        font-size: 1.8rem,
                        letter-spacing: 0.000 5em,
                        color: $dark-color,
                        text-transform: uppercase
                    ),
                    body: (
                        padding: 0,
                        color: #666,
                        letter-spacing: -.0
                        1em
                    )
                )
            ),
            newsletter: (
                desc: (
                    margin: 0.9rem 0 1.8rem,
                    font-size: 1.3rem,
                    line-height: 1.23,
                    letter-spacing: 0em,
                    color: #777
                ),
                form: (
                    max-width: 60rem
                ),
                input: (
                    border: 0,
                    background: $lighter-color,
                    color: #999,
                ),
                btn: (
                    padding: 0 1.5rem,
                )
            ),
            bottom: (
                padding: 3.5rem 0
            ),
            copyright: (
                font-size: 1.3rem,
                letter-spacing: .000 5em,
                color: #777
            ),
            social-link: (
                border: 1px solid $body-color,
                color: $body-color,
                line-height: 2.8rem,
            )
        )
    )
);