﻿/* -------------------------------------------
    Products 
        - Default
        - Classic
        - Slideup
            - SlideUp-Content
            - SlideUp
        - Gallery
        - List
            - List
            - Small
        - Product فیلتر
---------------------------------------------- */
// Variables
@include set-default(
	(
		product: (
            font-family: $font-family,
            font-size: 1.4rem,
            color: $grey-color,
            body: (
                background-color: $bg-secondary-white-color,
                padding-top: 1.4rem,
                padding-bottom: 2rem
            ),
            label: (
                margin-bottom: .5rem,
                padding: .5rem 1.1rem,
                font-family: false,
                font-size: 1.1rem,
                border-radius: 2px,
                font-weight: 600,
                line-height: 1,
                letter-spacing: false,
                color: $white-color,
                text-transform: uppercase,
                new: (
                    color: false,
                    background: $primary-color
                ),
                sale: (
                    color: false,
                    background: $secondary-color
                ),
                top: (
                    color: false,
                    background: $primary-color
                ),
                stock: (
                    color: false,
                    background: $grey-color
                )
            ),
            دسته بندی: (
                margin-bottom: .5rem,
                font-family: false,
                font-size: 1rem,
                font-weight: 400,
                line-height: 1,
                letter-spacing: false,
                text-transform: uppercase,
                color: false,
                white-space: nowrap,
                text-overflow: ellipsis,
                overflow: hidden
            ),
            name: (
                margin-bottom: .3rem,
                font-family: inherit,
                font-size: 1.4rem,
                font-weight: 400,
                line-height: false,
                letter-spacing: -.01em,
                text-transform: false,
                color: $text-grey-color,
                padding-right: false
            ),
            price: (
                display: block,
                align-items: center,
                flex-wrap: wrap,
                margin-bottom: .3rem,
                font-family: false,
                font-size: 1.6rem,
                font-weight: 600,
                line-height: 1.86,
                letter-spacing: false,
                text-transform: false,
                color: $dark-color
            ),
            rating: (
                margin-bottom: 1.2rem,
                font-size: 11px,
                letter-spacing: .2em,
                _star-color: $secondary-color
            ),
            variation: (
                width: 2.6rem,
                height: 2.6rem,
                color: $dark-color,
            transition: .3s,
                _active-border: false,
                _active-box-shadow: 0 0 0 3px #999,
                _active-outline: #fff solid 2px,
                // custom types
                _color-border: 1px solid $border-color,
                _image-border: 0,
            ),
            list-sm: (
                name: (
                    color: $dark-color,
                    padding-left: 0,
                ),
                _image-width: 10rem
            )
        )
    )
);
.equal-height {
    .product {
        height: 100%;
    }
}
// Product Shadow
.product-shadow {
    transition: box-shadow .3s;
    &:hover {
        box-shadow: 0 15px 25px -20px rgba(0,0,0,0.5);
    }
    // .product-details {
    //     padding-left: .5rem;
    //     padding-right: .5rem;
    // }
}
// Product Label Round
.product-label-round {
    .product-label {
        padding: .5rem 1.1rem;
        border-radius: 1rem;
    }
}
.text-center {
    .product-details {
        padding-left: .5rem;
        padding-right: .5rem;
    }
}
// Default
.product-wrap {
    margin-bottom: 2rem;
}
.product {
    position: relative;
    overflow: hidden;
    @include print_css( product );
    transition: .3s;
    &:hover {
        .product-action,
        .product-action-vertical {
            visibility: visible;
            opacity: 1;
        }
    }
}
.product-media:hover {
    img:first-child { opacity: 0 }
    img:last-child { opacity: 1 }
}
// Product Media
.product-media {
    position: relative;
    margin-bottom: 0;
    img {
        display: block;
        width: 100%;
        height: auto;
        transition: all .5s;
    }
    img:last-child {
        position: absolute;
        opacity: 0;
        left: 0;
        right: 0;
        top: 0;
        left: 0;
        object-fit: cover;
    }
    img:first-child {
        position: relative;
        opacity: 1;
    }
    transition: box-shadow .3s;
}
// Product Label
.product-label-group {
    display: flex;
    flex-direction: column;
    position: absolute;
    right: 2rem;
    top: 2rem;
    max-width: 10rem;
}
.product-label {
    display: inline-block;
    text-align: center;
    @include print_css( product, label );
    &.label-new {
        @include print_css( product, label, new );
    }
    &.label-sale {
        @include print_css( product, label, sale );
    }
    &.label-top {
        @include print_css( product, label, top );
    }
    &.label-stock {
        @include print_css( product, label, stock );
    }
}
// Product Icon Button
.btn-product-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3.6rem;
    height: 3.6rem;
    margin-bottom: .5rem;
    border: 1px solid $border-color;
    border-radius: 50%;
    background-color: $bg-secondary-white-color;
    color: $grey-color;
    font: {
        size: 1.6rem;
        weight: 700;
    }
    transition: border-color .3s, color .3s, background-color .3s;
    // &.btn-wishlist i {
    //     margin-top: .2rem;
    // }
    &:hover {
        border-color: $primary-color;
        color: $white-color;
        background-color: $primary-color;
    }
    &:not(.btn-wishlist) i {
        margin-bottom: 2px;
    }
    &.btn-quickview i {
        font-size: inherit;
    }
    &.btn-compare i {
        font-size: 1.8rem;
    }
}
// Product Button
.btn-product {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    padding: 1.14em 0;
    background-color: $primary-color;
    color: $white-color;
    font-family: $font-family;
    font: {
        size: 1.4rem;
        weight: 700;
    }
    line-height: 1.2;
    letter-spacing: -.025em;
    text-transform: uppercase;
    transition: opacity .3s;
    &:hover {
        color: $white-color;
    }
}
// QuickView Button
.btn-quickview {
    opacity: .8;
    transition: opacity .3s;
    &:hover {
        opacity: 1;
    }
    i {    
        //transform: translateX(1em);
        font: {
            size: 1.4rem;
        }
    }
}
// WishList Button
.btn-wishlist {
    line-height: 1;
    &.loading {
        &::before,
        &::after {
            z-index: 1;
        }
    }
}
// Product Action Vertical Container
.product-action-vertical {
    position: absolute;
    top: 15px;
    left: 15px;
    transition: opacity .3s, visibility .3s;
    opacity: 0;
    visibility: hidden;
    .btn-wishlist.loading {
        overflow: hidden;
    }
}
// Product Action Container
.product-action {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    background-color: transparent;
    z-index: 10;
    transition: opacity .3s, visibility .3s;
    opacity: 0;
}
// Product Details
.product-details {
    position: relative;
    @include print_css( product, body );
    > .btn-wishlist {
        position: absolute;
        top: 1.2rem;
        right: 0;
        color: $grey-color;
        z-index: 1;
        font: {
            size: 1.6rem;
        }
        i {
            display: inline-block;
            margin: 0;
            transition: transform .3s;
        }
        &:hover {
            i {
                transform: rotateY(180deg);
            }
            color: $primary-color;
        }
    }
    >:last-child {
        margin-bottom: 0;
    }
    .product-action {
        position: relative;
        opacity: 1;
        visibility: visible;
    }
}
// Product Category
.product-cat {
    @include print_css( product, category );
    a {
       &:hover {
           color: $primary-color;
       }
    }
}
// Product Name
.product-name {
    @include print_css( product, name );
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    a {
        // transition: color .3s;
        &:hover {
            color: $primary-color;
        }
    }
}
// Product Price
.product-price {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    @include print_css( product, price );
    .old-price {
        color: #aaa;
    }
    .new-price {
        margin-right: 1rem;
        text-decoration: none;
    }
}
.dark-theme {
    .old-price {
        color: #666;
    }
    .product.product-with-qty {
        .quantity ,
        button{
            color: #999;
        }
    } 
    .rating-reviews {
        color: #999;
    }    
    .ratings-full {
        &::before {
            color: #666;
        }
    }
}
// Product Rating
.ratings-container {
    display: flex;
    align-items: center;
    margin-left: 1px;
    line-height: 1.1;
    @include print_css( product, rating );
}
.ratings-full {
    cursor: pointer;
}
.ratings-full,
.ratings {
    position: relative;
    font-family: 'riode';
}
.ratings-full {
    & + .rating-reviews {
        margin-left: 1rem;
    }
    &::before {
        content: "\e955 " "\e955 " "\e955 " "\e955 " "\e955";
        color: rgba($black-color , 0.16);
        font-family: 'riode';
    }
    &:hover {
        .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
    }
}
.ratings {
    position: absolute;
    top: 0;
    left: 0;
    white-space: nowrap;
    overflow: hidden;
    &::before {
        content: "\e955 " "\e955 " "\e955 " "\e955 " "\e955";
        @include css( color, product, rating, _star-color );
        font-family: 'riode';
    }
}
.rating-reviews {
    margin: 2px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 1.1rem;
    color: #aaa;
    letter-spacing: 0;
    text-transform: capitalize;
    &:hover {
        color: $primary-color;
    }
}
// Product Variables
.product-variations {
    display: flex;
    // issue element-products
    .ratings-container + &{ 
        margin-top: -5px;
    }
    > a:not(.size-guide) {
        display: inline-block;
        position: relative;
		margin: 3px 5px 3px 0;
        padding: 0 5px;
        border-radius: 2px;
		background-color: transparent;
		background-size: cover;
		font-size: 1.3rem;
		font-family: inherit;
		text-align: center; // issue: if use only button, no need
		vertical-align: middle;
		cursor: pointer;
		@include print_css( product, variation );
        @include css(line-height, product-single, variation, height);
		&:last-child {
			margin-right: 0;
        }
        border: 1px solid $border-color;
    }
    .color:not(.active) {
        border: none;
    }
	> a.active,
	> a:not(.size-guide):hover {
		border: 1px solid;
		box-shadow: inset 0 0 0 2px #fff;
	}
}
// Product Details
.product-details,
.product-hide-details {
    .product-action {
        position: relative;
        opacity: 1;
        .btn-wishlist {
            position: static;
        }
    }
    .btn-cart {
        display: block;
        max-width: 14rem;
        line-height: 2.9;
        padding: 0 .6em;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: $dark-color;
        background-color: transparent;
        border: 2px solid #ccc;
        margin-right: 5px;
        border-radius: .3rem;
        i {
            margin-right: .8rem;
            font-size: 1.8rem;
        }
    }
    .btn-cart {
        flex: 1;
        height: 4.2rem;
        font-size: 1.3rem;
        transition: color .3s, background-color .3s, border-color .3s;
        z-index: 1;
    }
    .btn-quickview {
        margin: 0 1.6rem 0 0 ;
    }
    .btn-product-icon {
        width: auto;
        border: 0;
        background-color: transparent;
        color: $dark-color;
        transition: color .3s, background-color .3s, visibility .3s;
        i {
           font-size: 1.8rem;
        }
        &:hover {
            color: $primary-color;
        }
    }
}
.product:hover {
    .product-action .btn-cart {
        color: $white-color;
        background-color: #444;
        border-color: #444;
    }
}
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .product-hide-details,
    .product-details {
        .btn-cart {
            flex: 0 0 12.3rem;
        }
    }
    @include mq( sm,  max ) {
        .product-hide-details,
        .product-details {
            .btn-cart {
                flex: 0 0 9.5rem;
            }
        }
    } 
}
// Product Classic
.product-classic {
    .btn-quickview {
        opacity: 1;
        i:before {
            content: '\e98c';
        }
    }
    .btn-wishlist {
        margin: 0 1.6rem 0 0 ;
    }
    .btn-wishlist,
    .btn-quickview {
        transform: translateX(-200%);
        opacity: 0;
        visibility: hidden;
        transition: transform .3s, opacity .3s, visibility .3s, color .3s, background-color .3s, border-color .3s;
    }
    .btn-quickview {
        transition: transform .5s, opacity .5s, visibility .5s, color .3s, background-color .3s, border-color .3s;
    }
    &:hover {
        .btn-wishlist,
        .btn-quickview {
            transform: translateX(0);
            opacity: 1;
            visibility: visible;
        }
    }
    .product-details {
        padding-left: 0;
    }
    .ratings-container {
        margin-bottom: 1.5rem;
    }
}
// Slideup Content
.product-slideup-content {
    overflow: hidden;
    .product-hide-details {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        transition: transform .3s;
        background-color: $bg-secondary-white-color;
    }
    .product-details {
        padding-bottom: 1px;
        background-color: $bg-secondary-white-color;
        transition: transform .3s;
    }
    .btn-wishlist,
    .btn-quickview {
        opacity: 0;
        visibility: hidden;
        transition: transform .4s, opacity .3s, visibility .3s, color .3s, background-color .3s;
    }
    .btn-quickview {
        transform: translateX(-200%);
    }
    .btn-wishlist {
        transform: translateX(200%);
    }
    &:hover {
        .btn-wishlist,
        .btn-quickview {
            transform: translateX(0);
            opacity: 1;
            visibility: visible;
        }
    }
}
// SlideUp
.product-slideup {
    .product-details {
        padding-bottom: 0;
        background-color: $bg-secondary-white-color;
        transition: transform .3s;
    }
    .ratings-container {
        margin-bottom: 0;
    }
    .btn-wishlist,
    .btn-quickview {
        opacity: 0;
        visibility: hidden;
        transition: transform .4s, opacity .3s, visibility .3s, color .3s, background-color .3s;
    }
    .btn-quickview {
        transform: translateX(-200%);
    }
    .btn-wishlist {
        transform: translateX(200%);
    }
    .product-action {
        display: flex;
        position: absolute;
        padding-top: 1.5rem;
        top: 100%;
        bottom: auto;
        visibility: hidden;
        opacity: 0;
    }
    &:hover {
        transform: translateY(-58px);
        .btn-wishlist,
        .btn-quickview {
            transform: translateX(0);
            opacity: 1;
            visibility: visible;
        }
        .product-action {
            visibility: visible;
            opacity: 1;
        }
    }
}
.product-slideup, .product-slideup-content {
    .btn-cart {
        margin-right: 0;
    }
    .btn-wishlist {
        position: static;
        margin: 0 1.6rem 0 0;
    }
}
//Split Line
.split-line {
    overflow: hidden;
}
// Gallery
.product-image-gap {
    padding: .9rem;
    border: 1px solid $border-color;
    background-color: $bg-secondary-white-color;
    .product-details {
        padding: 1.8rem 1rem 1rem;
    }
    .ratings-container {
        margin-bottom: 0;
    }
    &:hover {
        box-shadow: 0 5px 30px rgba(0,0,0,0.05);
    }
}
@include mq(xs, max) {
    .product-image-gap .product-details {
        padding: 1.8rem 0 1rem;
    }
}
// List 
.product-list {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    .product-media {
        flex: 0 0 28rem;
        max-width: 28rem;
        margin: 0 2rem 0 0;
    }
    .product-details {
        padding: 0;
        flex: 1;
    }
    .product-label {
        font-size: 1.1rem;
    }
    .btn-quickview {
        opacity: 1;
    }
    .btn-wishlist {
        margin: 0 0 0 .8rem;
    }
    .btn-wishlist,
    .btn-quickview {
        transform: translateX(-200%);
        opacity: 0;
        visibility: hidden;
        transition: transform .3s, opacity .3s, visibility .3s, color .3s, background-color .3s;
    }
    .btn-quickview {
        transition: transform .5s, opacity .5s, visibility .5s, color .3s, background-color .3s;
    }
    .ratings-container {
        margin-bottom: .8rem; 
    }
    .product-name {
        font: {
            size: 1.8rem;
            weight: 600;   
        }
        letter-spacing: -.025em;
        color: $dark-color;
    }
    .product-price {
        font: {
            size: 1.8rem;
            weight: 600;  
        }
    }
    .product-short-desc {
        @include text-block(3);
        margin-bottom: 2rem;
        text-transform: none;
        font-size: 1.4rem;
        line-height: 1.78;
        color: $body-color;
    }
    &:hover {
        .btn-quickview, .btn-wishlist {
            transform: translateX(0);
            opacity: 1;
            visibility: visible;
        }
    }
}
// List 
.product-list-sm {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    .product-media {
        flex: 0 0 #{get(product, list-sm, _image-width)};
        @include css(max-width, product, list-sm, _image-width);
        margin: 0 0 0 1rem;
    }
    .product-details {
        flex: 0 0 calc(100% - #{get(product, list-sm, _image-width)});
        max-width: calc(100% - #{get(product, list-sm, _image-width)});
        padding: 0;
    }
    .product-name {
        @include text-block();
        margin-bottom: .7rem;
        white-space: normal;
        @include print_css( product, list-sm, name);
        &:hover {
            color: $primary-color;
        }
    }
    .ratings-container {
        margin: 0;
    }
    .product-price {
        font-size: 1.4rem;
    }
    & + .product-list {
        margin-top: 2rem;
    }
    .btn-cart {
        margin-top: 2rem;
        height: 3.3rem;
        max-width: 11.7rem;
        border: 1px solid #d7d7d7;
        color: $secondary-color;
        background: transparent;
        font-size: 1.3rem;
        &:hover {
            border-color: $secondary-color;
            background: $secondary-color;
        }
    }
}
// Other Styles
.product {
    &.text-center {
        .product-cat {
            padding: 0;
        }
        .product-price,
        .product-variations,
        .ratings-container,
        .product-action {
            justify-content: center;
        }
    }
    &.shadow-media:hover {
        .product-media {
            box-shadow: 0px 20px 20px -16px rgba(0,0,0,0.5);
        }
    }
    &.cart-full, &.product-with-qty .product-details {
        .product-price {
            margin-bottom: 1rem;
        }
        .btn-cart {
            padding: 0;
            border-color: $border-color-secondary-light;
            &:hover, &:focus, &:active {
                background-color: $primary-color;
                border-color: $primary-color;
                color: #fff;
            }
        }
    }
    &.cart-full .btn-cart {
        max-width: none;
        width: 100%;
    }
    &.product-with-qty {
        .product-details {
            padding-left: 0;
            padding-right: 0;
        }
        .btn-cart {
            max-width:  12.3rem;
            margin-right: 0;
            i {
                display: none;
            }
        }
        .product-quantity {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 2rem;
        }
        button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            color: $border-color-dark;
            font-size: 1rem;
            width: 24px;
            height: 24px;
            border-radius: .3rem;
            border: none;
            background: $lighter-color;
            cursor: pointer;
            transition: background-color .3s, color .3s;
            &:hover {
                background-color: $primary-color;
                color: $white-color;
            }
        }
        .quantity {
            min-height: auto;
            width: 3.2rem;
            padding: 0;
            font-weight: 700;
            font-size: 1.3rem;
            border: none;
            text-align: center;
            color: $dark-color;
        }
    }
    &.product-variable:not(.cart-full) .btn-cart::after {
        display: none;
        font-family: 'riode';
        content: '\e97f';
        font-size: 1.8rem;
        font-weight: 400;
    }
    &:hover {
        &.product-with-qty .btn-cart {
            background-color: transparent;
            color: $dark-color;
        }
    }
}
@include mq(sm, max) {
    .product-list {
        display: block;
        .product-media {
            margin: 0;
            max-width: 100%;
        }
        .product-details {
            padding: 1.8rem 0 2rem;
        }
    }
    .product-action-vertical {
        top: 10px;
        left: 10px;
    }
}
@include mq(xs, max) {
    .product-details, .product-hide-details {
        .btn-cart {
            max-width: 4.2rem;
            display: flex;
            span {
                display: none;
            }
            i {
                margin-right: 0;
            }
        }
    }
    .product.product-with-qty {
        .product-details .btn-cart {
            max-width: 4.2rem;
            padding: .78em 0;
        }
        .btn-cart i {
            display: block;
        }
        .product-quantity {
            margin-right: 1rem;
        }
    }
    .cart-full .product-details .btn-cart span {
        display: inline;
    }
    .cart-full .btn-cart i { 
        display: none;
    }
    .product-label-group {
        right: 1.2rem;
        top: 1.2rem;
    }
    .btn-quickview {
        font-size: 1.1rem;
    }
    .btn-product-icon {
        width: 3.2rem;
        height: 3.2rem;
        font-size: 1.3rem;
    }
    .product.product-variable:not(.cart-full) .btn-cart::after {
        display: block;
    }
    .product.product-variable:not(.cart-full) .btn-cart i {
        display: none;
    }
}
// Product فیلتر
.product-filters {
    margin-right: 1.5rem;
}