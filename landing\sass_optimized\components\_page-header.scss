/* -------------------------------------------
    Page Header
        - Page Subtitle
        - Page Title
        - Breadcrumb
---------------------------------------------- */
$page-header-background: #f2f3f5 !default;
// Page Subtitle (new)
.page-subtitle {
    margin-bottom: 0;
    text-transform: uppercase;
    font-size: 2rem;
    line-height: 1.8;
}
// Page title
.page-title {
    margin-bottom: 0;
    text-transform: uppercase;
    font-size: 4rem;
    line-height: 1.125;
    letter-spacing: -.025em;
    color: #222;
    & + .breadcrumb {
        margin-top: .8rem;
    }
}
// Breadcrumb
.breadcrumb-nav {
    border-bottom: 1px solid #ebebeb;
    .breadcrumb {
        padding: 1.4rem .5rem;
        li:not(:last-child)::after {
            margin-left: 1.2rem;
        }
    }
}
.breadcrumb {
    display: flex;
    flex-wrap: wrap;
    color: #999;
    font-family: 'Open Sans', $alt-font-family;
    font-size: 1.4rem;
    a {
        color: inherit;
        &:hover {
            color: #222;
        }
    }
    li:not(:last-child){
        padding-right: .8rem;
        &::after {
            content: '\f105';
            position: relative;
            margin-left: .7rem;
            font-size: 1.1rem;
            font-weight: 600;
            font-family: 'Font Awesome 5 Free';
        }
    }
    li:last-child {
        color: #222;
    }
}
.breadcrumb-sm {
    padding: 1.1rem 0;
    color: inherit;
    li:last-child {
        color: inherit;
    }
}
// Page Header
.page-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 250px;
    background-color: $page-header-background;
    background-position: center;
    background-size: cover;
    text-align: center;
    color: #222;
    .breadcrumb {
        justify-content: center;
    }
}
// Page Header Dark
.bg-dark {
    > .page-subtitle,
    > .page-title,
    > .breadcrumb li,
    > .breadcrumb a {
        color: #fff;
    }
    > .breadcrumb li:not(:last-child) {
        opacity: .5;
        transition: opacity .3s;
    }
    > .breadcrumb li:hover {
        opacity: 1;
    }
}
@include mq( md, max ) {
    .page-title {
        font-size: 3.6rem;
    }
} 