/*****************************
    product grid
*****************************/
.element-product-grid {
    .grid-type {
        .product-details {
            padding: 12px 5px;
            background: transparent;
        }
        .product { 
            margin-bottom: 0;
            background: #f7f8fc;
        }
        .grid-height-2 .product{
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        @include mq('lg') {
            .product {
                .product-media {
                    padding: 0 13.5%;
                }
            }
        }
    }
    //grid type
    .grid-type {
        display: grid;
        grid-template-columns: repeat(auto-fill,calc(100% / 4));
        .grid-item {
            grid-row-end: span 1;
            grid-column-end: span 1;
            padding: .5rem;
            &.grid-height-2 {
                grid-row-end: span 2;
                grid-column-end: span 2;
            }
        }
        @include mq(lg, max) {
            .grid-type {
                grid-template-columns: repeat(auto-fill,calc(100% / 2));
            }
            .grid-height-2 {
                order: -1;
            }
        }
    }
    .product-list-sm {
        display: flex;
        align-items: center;
        margin-bottom: 2rem;
        .product-media {
            margin: 0;
            flex: 0 0 41.7%;
            max-width: 41.7%;
        }
        .product-details { padding-right: .5rem; }
        .product-price {
            font-size: 1.6rem;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}
//grid-type-other
.grid-type-other {
    display: grid;
    grid-template-columns: repeat(auto-fill,calc(100% / 4));
    grid-template-rows: repeat(auto-fill,calc(100% / 3));
    margin: -10px;
    padding: 10px;
    .height-x3 { 
        grid-row-end: span 3;
        grid-column-end: span 1;
    }
    .product,
    .product-media > a,
    .product-media img { 
        height: 100%; 
    }
    .product {
        border: 1px solid $border-color-secondary-light;
        transition: border-color .3s;
        &:hover { 
            border-color: $primary-color; 
        }
    }
    @include mq(xl, max) {
        grid-template-columns: repeat(auto-fill,calc(100% / 3));
        //grid-template-rows: repeat(auto-fill,calc(100% / 3));
    }
    @include mq('800px', max) {
        grid-template-columns: repeat(auto-fill,calc(100% / 2));
        grid-template-rows: repeat(auto-fill,calc(100% / 5));
    }
    @include mq(sm, max) {
        grid-template-columns: repeat(auto-fill,calc(100% / 1));
    }
}
