﻿/* -------------------------------------------
    But<PERSON>
        - <PERSON><PERSON>
            - De<PERSON>ult
            - Solid
        - Outline Button
        - Icon <PERSON>ton
            - <PERSON>con <PERSON>ton
            - <PERSON>eal <PERSON>
            - <PERSON><PERSON>
        - <PERSON>
        - Underline Button
        - <PERSON>
        - Other <PERSON><PERSON> Styles
        - Button Color Styles
---------------------------------------------- */
@include set-default(
    (
        button: (
            rounded: (
                border-radius: 2px
            ),
            large: (
                padding: 1.3em 2.54em
            )
        )
    )
);
// Button
.btn {
    display: inline-block;
    border: 2px solid #e4eaec;
    background-color: #e4eaec;
    color: #222;
    outline: 0;
    border-radius: 0;
    padding: 1em 2em;
    font: {
        weight: 700;
        size: 1.4rem;
        family: $font-family;
    }
    
    line-height: 1.2;
    text-transform: uppercase;
    text-align: center;
    transition: color .3s, border-color .3s, background-color .3s, box-shadow .3s;
    white-space: nowrap;
    cursor: pointer;
    &:hover,
    &:active,
    &:focus {
        color: #222;
        border-color:   darken( #e4eaec , 7% );
        background-color: darken( #e4eaec , 7% );  
    }
    i {
        display: inline-block;
        margin-left: .4rem;
        line-height: 0;
        &::before {
            margin: 0;
        }
    }
}
.btn-solid {
    background-color: #fff;
    border-color: #fff;
    color: #222;
    &:hover,
    &:active,
    &:focus {
        border-color:   #222;
        background-color: #222;
        color: #fff;
    }
}
.btn {
    &.btn-rounded {
        @include css(border-radius, button, rounded, border-radius);
        &::before,
        &::after {
            @include css(border-radius, button, rounded, border-radius);
        }
    }
    &.btn-block {
        display: block;
        padding-left: 0;
        padding-right: 0;
        width: 100%;
    }
    &.btn-lg {
        @include css(padding, button, large, padding);
    }
}
// Link Button 
.btn-link {
    padding: 0;
    color: #222;
    border: 0;
    background-color: transparent;
    &:hover,
    &:active,
    &:focus {
        background-color: transparent;
    }
}
// Underline Button
.btn-underline {
    &:hover,
    &:active,
    &:focus {
        &::after {
            transform: scaleX(1);
        }
    }
    &::after {
        display: block;
        margin-left: auto;
        margin-right: auto;
        margin-top: 1px;
        width: 100%;
        border-bottom: 2px solid;
        transform: scaleX(0);
        transition: transform .3s;
        content: '';
    }
    i {
        margin-bottom: 2px;
    }
    &.btn-underline-visible {
        &::after {
            transform: scaleX(1);
        }
    }
    &.btn-underline-width-sm {
        &::after {
            height: 1px;
            border-bottom: 0;
        }
    }
}
// 8. Button Color Styles
.btn-dark {
    @include button-variant($dark-color);
}