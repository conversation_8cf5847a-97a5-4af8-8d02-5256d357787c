﻿"use strict";var $=jQuery.noConflict();$.extend($.easing,{def:"easeOutQuad",swing:function(t,e,i,o,n){return $.easing[$.easing.def](t,e,i,o,n)},easeOutQuad:function(t,e,i,o,n){return-o*(e/=n)*(e-2)+i},easeOutQuint:function(t,e,i,o,n){return o*((e=e/n-1)*e*e*e*e+1)+i}}),window.Riode={},function(){var t,e,i,o;Riode.$window=$(window),Riode.$body=$(document.body),Riode.status="",Riode.minDesktopWidth=992,Riode.isIE=navigator.userAgent.indexOf("Trident")>=0,Riode.isEdge=navigator.userAgent.indexOf("Edge")>=0,Riode.isMobile=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),Riode.defaults={animation:{name:"fadeIn",duration:"1.2s",delay:".2s"},isotope:{itemsSelector:".grid-item",layoutMode:"masonry",percentPosition:!0,masonry:{columnWidth:".grid-space"}},slider:{responsiveClass:!0,navText:['<i class="d-icon-angle-left ">','<i class="d-icon-angle-right">'],checkVisible:!1,items:1,smartSpeed:Riode.isEdge?200:500,autoplaySpeed:Riode.isEdge?200:1e3,autoplayTimeout:1e4},popup:{removalDelay:350,callbacks:{open:function(){$("html").css("overflow-y","hidden"),$("body").css("overflow-x","visible"),$(".mfp-wrap").css("overflow","hidden auto"),$(".sticky-header.fixed").css("padding-right",window.innerWidth-document.body.clientWidth)},close:function(){$("html").css("overflow-y",""),$("body").css("overflow-x","hidden"),$(".mfp-wrap").css("overflow",""),$(".sticky-header.fixed").css("padding-right","")}}},popupPresets:{video:{type:"iframe",mainClass:"mfp-fade",preloader:!1,closeBtnInside:!1}},sliderPresets:{"intro-slider":{animateIn:"fadeIn",animateOut:"fadeOut"},"product-single-carousel":{dots:!1,nav:!0},"product-gallery-carousel":{dots:!1,nav:!0,margin:20,items:1,responsive:{576:{items:2},768:{items:3}}},"rotate-slider":{dots:!1,nav:!0,margin:0,items:1,animateIn:"",animateOut:""}},sliderThumbs:{margin:0,items:4,dots:!1,nav:!0,navText:['<i class="fas fa-chevron-left ">','<i class="fas fa-chevron-right">']},stickyContent:{minWidth:Riode.minDesktopWidth,maxWidth:2e4,top:300,hide:!1,max_index:1060,scrollMode:!1},stickyHeader:{activeScreenWidth:768}},Riode.$=function(t){return t instanceof jQuery?t:$(t)},Riode.call=function(t,e){setTimeout(t,e)},Riode.byId=function(t){return document.getElementById(t)},Riode.byTag=function(t,e){return e?e.getElementsByTagName(t):document.getElementsByTagName(t)},Riode.byClass=function(t,e){return e?e.getElementsByClassName(t):document.getElementsByClassName(t)},Riode.setCookie=function(t,e,i){var o=new Date;o.setTime(o.getTime()+24*i*60*60*1e3),document.cookie=t+"="+e+";expires="+o.toUTCString()+";path=/"},Riode.getCookie=function(t){for(var e=t+"=",i=document.cookie.split(";"),o=0;o<i.length;++o){for(var n=i[o];" "==n.charAt(0);)n=n.substring(1);if(0==n.indexOf(e))return n.substring(e.length,n.length)}return""},Riode.parseOptions=function(t){return"string"==typeof t?JSON.parse(t.replace(/'/g,'"').replace(";","")):{}},Riode.parseTemplate=function(t,e){return t.replace(/\{\{(\w+)\}\}/g,(function(){return e[arguments[1]]}))},Riode.requestTimeout=function(t,e){var i=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame;if(!i)return setTimeout(t,e);var o,n=new Object;return n.val=i((function s(a){o||(o=a),a-o>=e?t():n.val=i(s)})),n},Riode.isOnScreen=function(t,e,i){var o=window.pageXOffset,n=window.pageYOffset,s=t.getBoundingClientRect(),a=s.left+o,d=s.top+n,r=void 0===e?0:e,l=void 0===i?0:i;return d+s.height+l>=n&&d<=n+window.innerHeight+l&&a+s.width+r>=o&&a<=o+window.innerWidth+r},Riode.appear=(e=[],i=!1,o=function(){for(var i=e.length;i--;)t=e[i],Riode.isOnScreen(t.el,t.options.accX,t.options.accY)&&("function"==typeof $(t.el).data("appear-callback")&&$(t.el).data("appear-callback").call(t.el,t.data),t.fn&&t.fn.call(t.el,t.data),e.splice(i,1))},window.addEventListener("scroll",o,{passive:!0}),window.addEventListener("resize",o,{passive:!0}),$(window).on("appear.check",o),function(t,n,s){var a={data:void 0,accX:0,accY:0};s&&(s.data&&(a.data=s.data),s.accX&&(a.accX=s.accX),s.accY&&(a.accY=s.accY)),e.push({el:t,fn:n,options:a}),i||(i=Riode.requestTimeout(o,100))});var n=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)};function s(t,e){this.options=$.extend({speed:1,from:"100%",to:"70.5%",styleKey:"width",animation:!1},e,Riode.parseOptions(t.getAttribute("data-parallax-options"))),this.el=t,this.flag=!0;var i=this,o=this.update.bind(this);i.update(),window.addEventListener("resize",o,{passive:!0}),window.addEventListener("scroll",o,{passive:!0})}function a(t,e){this.el=t,this.$el=$(t),this.options=$.extend({layersCount:4,duration:800,delay:70,delayMax:180,fill:"",fillMode:"",gradient:"",color:[46,102,232]},e),this.init()}function d(t,e){var i=this.update.bind(this);this.el=t,this.options=$.extend({friction:.03},e),this.x2=this.y2=this.x=this.y=0,$(window).on("mousemove click",this.moveTo.bind(this)),window.addEventListener("resize",i,{passive:!0}),window.addEventListener("scroll",i,{passive:!0}),this.update()}s.prototype.update=function(){var t=this,e=(window.pageYOffset,window.innerHeight),i=t.el.getBoundingClientRect(),o=2*(e-i.top)/(e+i.height);t.options.animation?(.4<o&&t.flag&&(t.el.classList.add("animated"),t.flag=!1),.4>o&&!t.flag&&(t.el.classList.remove("animated"),t.flag=!0)):$(t.el).css(t.options.styleKey,"calc("+t.options.from+" + ("+t.options.to+" - "+t.options.from+") * "+(o>1?1:o)+")")},Riode.parallaxBg=function(t,e){Riode.$(t).each((function(){new s(this,e)}))},a.prototype.init=function(){this.isAnimating=!1,this.status="",this.delays=[],this.$el.on("mouseenter touchstart",this.start.bind(this)).on("mouseleave touchend",this.finish.bind(this)),this.path=this.el.getElementsByClassName("overlay-path")},a.prototype.start=function(){if(""==this.status){var t,e="";for(e+='<svg class="shape-overlays" viewBox="0 0 100 100" preserveAspectRatio="none"',this.options.fill&&(e+=' fill="'+this.options.fill+'"'),e+=">"+this.options.gradient,t=0;t<this.options.layersCount;++t)e+='<path opacity="'+(t+1)/this.options.layersCount+'" class="overlay-path" />',this.delays[t]=(Math.sin(2*Math.PI*(Math.random()+t/this.options.layersCount))+1)/2*this.options.delayMax;this.$el.append(e+="</svg>"),this.status="opening",this.timeStart=Date.now(),this.update()}},a.prototype.finish=function(){"opening"==this.status?this.status="opening-close":"opened"==this.status&&("onlyshape"==this.options.fillMode?(this.$el.find(".shape-overlays").remove(),this.status=""):(this.status="closing",this.timeStart=Date.now(),this.update()))},a.prototype.update=function(){if(Date.now()<this.timeStart+this.options.duration+this.options.delay*(this.options.layersCount-1)+this.options.delayMax){var t=this;n((function(){t.draw()}))}else"onlyshape"==this.options.fillMode?(this.$el.find(".shape-overlays").remove(),this.status=""):"opening"==this.status?this.status="opened":"opening-close"==this.status?(this.status="closing",this.timeStart=Date.now(),this.update()):"closing"==this.status&&(this.$el.find(".shape-overlays").remove(),this.status=""),this.isAnimating=!1},a.prototype.draw=function(){for(var t=0;t<this.options.layersCount;++t)this.path[t].setAttribute("d",this.updatePath(Date.now()-this.timeStart-this.options.delay*(this.status.startsWith("opening")?t:this.options.layersCount-t-1)));this.update()},a.prototype.updatePath=function(t){for(var e,i,o,n=[],s="",a=0;a<this.options.layersCount;++a)n[a]=100*(.5>(o=Math.min(Math.max(t-this.delays[a],0)/this.options.duration,1))?4*o*o*o:.5*Math.pow(2*o-2,3)+1);s+=this.status.startsWith("opening")?"M 0 0 V "+n[0].toFixed(2)+" ":"M 0 "+n[0].toFixed(2)+" ";for(a=0;a<this.options.layersCount-1;++a)s+="C "+(i=(e=(a+1)/(this.options.layersCount-1)*100)-1/(this.options.layersCount-1)*100/2).toFixed(2)+" "+n[a].toFixed(2)+" "+i.toFixed(2)+" "+n[a+1].toFixed(2)+" "+e.toFixed(2)+" "+n[a+1].toFixed(2)+" ";return s+=this.status.startsWith("opening")?"V 0 H 0":"V 100 H 0"},Riode.shapeOverlay=function(t,e){Riode.$(t).each((function(){new a(this,e)}))},d.prototype.update=function(){var t=this;Riode.isOnScreen(this.el)&&n((function(){t.move()}))},d.prototype.moveTo=function(t){this.x2=-.1*t.clientX,this.y2=-.1*t.clientY},d.prototype.move=function(){this.x+=(this.x2-this.x)*this.options.friction,this.y+=(this.y2-this.y)*this.options.friction,this.el.style["background-position"]=parseInt(this.x)+"px "+parseInt(this.y)+"px",this.update()},Riode.floatBackground=function(t,e){Riode.$(t).each((function(){new d(this,e)}))},Riode.scrollTo=function(t,e){var i=0,o=void 0===e?600:e;i="number"==typeof t?t:Riode.$(t).offset().top,$("html,body").stop().animate({scrollTop:i},o)},Riode.floatScrollDefaults={startPos:"top",top:0,speed:.1,horizontal:!1,isInsideSVG:!0,transition:!1,transitionDelay:0,transitionDuration:500},Riode.FloatScrollElement={initScrollFloatElement:function(t,e){var i,o=this,n=$(window);o.flag=!0,e.style&&t.attr("style",e.style),n.width()>767&&("none"==e.startPos?(i="",t.css({top:e.top})):"top"==e.startPos?(t.css({top:0}),i=""):(t.css({bottom:0}),i="-"),e.transition&&t.css({transition:"linear transform "+e.transitionDuration+"ms "+e.transitionDelay+"ms"}),o.movement(t,e,i),n.on("scroll",(function(){o.movement(t,e,i)})))},movement:function(t,e,i){var o=$(window),n=o.scrollTop(),s=t.offset().top-n,a=e.isInsideSVG?2:100,d=t.innerHeight(),r=a*s/o.height(),l=a*(s+(i+r/e.speed)*d/100)/o.height();Riode.isOnScreen(t[0])&&(e.horizontal?t.css({transform:"translate3d("+i+r/e.speed+"%, "+i+r/e.speed+"%, 0)"}):this.flag?(t.css({transform:"translate3d(0, "+i+l/e.speed+"%, 0)"}),this.flag=!1):t.css({transform:"translate3d(0, "+i+r/e.speed+"%, 0)"}))},init:function(t){var e=this;Riode.$(t).each((function(){var t,i=$(this);t=$.extend(!0,{},Riode.floatScrollDefaults,Riode.parseOptions(i.data("plugin-options"))),e.initScrollFloatElement(i,t)}))}},Riode.appearAnimate=function(t){Riode.$(t).each((function(){var t=this;Riode.appear(t,(function(){if(t.classList.contains("appear-animate")){var e=$.extend({},Riode.defaults.animation,Riode.parseOptions(t.getAttribute("data-animation-options")));Riode.call((function(){setTimeout((function(){t.style["animation-duration"]=e.duration,t.classList.add(e.name),t.classList.add("appear-animation-visible")}),e.delay?1e3*Number(e.delay.slice(0,-1)):0)}))}}))}))},Riode.lazyload=function(t,e){function i(){this.setAttribute("src",this.getAttribute("data-src")),this.addEventListener("load",(function(){this.style["padding-top"]="",this.classList.remove("lazy-img")}))}Riode.$(t).find(".lazy-img").each((function(){void 0!==e&&e?i.call(this):Riode.appear(this,i)}))},Riode.slider=function(){function t(t,e){return this.init(t,e)}var e=function(t){var e,i=["","-xs","-sm","-md","-lg","-xl"];for(this.classList.remove("row"),s=0;s<6;++s)for(e=1;e<=12;++e)this.classList.remove("cols"+i[s]+"-"+e);if(this.classList.remove("gutter-no"),this.classList.remove("gutter-sm"),this.classList.remove("gutter-lg"),this.classList.contains("animation-slider"))for(var o=this.children,n=o.length,s=0;s<n;++s)o[s].setAttribute("data-index",s+1)},i=function(t){var e,i=this.firstElementChild.firstElementChild.children,o=i.length;for(e=0;e<o;++e)if(!i[e].classList.contains("active")){var n,s=Riode.byClass("appear-animate",i[e]);for(n=s.length-1;n>=0;--n)s[n].classList.remove("appear-animate")}var a=$(t.currentTarget);a.find("video").on("ended",(function(){$(this).closest(".owl-item").hasClass("active")&&(!0===a.data("owl.carousel").options.autoplay?(!1===a.data("owl.carousel").options.loop&&a.data().children-1===a.find(".owl-item.active").index()&&(this.loop=!0,this.play()),a.trigger("next.owl.carousel"),a.trigger("play.owl.autoplay")):(this.loop=!0,this.play()))}))},o=function(t){$(window).trigger("appear.check");var e=$(t.currentTarget),i=e.find(".owl-item.active video");e.find(".owl-item:not(.active) video").each((function(){this.paused||e.trigger("play.owl.autoplay"),this.pause(),this.currentTime=0})),i.length&&(!0===e.data("owl.carousel").options.autoplay&&e.trigger("stop.owl.autoplay"),i.each((function(){this.paused&&this.play()})))},n=function(t){var e=this;$(t.currentTarget).find(".owl-item.active .slide-animate").each((function(){var t=$(this),i=$.extend(!0,{},Riode.defaults.animation,Riode.parseOptions(t.data("animation-options"))),o=i.duration,n=i.delay,s=i.name;t.css("animation-duration",o);var a=Riode.requestTimeout((function(){t.addClass(s),t.addClass("show-content")}),n?1e3*Number(n.slice(0,-1)):0);e.timers.push(a)}))},s=function(t){$(t.currentTarget).find(".owl-item.active .slide-animate").each((function(){var t=$(this);t.addClass("show-content"),t.attr("style","")}))},a=function(t){var e=this,i=$(t.currentTarget);e.translateFlag=1,e.prev=e.next,i.find(".owl-item .slide-animate").each((function(){var t=$(this),e=$.extend(!0,{},Riode.defaults.animation,Riode.parseOptions(t.data("animation-options")));t.removeClass(e.name)}))},d=function(t){var e=this,i=$(t.currentTarget);if(1==e.translateFlag){if(e.next=i.find(".owl-item").eq(t.item.index).children().attr("data-index"),i.find(".show-content").removeClass("show-content"),e.prev!=e.next){if(i.find(".show-content").removeClass("show-content"),i.hasClass("animation-slider")){for(var o=0;o<e.timers.length;o++)Riode.deleteTimeout(e.timers[o]);e.timers=[]}i.find(".owl-item.active .slide-animate").each((function(){var t=$(this),i=$.extend(!0,{},Riode.defaults.animation,Riode.parseOptions(t.data("animation-options"))),o=i.duration,n=i.delay,s=i.name;t.css("animation-duration",o),t.css("animation-delay",n),t.css("transition-property","visibility, opacity"),t.css("transition-delay",n),t.css("transition-duration",o),t.addClass(s),o=o||"0.75s",t.addClass("show-content");var a=Riode.requestTimeout((function(){t.css("transition-property",""),t.css("transition-delay",""),t.css("transition-duration",""),e.timers.splice(e.timers.indexOf(a),1)}),n?1e3*Number(n.slice(0,-1))+500*Number(o.slice(0,-1)):500*Number(o.slice(0,-1)));e.timers.push(a)}))}else i.find(".owl-item").eq(t.item.index).find(".slide-animate").addClass("show-content");e.translateFlag=0}};return t.zoomImage=function(){Riode.zoomImage(this.$element)},t.zoomImageRefresh=function(){this.$element.find("img").each((function(){var t=$(this);if($.fn.elevateZoom){var e=t.data("elevateZoom");void 0!==e?e.refresh():(Riode.defaults.zoomImage.zoomContainer=t.parent(),t.elevateZoom(Riode.defaults.zoomImage))}}))},Riode.defaults.sliderPresets["product-single-carousel"].onInitialized=Riode.defaults.sliderPresets["product-gallery-carousel"].onInitialized=t.zoomImage,Riode.defaults.sliderPresets["product-single-carousel"].onRefreshed=Riode.defaults.sliderPresets["product-gallery-carousel"].onRefreshed=t.zoomImageRefresh,t.prototype.init=function(t,r){this.timers=[],this.translateFlag=0,this.prev=1,this.next=1,Riode.lazyload(t,!0);var l=t.attr("class").split(" "),c=$.extend(!0,{},Riode.defaults.slider);l.forEach((function(t){var e=Riode.defaults.sliderPresets[t];e&&$.extend(!0,c,e)})),t.find("video").each((function(){this.loop=!1})),$.extend(!0,c,Riode.parseOptions(t.attr("data-owl-options")),r),n=n.bind(this),a=a.bind(this),d=d.bind(this),t.on("initialize.owl.carousel",e).on("initialized.owl.carousel",i).on("translated.owl.carousel",o),t.hasClass("animation-slider")&&t.on("initialized.owl.carousel",n).on("resized.owl.carousel",s).on("translate.owl.carousel",a).on("translated.owl.carousel",d),t.owlCarousel(c)},function(e,i){Riode.$(e).each((function(){var e=$(this);Riode.call((function(){new t(e,i)}))}))}}(),Riode.popup=function(t,e){var i=$.magnificPopup.instance,o=$.extend(!0,{},Riode.defaults.popup,void 0!==e?Riode.defaults.popupPresets[e]:{},t);i.isOpen&&i.content?(i.close(),setTimeout((function(){$.magnificPopup.open(o)}),500)):$.magnificPopup.open(o)},Riode.initPopups=function(){Riode.$body.on("click",".btn-iframe",(function(t){t.preventDefault(),Riode.popup({items:{src:'<video src="'+$(t.currentTarget).attr("href")+'" autoplay loop controls>',type:"inline"},mainClass:"mfp-video-popup"},"video")}))},Riode.stickyContent=function(t,e){var i=Riode.$(t),o=$.extend({},Riode.defaults.stickyContent,e),n=window.pageYOffset;if(0!=i.length){var s=function(){i.each((function(t){var e=$(this);if(e.data("is-wrap"))(window.innerWidth<o.minWidth||window.innerWidth>=o.maxWidth)&&(e.unwrap(".sticky-content-wrapper"),e.data("is-wrap",!1));else{var i,n=e.removeClass("fixed").outerHeight(!0);if(i=e.offset().top+n,e.hasClass("has-dropdown")){var s=e.find(".category-dropdown .dropdown-box");s.length&&(i+=s[0].offsetHeight)}e.data("top",i),function(t,e){window.innerWidth>=o.minWidth&&window.innerWidth<=o.maxWidth&&(t.wrap('<div class="sticky-content-wrapper"></div>'),t.parent().css("height",e+"px"),t.data("is-wrap",!0))}(e,n)}}))},a=function(t){t&&!t.isTrusted||i.each((function(t){var e=$(this),i=!0;o.scrollMode&&(i=n>window.pageYOffset,n=window.pageYOffset),window.pageYOffset>(0==o.top?e.data("top"):o.top)&&window.innerWidth>=o.minWidth&&window.innerWidth<=o.maxWidth?(e.hasClass("fix-top")?(void 0===e.data("offset-top")&&function(t){var e=0,i=0;$(".sticky-content.fixed.fix-top").each((function(){e+=$(this)[0].offsetHeight,i++})),t.data("offset-top",e),t.data("z-index",o.max_index-i)}(e),e.css("margin-top",e.data("offset-top")+"px")):e.hasClass("fix-bottom")&&(void 0===e.data("offset-bottom")&&function(t){var e=0,i=0;$(".sticky-content.fixed.fix-bottom").each((function(){e+=$(this)[0].offsetHeight,i++})),t.data("offset-bottom",e),t.data("z-index",o.max_index-i)}(e),e.css("margin-bottom",e.data("offset-bottom")+"px")),e.css("z-index",e.data("z-index")),o.scrollMode?i&&e.hasClass("fix-top")||!i&&e.hasClass("fix-bottom")?e.addClass("fixed"):(e.removeClass("fixed"),e.css("margin","")):e.addClass("fixed"),o.hide&&e.parent(".sticky-content-wrapper").css("display","")):(e.removeClass("fixed"),e.css("margin-top",""),e.css("margin-bottom",""),o.hide&&e.parent(".sticky-content-wrapper").css("display","none"))}))},d=function(t){i.removeData("offset-top").removeData("offset-bottom").removeClass("fixed").css("margin","").css("z-index",""),Riode.call((function(){s(),a()}))};setTimeout(s,550),setTimeout(a,600),Riode.call((function(){window.addEventListener("scroll",a,{passive:!0}),Riode.$window.on("resize",d)}),700)}},Riode.initScrollTopButton=function(){var t=Riode.byId("scroll-top");if(t){t.addEventListener("click",(function(t){$("html, body").animate({scrollTop:0},600),t.preventDefault()}));var e=function(){window.pageYOffset>400?t.classList.add("show"):t.classList.remove("show")};Riode.call(e,500),window.addEventListener("scroll",e,{passive:!0})}},Riode.isotopes=function(t,e){if("function"==typeof imagesLoaded&&$.fn.isotope){Riode.$(t).each((function(){var t=$(this),i=$.extend(!0,{},Riode.defaults.isotope,Riode.parseOptions(t.attr("data-grid-options")),e||{});Riode.lazyload(t),t.imagesLoaded((function(){i.customInitHeight&&t.height(t.height()),i.customDelay&&Riode.call((function(){t.isotope(i)}),parseInt(i.customDelay)),t.isotope(i)}))}))}},Riode.initNavFilter=function(t){$.fn.isotope&&Riode.$(t).on("click",(function(t){var e=$(this),i=e.attr("data-filter"),o=e.parent().parent().attr("data-target");$(o||".grid").isotope({filter:i}).isotope("on","arrangeComplete",(function(){Riode.$window.trigger("appear.check")})),e.parent().siblings().children().removeClass("active"),e.addClass("active"),t.preventDefault()}))},Riode.playableVideo=function(t){$(t+" .video-play").on("click",(function(e){var i=$(this).closest(t);i.hasClass("playing")?i.removeClass("playing").addClass("paused").find("video")[0].pause():i.removeClass("paused").addClass("playing").find("video")[0].play(),e.preventDefault()})),$(t+" video").on("ended",(function(){$(this).closest(t).removeClass("playing")}))},$(window).on("riode_complete",(function(){Riode.floatBackground(".float-bg"),Riode.shapeOverlay(".shape-overlay",{fill:"url(#bg-gradient)",gradient:'<defs><linearGradient id="bg-gradient" y2="100%"><stop offset="0" stop-color="#08c"/><stop offset="100%" stop-color="#5349ff"/></linearGradient></defs>'}),$(".demos .appear-animate, .features .appear-animate").each((function(){this.setAttribute("data-animation-options","{'name':'fadeInUpShorter','duration':'.5s','delay':'"+parseInt(3*Math.random())/10+"s'}")})),Riode.$body.on("click",".main-nav a, .mobile-menu a, .scroll-to",(function(t){var e=t.currentTarget,i=e.hash?e.hash:e.slice(e.lastIndexOf("#"));i.startsWith("#")&&($(".mobile-menu-overlay").click(),Riode.scrollTo(i),t.preventDefault())})),Riode.lazyload(document.body),Riode.playableVideo(".video-banner"),Riode.parallaxBg(".parallax-effect"),Riode.FloatScrollElement.init(".scroll-float-el")})),Riode.initLayout=function(){Riode.isotopes(".grid:not(.grid-float)")},Riode.initMenu=function(){$(".menu li").each((function(){this.lastElementChild&&("UL"===this.lastElementChild.tagName||this.lastElementChild.classList.contains("megamenu"))&&this.classList.add("submenu")})),$(".main-nav .megamenu, .main-nav .submenu > ul").each((function(){var t=$(this),e=t.offset().left,i=t.outerWidth(),o=e+i-(window.innerWidth-20);if(t.closest("li").hasClass("submenu-container")){var n=$(window).innerWidth();n<=1180&&(t.css("width",n),i=t.outerWidth()),t.css("margin-left ",(n-i)/2-e)}else o>=0&&e>20&&t.css("margin-left ","-="+o)})),Riode.$window.on("resize",(function(){$(".main-nav .megamenu, .main-nav .submenu > ul").each((function(){var t=$(this),e=t.offset().left,i=t.outerWidth(),o=e+i-(window.innerWidth-20);if(t.closest("li").hasClass("submenu-container")){var n=$(window).innerWidth();n<=1180&&(t.css("width",n),i=t.outerWidth()),t.css("margin-left ",0),e=t.offset().left,t.css("margin-left ",(n-i)/2-e)}else o>=0&&e>20&&t.css("margin-left ","-="+o)}))}))},Riode.initMobileMenu=function(){function t(){Riode.$body.removeClass("mmenu-active")}$(".mobile-menu li, .toggle-menu li").each((function(){if(this.lastElementChild&&("UL"===this.lastElementChild.tagName||this.lastElementChild.classList.contains("megamenu"))){var t=document.createElement("span");t.className="toggle-btn",this.firstElementChild.appendChild(t)}})),$(".mobile-menu-toggle").on("click",(function(){Riode.$body.addClass("mmenu-active")})),$(".mobile-menu-overlay").on("click",t),$(".mobile-menu-close").on("click",t),Riode.$window.on("resize",t)},Riode.init=function(){Riode.appearAnimate(".appear-animate"),Riode.slider(".owl-carousel"),Riode.stickyContent(".product-sticky-content, .sticky-header",{top:!1}),Riode.initScrollTopButton(),Riode.initNavFilter(".nav-filters .nav-filter"),Riode.initPopups(),Riode.initMenu(),Riode.initMobileMenu(),Riode.status="complete"},window.onload=function(){Riode.status="loaded",Riode.$body.addClass("loaded"),Riode.$window.trigger("riode_load"),Riode.call(Riode.initLayout),Riode.call(Riode.init),Riode.$window.trigger("riode_complete")}}(jQuery);