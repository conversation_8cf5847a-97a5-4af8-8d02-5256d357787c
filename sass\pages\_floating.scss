/***************************************
        floating
***************************************/
//floating set
.floating-set {
    background-color: #f8f8f8;
    .floating-template {
        position: relative;
    }
    .bg-yel-float {
        padding: 37px 0 38px;
        background-color: #F0D67C;
    }
    .floating-content {
        position: relative;
        text-align: center;
        left: 50%;
        transform: translate(-50%, -50%);
        img {
            border-radius: 1rem;
            box-shadow: 0px 5px 20px 0px rgb(0 0 0 / 6%);
        }
    }
    .floating-speed-image1 .floating{
        background-image: url('../images/elements/floating/float-set3.jpg');
    }
    .floating-speed-image2 .floating{
        background-image: url('../images/elements/floating/float-set4.jpg');
    }
}
//float-scroll
.float-scroll {
    background-color: #313439;
    h2 {
        font-size: 26px;
        letter-spacing: .025em;
    }
    .scroll-area {
        display: flex;
        align-items: center;
        min-height: 100vh;
    }
    .skrollr {
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        padding: 0 1rem;
        background-color: #3B3E42;
        height: 200px;
        width: 100%;
    }
    .scroll-entire {
        margin-bottom: 140px;
    }
    @include mq(md, max) {
        .scroll-area {
            min-height: 70vh;
        }   
    }
}