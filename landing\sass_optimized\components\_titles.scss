/* -------------------------------------------
   Titles
        - Default
        - Simple
        - Line
        - Underline
        - Icon
        - Link
---------------------------------------------- */
// Variables
@include set-default(
	(
		base: (
            title: (
                margin-bottom: 2.6rem,
                text-transform: uppercase,
                font-size: 1.8rem,
                font-weight: 600,
                line-height: 1.2,
                letter-spacing: -.01em,
                color: #2
                desc: (
                    margin-bottom: 2.3rem,
                    text-transform: false,
                    font-family: false,
                    font-size: 1.4rem,
                    font-weight: false,
                    line-height: 1.71,
                    letter-spacing: false,
                    color: #999,
                ),
                border: (
                    color: #edeef0,
                    _active-color: $primary-color,
                    _width: 2px,
                    line-height: 2.28,
                ),
            )
        )
    )
);
.title {
    display: flex;
    flex-wrap: wrap;
    position: relative;
    align-items: center;
    text-align: center;
    @include print_css( base, title );
    &::before,
    &::after {
        flex: 1;
        content: '';
        @include css( height, base, title, border, _width );
        @include css( background-color, base, title, border, color );
    }
    &::before {
        margin-right: 3rem;
    }
    &::after {
        margin-left: 3rem;
    }
}
@include mq(xs, max) {
    .title {
        &::before { margin-right: 1.5rem; }
        &::after { margin-left: 1.5rem; }
    }
}
.title-wrapper {
    .title {
        margin-bottom: .5rem;
    }
    p {
        @include print_css( base, title, desc );
    }
}
// Simple
.title-simple {
    display: block;
    &::before,
    &::after {
        content: none;
    }
}
// Line
.title-line {
    &:before {
        content: none;
    }
}
// Underline
.title-underline {
    display: block;
    position: relative; 
    // line-height: 1;
    text-align: left;
    &:after {
        margin: 0;
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }
    &:before  {
        content: none;
    }
    span {
        display: inline-block;
        position: relative;
        z-index: 2;
        @include css( line-height, base, title, border, line-height );
        &::after {
            content: '';
            position: absolute;
            display: block;
            width: 100%;
            height: 2px;
            bottom: 0; 
            @include css( background-color, base, title, border, _active-color );
        }
    }
}
// Icon
.title-icon {
    flex-direction: column;
    > i {
        margin-top: 1rem;
        font-size:  1.4rem;
        color: $primary-color;
    }
    &::before,
    &::after {
        content: none;
    }
} 
.title-white {
    &::before,
    &::after {
        background-color: #fff;
        opacity: .1;
    }
    color: #fff;
    > .title {
        &::before,
        &::after {
            background-color: #fff;
            opacity: .1;
        }
        color: #fff;
    }
    > p {
        color: #fff;
        opacity: .5;
    }
}
// Link
.title-link {
    &::before,
    &::after {
        content: none;
    }
    justify-content: space-between;
    padding: 1rem 0;
    border-bottom: 1px solid $border-color;
    a {
        color: #444;
        font-size: 1.3rem;
        &:hover {
            color: $primary-color;
        }
    }
    i { font-size: 1rem }
}
.with-link {
    display: flex;
    justify-content: space-between;
    @include css( line-height, base, title, line-height );
    &::after {
        @include css( height, base, title, border, width );
        @include css( background-color, base, title, border, color );
    }
    a {
        display: inline-flex;
        align-items: center;
        margin-left: auto;
        font-size: 1.3rem;
        font-family: $second-font-family;
        font-weight: 700;
        letter-spacing: -.025em;
        color: #444;
        transition: color .3s;
        &:hover {
            color: $primary-color;
        }
    }
    i {
        margin-left: .5rem;
        font-size: 1.1rem;
        line-height: 0;
    }
}