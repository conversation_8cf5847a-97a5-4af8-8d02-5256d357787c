// base
.title-wrapper {
    position: relative;
    margin-bottom: 3.5rem;
    .badge {
        position: absolute;
        top: -4.4rem;
        font-size: 20px;
        font-weight: 600;
        line-height: 1em;
        letter-spacing: -0.5px;
        padding: 5px 15px 4px 11px;
        color: #fff;
        background-color: $secondary-color;
    }
    a {
        font-size: 14px;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: -0.1px;
        color: #222222;
    }
}
//header
.header-top {
    .header-left  {
        position: relative;
        overflow: hidden;
        i {
            font-size: 1.7rem;
        }
    }
    .welcome-msg {
        padding: 1.4rem 0 1.1rem;
        letter-spacing: -.025em;
        .contact {
            margin-right: 2.2rem;
        }
    }
}
.header-middle {
    .header-search i {
        font-size: 20px;
    }
    .divider {
        height: 1.9rem;
        background-color: #C4C4C4;
    }
}
.menu-active-underline>li>a::before {
    bottom: 8px;
}
.cart-dropdown.type3 .cart-toggle {
    padding: 17px 12px 16px 12px;
    background-color: #78b246;
    i {
        font-size: 1.3rem;
        margin-right: 8px;
    }
    &:hover {
        color: #fff;
    }
}
.intro-slider {
    .banner-content {
        top: 56.3%;
        transform: translateY(-50%);
    }
    .banner-subtitle {
        font-size: 2em;
        font-weight: 400;
        color: #181818;
        letter-spacing: 1.8px;
        margin-bottom: 2.6rem;
    }
    .banner-title {
        font-size: 5em;
        font-weight: 700;
        color: #181818;
        letter-spacing: -.3px;
        padding-bottom: 2.2rem;
        margin-bottom: 3rem;
        &:after {
            content: ' ';
            position: absolute;
            left: 0;
            height: 6px;
            width: 8rem;
            top: 100%;
            background: #222;
        }
    }
    p {
        font-size: 1.6em;
        letter-spacing: -.1px;
        line-height: 1.875em;
        margin-bottom: 0;
    }
    strong {
        font-size: 1.8rem;
        margin-right: 1.5rem;
        color: #181818;
    }
    .banner-desc {
        padding-left: 4px;
        margin-bottom: 4.1rem;
    }
    .banner img {
        min-height: 100rem;
        object-fit: cover;
    }
    &.owl-carousel .owl-nav {
        button {
            font-size: 46px;
            color: #6E6C6C;
            font-weight: 400;
            width: 1em;
            height: 1em;
            border: none;
            i {
                display: none;
            }
            &.owl-prev {
                font-family: 'riode';
                left: 5%;
                &::before {
                    content: "\e982";
                }
            }
            &.owl-next {
                font-family: 'riode';
                right: 5%;
                &::before {
                    content: "\e983";
                }
            }
            &:hover {
                color: $primary-color !important;
                background-color: transparent !important;
            }
        }
    }
}
.intro-slide2 {
    .ls-super {
        letter-spacing: .7em;
    }
    .banner-desc {
        font-size: 1.8rem;
        letter-spacing: 0;
        line-height: 1.8;
    }
}
.shape-divider {
    overflow: hidden;
    svg {
        position: absolute;
        z-index: 1;
        width: 100%;
    }
}
.shape1 svg {
    width: calc(150% + 1.3px);
    height: 200px;
    left: 50%;
    transform: translateX(-50%) rotateY(180deg);
    bottom: -1px;
    opacity: .3;
    path {
        fill: #fff
    }
}
.divider-section1 {
    margin-top: -10rem;
}
.shape2 svg {
    opacity: .5;
    bottom: -1px;
    width: calc(200% + 1.3px);
    height: 180px;
    left: 50%;
    transform: translateX(-50%) rotateX(180deg);
    path {
        fill: #fff;
    }
}
.shape3 svg {
    left: 50%;
    top: -60%;
    width: calc(250% + 1.3px);
    height: 170px;
    transform: translateX(-50%) rotateX(180deg);
    path {
        fill: #fff;
    }
}
.shadow-product {
    box-shadow: 0px 0px 30px 0px rgba(0,0,0,0.05);
}
.product-filter-section {
    .nav-filter {
        font-size: 14px;
        font-weight: 600;
        line-height: 1em;
        letter-spacing: -0.14px;
        border-bottom: 2px solid transparent;
        &:hover, &.active {
            border-bottom: 2px solid $primary-color;
        }
    }
    li {
        margin-bottom: .5rem;
        color: #222;
    }
}
.shape4 svg {
    top: -1px;
    width: calc(210% + 1.3px);
    height: 190px;
    left: 50%;
    transform: translateX(-50%);
    path {
        fill: #fff;
    }
}
.home .icon-box-side {
    padding: 6.5rem 1rem 3.5rem;
    background-color: #FFF;
    border-radius: 3px;
    box-shadow: 5px 5px 30px 0px rgba(0,0,0,0.05);
    .icon-box-icon {
        font-size: 4rem;
        height: 3.7rem;
        color: #222;
    }
    .d-icon-card {
        font-size: 4.8rem;
    }
    .d-icon-truck {
        font-size: 5rem;
    }
    p {
        font-size: 14px;
        line-height: 1.75;
        letter-spacing: -0.07px;
    }
    .icon-box-title {
        font-size: 1.6rem;
        font-weight: 700;
        line-height: 1.33;
        margin-bottom: 7px;
        letter-spacing: 0;
    }
}
.service-list-section {
    background-color: #78B244;
    .container {
        position: relative;
        z-index: 2;
        top: -3rem;
    }
}
.brand-section {
    background-color: #78B244;
    .brand-carousel {
        border-style: solid;
        border-width: 1px 0px 1px 0px;
        border-color: rgba(255,255,255,.2);
        .owl-stage-outer {
            margin: 0;
            padding: 0;
        }
        .owl-item:not(:last-child) {
            border-right: 1px solid rgba(255,255,255,.2);
        }
    }
    .brand {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 15rem;
        img {
            width: auto;
        }
    }
}
.banner-section { 
    background-color: #78B244;
    position: relative;
    .banner-content {
        position: absolute;
    }
    .banner-subtitle {
        font-size: 2em;
        line-height: 1.15em;
        letter-spacing: -0.6px;
        margin-bottom: 1.6rem;
    }
    .banner-title {
        font-size: 5em;
        line-height: 1em;
        letter-spacing: -1.6px;
    }
    .card-description {
        border-radius: 3px 3px 3px 3px;
        border-width: 15px 15px 15px 15px;
        border-color: #FFFFFF;
        border-style: solid;
        img {
            max-height: 41rem;
            object-fit: cover;
        }
    }
    p {
        font-size: 16px;
        line-height: 1.875em;
        letter-spacing: 0.1px;
    }
    .btn-play {
        font-size: 2.7rem;
    }
}
.shape5 svg {
    width: calc(200% + 1.3px);
    height: 130px;
    left: 50%;
    bottom: -1px;
    transform: translateX(-50%) rotateX(180deg);
    path {
        fill: #fff;
    }
}
.banner-section2 {
    .title-wrapper {
        margin-bottom: 1.8rem;
    }
    .title {
        line-height: 1.1em;
        letter-spacing: 0px;
    }
    .banner-content {
        position: absolute;
    }
    .banner-desc {
        line-height: 1.75em;
        letter-spacing: -0.21px;
        margin-bottom: 4.4rem;
    }
    .image-box  {
        position: relative;
        div {
            position: absolute;
            top: 60%;
            left: 1rem;
            right: 1rem;
            color: #fff;
            visibility: hidden;
            opacity: 0;
            transition: .3s;
        }
        &:hover div {
            visibility: visible;
            opacity: 1;
            top: 37%;
        }
        &:hover {
            figure:after {
                background-color: #000;
                opacity: .75;
            }
        }
        img {
            max-height: 35rem;
            object-fit: cover;
        }
    }
    .banner {
        max-height: 32rem;
    }
}
.blog-section {
    background-position: top center;
    background-repeat: no-repeat;
    padding-top: 16rem;
    .title {
        font-size: 4rem;
        letter-spacing: -2px;
    }
    .title-wrapper .badge {
        background-color: transparent;
        top: -3.5rem;
        padding-left: 0;
        font-weight: 700;
    }
}
.post-frame {
    .post-details {
        padding: 2.5rem 0 3.8rem;
    }
    .post-content {
        margin-bottom: 2rem;
        line-height: 1.7;
        letter-spacing: 0;
    }
    .btn {
        font-size: 1.3rem;
    }
}
.owl-theme .owl-nav.disabled+.owl-dots {
    margin-top: 4rem;
}
// Footer
.footer-middle {
    .logo-footer {
        margin-bottom: 5.1rem;
    }
    .col-account {
        .widget-body li {
            color: #a6a6a6;
            margin-bottom: 11px;
        }
    }
    .widget-newsletter {
        margin-bottom: 3.6rem;
        .btn {
            padding: .94em 1.61em .9em;
            letter-spacing: -.025em;
        }
    }
    .widget-newsletter .input-wrapper-inline {
        height: 4.6rem;
    }
}
.footer-bottom {
    .footer-left  {
        margin-top: 1px;
    }
    .footer-right {
        margin-bottom: 2px;
    }
}
.widget-newsletter {
    .form-control {
        padding: 1rem 1.5rem;
    }
    .btn {
        i {
            margin-left: .7rem;
        }
    }
}
// Shop page
.page-header {
    height: 35rem;
    padding-top: 15rem;
    background-position: top right;
    background-repeat: no-repeat;
    .page-subtitle {
        font-size: 1.6rem;
        font-weight: 400;
    }
}
// Product page
.accordion {
    .card-header {
        .collapse {
           color: #222;
        }
        a {
            padding: 2.7rem 0;
            &:after {
                content: '\e952';
            }
            &.collapse:after {
                content: '\e953';
            }
        }
    }
    .description-title {
        font-size: 1.6rem;
    }
    .card-body {
        padding: 0;
    }
    ul {
        padding-inline-start: 0;
        li {
            list-style: none;
        }
    }
    #product-tab-description .description-title ~ p, .card-description .description-title ~ p {
        line-height: 1.6;
    }
}
.single-product {
    .title {
        font-size: 2.4rem;
    }
}
@include mq(md) {
    .product-filter-section {
        .nav-filters {
            display: block;
        }
        li {
            margin-bottom: 2.3rem;
        }
    }
    .banner-section2 {
        .banner {
            max-width: 35%;
            flex: 0 0 35%;
        }
        .banner-image-wrapper {
            max-width: 65%;
            flex:  0 0 65%;
        }
    }
}
@include mq(lg) {
    .footer-middle {
        .col-lg-2 {
            max-width: 18%;
            flex: 0 0 18%;
        }
        .col-lg-3.col-contact {
            max-width: 20.9%;
            flex: 0 0 20.9%;
        }
        .col-lg-3.col-account {
            max-width: 19.5%;
            flex: 0 0 19.5%;
        }
        .col-lg-4 {
            max-width: 41.6%;
            flex: 0 0 41.6%;
        }
    }
}
@include mq(xl, max) {
    .main-nav .menu > li {
        margin-right: 2.9rem;
    }
}
@include mq(lg, max) {
    .header-top {
        .header-left  {
            margin-right: 0;
        }
        .call {
            display: none;
        }
    }
    .header-middle {
        .header-left , .header-right {
            flex: none;
        }
        .header-center {
            flex: 1;
        }
    }
    .intro-slider .banner img {
        min-height: 85rem;
    }
    .intro-slider .banner-content {
        top: 50%;
    }
    .footer-middle .row>div:last-child .widget {
        margin-bottom: 2rem;
    }
}
@include mq(md, max) {
    .header-top {
        .wishlist, .login-link {
            display: none;
        }
    }
    .banner-section .banner {
        min-height: 40rem;
    }
    .banner-section2 .banner-content {
        position: static;
        margin-bottom: 3rem;
        transform: none !important;
    }
}
@include mq(sm, max) {
    .intro-section, .banner-section {
        .banner {
            font-size: .9rem;
        }
    }
    .welcome-msg {
        transform: translateX(0);
        animation: 6s linear 2s 1 show_msg_first, 12s linear 8s infinite show_msg;
    }
    .title-wrapper {
        .title {
            display: block;
            a {
                margin-top: 10px;
                display: block;
            }
        }
    }
    .owl-shadow-carousel .owl-stage-outer {
        margin: -20px;
        padding: 20px;
    }
}
@include mq('1920px') {
    .blog-section {
        background-size: contain;
    }
}