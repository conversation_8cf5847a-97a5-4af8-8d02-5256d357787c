// Demo beauty
// base
h1, h2, h3, h4, h5, h6, .btn {
    font-family: 'vazir';
}
//title
.title.title-underline {
    position: relative;
    &::after {
        content: '';
        display: block;
        border-bottom: 4px solid $primary-color;
        width: 60px;
        margin: auto;
        margin-top: 12px;
        bottom: -15px;
    }
}
//header
.header-top {
    .header-left  {
        position: relative;
        overflow: hidden;
        i {
            font-size: 1.7rem;
        }
    }
    .welcome-msg {
        padding: 1.4rem 0 1.1rem;
        letter-spacing: -.025em;
        .contact {
            margin-right: 17px;
        }
    }
}
.header-middle {
    .header-search i {
        font-size: 20px;
    }
    .divider {
        height: 1.9rem;
    }
}
.cart-dropdown.type3 .cart-toggle {
    padding: 17px 12px 15px 12px;
    i {
        font-size: 1.3rem;
        margin-right: 8px;
    }
    &:hover {
        background-color: #639573;
    }
}
.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, calc(100% / 12));
    grid-auto-rows: auto;
    > * {
        float: left;
        padding: 1rem;
    }
    .height-x2 {
        grid-column-end: span 4;
        grid-row-end: span 2;
    }
    .height-x1 {
        grid-row-end: span 1;
        grid-column-end: span 3;
    }
    .w-2 {
        grid-column-end: span 5;  
    }
    .category > a {
        display: block;
    }
    .category {
        width: 100%;
        height: 100%;
        >a, figure, img {
            width: 100%;
            height: 100%;
        }
        .category-content {
            left: 3rem;
            top: 2.8rem;
        }
        .category-name {
            font-weight: 500;
            letter-spacing: -.4px;
            margin-bottom: 1px;
        }
    }
    img {
        object-fit: cover;
        border-radius: 0;
    }
}
.category5 .banner-content {
    position: absolute;
    .banner-title {
        font-size: 2.6rem;
        letter-spacing: -.75px;
        margin-bottom: 1.3rem;
    }
    p {
        font-family: 'vazir';
    }
}
// main
// intro slide
.intro-slider {
    figure img {
        min-height: 530px;
        object-fit: cover;
    }
    .banner-content {
        position: absolute;
        padding: 1px 0px 0 4px;
        margin: 0 20px;
        max-width: 49rem;
    }
    .banner-subtitle {
        font-size: 2em;
        font-weight: 600;
        letter-spacing: .4px;
        text-transform: uppercase;
        color: $primary-color;
    }
    .banner-title {
        color: #333333;
        font-size: 4.8em;
        font-weight: 700;
        text-transform: none;
        line-height: 1.05em;
        letter-spacing: -1.2px;
        margin-bottom: 4.2rem;
    }
    .btn {
        margin-left: 2px;
        background-color: #333;
        &:hover {
            background-color: #343434;
        }
        i {
            margin-left: 0.7rem;
            margin-bottom: 0px;
        }
    }
    &.owl-carousel .owl-nav {
        button {
            font-size: 46px;
            color: #6E6C6C;
            font-weight: 400;
            width: 1em;
            height: 1em;
            border: none;
            i {
                display: none;
            }
            &.owl-prev {
                font-family: 'riode';
                left: 5%;
                &::before {
                    content: "\e982";
                }
            }
            &.owl-next {
                font-family: 'riode';
                right: 5%;
                &::before {
                    content: "\e983";
                }
            }
            &:hover {
                color: $primary-color !important;
                background-color: transparent !important;
            }
        }
    }
}
.intro-slide1 {
    .banner-content {
        left: 35px;
    }
    .banner-subtitle {
        margin-bottom: 0px;
    }
    strong {
        letter-spacing: -0.45px;
    }
}
.intro-slide2 {
    .banner-content {
        right: 40px;
    }
    .banner-subtitle {
        margin-bottom: 0;
        letter-spacing: -.6px;
    }
    p {
        color: #666666;
        letter-spacing: 0.3px;
    }
}
.intro-slide1 .banner-subtitle:after, 
.intro-slide2 .banner-subtitle:before {
        display: inline-block;
        content: '';
        width: 6rem;
        height: 4px;
        background-color: $primary-color;
        vertical-align: middle;
        margin: -2px 15px 0 13px;
}
//service section
.service-carousel {
    padding: 31px 0;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.07);
    .owl-stage-outer {
        padding: 1px 0;
    }
    .icon-box-icon {
        color: $primary-color;
        margin-right: 15px;
        font-size: 37px;
    }
    .icon-box-title {
        font-family: 'vazir';
        font-size: 1.5rem;
        text-transform: capitalize;
        font-style: normal;
        line-height: 1.3em;
        letter-spacing: -0.1px;
        margin-bottom: -1px;
    }
    p {
        line-height: 1.3em;
        letter-spacing: -0.2px;
    }
}
.parallax {
    .banner-subtitle {
        letter-spacing: -.3px;
        margin-bottom: 1.6rem;
        &:after {
            display: inline-block;
            content: '';
            width: 6rem;
            height: 4px;
            background-color: $primary-color;
            vertical-align: middle;
            margin: -2px 15px 0 13px;
        }
    }
    .banner-title {
        font-size: 4em;
        letter-spacing: 0px;
        margin-bottom: 3.4rem;
    }
    .banner-content {
        padding: 10.3rem 0 10.9rem;
    }
}
// post
.post-frame {
    .post-details {
        padding: 2.2rem 0;
    }
    .post-content {
        font-family: 'vazir';
        margin-bottom: 2rem;
    }
    .btn {
        letter-spacing: -.35px;
        font-weight: 600;
    }
}
.brand-section {
    .with-border {
        border-top: 1px solid #e1e1e1;
        border-bottom: 1px solid #e1e1e1;
    }
}
.instagram-section {
    background-color: #F2F3F5;
}
// Footer
.footer-middle {
    .widget:not(.widget-about) { 
		margin-bottom: 3.8rem;
		margin-top: .4rem; 
	}
	.widget-about {
		margin-bottom: 3.5rem;
	}
}
// shop
.page-header {
    .breadcrumb {
        font-family: 'vazir';
    }
    .page-title {
        margin-bottom: 2px;
    }
}
.toolbox-pagination {
    p {
        font-family: 'vazir';
    }
}
.main.with-border {
    border-top: 1px solid #e1e1e1;
}
.single-product {
    .breadcrumb li:not(:last-child)::after {
        display: none;
    }
    p, label, .card-header a, li, td {
        font-family: 'vazir';
    }
}
.accordion {
    .card-header {
        .collapse {
           color: #222;
        }
        a {
            padding: 2.7rem 0;
            &:after {
                content: '\e952';
            }
            &.collapse:after {
                content: '\e953';
            }
        }
    }
    .description-title {
        font-size: 1.6rem;
    }
    .card-body {
        padding: 0;
    }
    ul {
        padding-inline-start: 0;
        li {
            list-style: none;
        }
    }
    #product-tab-description .description-title ~ p, .card-description .description-title ~ p {
        line-height: 1.6;
    }
}
@include mq(lg) {
    .service-carousel .owl-item:not(:last-child) .icon-box::after {
        content: '';
        height: 37px;
        width: 1px;
        background: #e1e1e1;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
    }
    @include mq(xl, max) {
        .category-grid .category5 p {
            font-size: 1.3rem;
        }
    }
}
@include mq(lg, max) {
    .category-grid {
        .height-x1 {
            grid-row-end: span 1;
            grid-column-end: span 6;
        }
        .height-x2 {
            grid-row-end: span 2;
            grid-column-end: span 6;
        }
    }
    .header-top {
        .header-left  {
            margin-right: 0;
        }
        .call {
            display: none;
        }
    }
    .header-middle {
        .header-left , .header-right {
            flex: none;
        }
        .header-center {
            flex: 1;
        }
    }
}
@include mq(md, max) {
    .header-top {
        .wishlist, .login-link {
            display: none;
        }
    }
    .intro-section {
        .banner {
            font-size: .8rem;
        }
        .banner-content {
            margin: 0;
        }
    }
    .parallax .banner-content {
        font-size: .8rem;
    }
}
@include mq(sm, max) {
    .welcome-msg {
        transform: translateX(0);
        animation: 6s linear 2s 1 show_msg_first, 12s linear 8s infinite show_msg;
    }
    .category-grid {
        display: block;
        margin-left: 0;
        margin-right: 0;
        > * {
            float: none;
        }
        .height-x1, .height-x1 img {
            max-height: 30rem;
        }
        .height-x2, .height-x2 img {
            max-height: 40rem;
        }
        .height-x1, .height-x2 {
            overflow: hidden;
            margin-bottom: 2rem;
            padding: 0;
        }
    }
}