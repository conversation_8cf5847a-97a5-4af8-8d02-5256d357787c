/* 
Demo 3
*/
@include set(
    (
        base: (
            title: (
                font-size: 2rem,
                line-height: 2.75,
                margin-bottom: 2rem,
                letter-spacing: -.02em,
                border: (
                    color: #eee,
                    _height: 1px
                )
            )
        ),
        header: (
            bottom: (
                padding: 0 2.2rem,
                background: #28475c,
                color: #fff,
                font-weight: 600,
                letter-spacing:-.015em
            ),
            search: (
                simple: (
                    color: #e2e2e2
                )
            )
        ),
        menu: (
            ancestor: (
                padding: 2rem 0,
                font-weight: 600,
                letter-spacing: -.015em
            )
        ),
        category-menu: (
            padding: 0 0 .4rem,
            background: $white-color,
            border: 1px solid $border-color,
            ancestor: ( 
                border-bottom: false,
                padding: 1rem 0,
                color: #333,
                _split-line: false
            ),
            title: (
                padding: 1.7rem 0rem 1.7rem,
                font-weight: 600,
                border-bottom: 1px solid #e1e1e1,
                letter-spacing: -.035em,
            ),
            icon: (
                padding-left: 0,
                color: #2
                margin-bottom: .2rem
            )
        ),
        product: (
            body: (
                padding-top: 1.2rem,
                padding-bottom: .8rem
            ),
            label: (
                new: (
                    background: #0162d2
                ),
                sale: (
                    background: $primary-color
                )
            ),
            price: (
                margin-bottom: .5rem
            ),
            rating: (
                _star-color: $primary-color
            )
        ),
        post: (
            detail: (
                padding: 1.9rem 0 1rem
            ),
            meta: (
                margin-bottom: .7rem,
                font-size: 1.3rem,
            ),
            info: (
                margin-bottom: .7rem,
                color: #666
            ),
            title: (
                margin-bottom: 1rem,
                font-size: 1.6rem,
                letter-spacing: -.025em,
                line-height: 1.5
            )
        ),
        product-single: (
            rating: (
                color: $primary-color
            )
        ),
        footer: (
            middle: (
                padding-bottom: 1.5rem
            ),
            bottom: (
                padding: 3rem 0 2.8rem
            ),
            newsletter: (
                title: (
                    color: #e1e1e1
                )
            )
        )
    )
);