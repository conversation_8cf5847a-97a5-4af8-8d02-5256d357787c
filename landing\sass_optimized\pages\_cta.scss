/* -------------------------------------------
    Call to Action
        - Simple
        - 2 Columns
        - 3 Columns
        - Expanded Newsletter
        - Expanded on Background
---------------------------------------------- */
// Simple
.cta-simple {
    position: relative;
    overflow: hidden;
    background-color: #edeef0;
    &::before {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        width: 50%;
        padding-top: 50%;
        background: linear-gradient( 135deg, #0088cc 0%, $primary-color 50%, transparent 50.1% );
    }
    .banner-content { 
        position: relative;
        margin: 1.5rem;
        padding: 3.4rem 0;
        z-index: 3;
    }
    .banner-header {
        position: relative;
        max-width: 29.1%;
        flex: 0 0 29.1%;
        text-align: right;
        &::after {
            content: '';
            position: absolute;
            top: 50%;
            right: 0;
            transform: translateY( -50% );
            width: .5rem;
            height: 9rem;
            background-color: #edeef0;
        }
    }
    .banner-text { flex-grow: 1; }
    .banner-title {
        margin-bottom: .2rem;
        font-size: 3em;
    }
    .banner-subtitle {
        margin-bottom: 0;
        font-size: 2em;
    }
    .banner-text {
        h4 {
            font-size: 2em;
            line-height: 1.3;
        }
    }
    .btn {
        position: relative;
        margin-right: 5.2rem;
        padding: 1.38em 2em;
        min-width: 18rem;
        overflow: hidden;
        background: transparent;
        transition: opacity .3s;
        border: 0;
        &::before {
            content: '';
            display: block;
            position: absolute;
            left: -100%;
            width: 200%;
            top: 0;
            bottom: 0;
            z-index: -1;
            background: linear-gradient( 90deg, $primary-color, #0088cc , $primary-color );
            transition: left .3s;
        }
        &:hover {
            &::before {
                left: 0;
            }
        }
    }
}
// 2 Columns
.banner-1,
.banner-2 {
    img {
        min-height: 30rem;
        object-fit: cover;
    }
}
.banner-1 {
    background-color: #4f4f4f;
    .banner-title {
        margin-bottom: 1.1rem;
        font-size: 4em;
    }
    .banner-subtitle { 
        margin-bottom: 1rem;
        white-space: nowrap;
        font-size: 3em;
    }
    p {
        margin-bottom: 2.4rem;
        font-size: 1.6em;
        line-height: 1.25;
    }
    .btn { padding: .77em 1.12em; }
}
.banner-2 {
    background-color: #2b579a;
    .banner-content { width: 69%; }
    .banner-title {
        font-size: 3em;
        line-height: 1.06;
    }
    p {
        margin-bottom: 1.6rem; 
        font-size: 1.6em;
        line-height: 1.2;
    }
    input.form-control {
        border: none;
        background-color: rgba( 255, 255, 255, .3 );
        border-radius: 2rem;
    }
    .btn {
        padding: .77em 1.35em;
        &:hover,
        &:active,
        &:focus {
            color: $primary-color;
            border-color: #fff;
        }
    }
}
// 3 Columns
.banner-group {
    .banner-title {
        margin-bottom: .1rem;
        font-size: 2.2em;
    }
    .banner-subtitle {
        margin-bottom: 1.8rem;
        font-size: 1.3em;
        &::after {
            margin-top: 1.8rem;
            content: '';
            display: block;
            width: 3.5rem;
            height: .4rem;
            background-color: #222;
        }
    }
}
.banner-3 {
    background-color: #ccc;
    .banner-content { left: 7.8%; }
}
.banner-4 {
    background-color: #ccc;
    figure {
        border: 1.5em solid $primary-color;
        img {
            width: 100%;
            max-height: 25rem;
        }
    }
    .banner-title {
        margin-bottom: .2rem;  
        font-size: 3em;
        white-space: nowrap;
    }
    .banner-subtitle {
        font-size: 2em;
        &::after { content: none; }
    }
    .btn { padding: .76em 1.36em; }
}
.banner-5 {
    background-color: #ccc;
    .banner-content { right: 3.9%; }
}
// Expanded Newsletter
.banner-newsletter {
    border: 2px solid $primary-color;
	.banner-content { padding: 1.8rem 0; }
	.icon-box { justify-content: flex-start; }
    .icon-box p {
		line-height: 1.43;
		letter-spacing: .01em;
    }
    .icon-box-icon {
		margin: 0 2.4rem 0 .2rem;
        font-size: 4.5rem;
    }
    .icon-box-title {
		font-size: 1.8rem;
        line-height: .9;
    }
    .input-wrapper {
        height: 4.8rem;
        .form-control {
            border: 1px solid #e4e4e4;
            border-left: 0;
        }
        .btn { padding: 1em 2.6em; }
    }
}
// Expanded on Background
.banner-background {
    padding: 10.7rem 0;
    background-color: #6b6b6b;
    .banner-title {
        margin-bottom: .9rem;
        font-size: 3em; 
    }
    .banner-subtitle {
        font-size: 1.8em;
        line-height: 1.34;
    }
    .input-wrapper {
        max-width: 60rem;
        width: 100%;
        height: 4.8rem;
        .form-control {
            position: relative;
            flex: 1;
            padding-left: 2.4rem;
            border: 1px solid #e7e7e7;
            border-left: 0;
            font-size: 1.3rem;
        }
        .btn { min-width: 12.3rem; }
    }
}
// Responsive
@include mq( lg, max ) {
    .cta-simple {
        .banner-content { text-align: center; }
        .banner-header {
            max-width: 100%;
            text-align: center;
            &::after {
                height: .5rem;
                width: 80%;
                top: 100%;
                right: auto;
                left: 50%;
                transform: translateX(-50%);
            }
        }
        .banner-text {
            max-width: 100%;
            flex: 0 0 100%;
        }
        .btn { margin: 0; }
    }
    .banner-2 .banner-content {
        width: 100%;
        padding: 0 3rem;
    }
	.banner-newsletter .icon-box { justify-content: center; }
}
@include mq( sm, max ) {
    .banner { font-size: .9rem; }
    .banner-group .banner { font-size: 1.3rem; }
    .banner-newsletter {
        .icon-box {
            display: block;
            text-align: center;
        }
        .icon-box-icon {
            margin-right: 0;
            margin-bottom: 1rem;
        }
        .icon-box-content { text-align: center; }
	}
}
@include mq( xs, max ) {
    .banner-group .banner { font-size: 1rem; }
}