// Mobile Filter Styles
.mobile-filter-btn {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
    display: none;

    @include mq('lg', 'max') {
        display: block;
    }

    .btn {
        border-radius: 30px;
        padding: 1rem 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        
        i {
            margin-right: 8px;
            font-size: 1.6rem;
        }
    }
}

// Mobile Sidebar Styles
@include mq('lg', 'max') {
    .shop-sidebar {
        position: fixed;
        top: 0;
        left: -300px;
        width: 280px;
        max-width: 90vw;
        height: 100vh;
        background: #fff;
        z-index: 1001;
        transition: left .3s ease-in-out;
        overflow-y: auto;
        padding: 2rem;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);

        &.show {
            left: 0;
        }

        .sidebar-content {
            height: 100%;
            overflow-y: auto;
        }

        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            display: none;

            &.show {
                display: block;
            }
        }

        .sidebar-close {
            position: absolute;
            top: 1.5rem;
            right: 1.5rem;
            font-size: 2rem;
            color: #999;
            z-index: 1002;
        }
    }
} 