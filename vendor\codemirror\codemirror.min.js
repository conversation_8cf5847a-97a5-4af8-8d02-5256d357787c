!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.CodeMirror=t()}(this,(function(){"use strict";var e=navigator.userAgent,t=navigator.platform,r=/gecko\/\d/i.test(e),n=/MSIE \d/.test(e),i=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e),o=n||i,a=o&&(n?document.documentMode||6:i[1]),l=/WebKit\//.test(e),s=l&&/Qt\/\d+\.\d+/.test(e),u=/Chrome\//.test(e),c=/Opera\//.test(e),d=/Apple Computer/.test(navigator.vendor),f=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e),h=/PhantomJS/.test(e),p=/AppleWebKit/.test(e)&&/Mobile\/\w+/.test(e),g=p||/Android|webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e),m=p||/Mac/.test(t),v=/\bCrOS\b/.test(e),y=/win/i.test(t),b=c&&e.match(/Version\/(\d*\.\d*)/);b&&(b=Number(b[1])),b&&b>=15&&(c=!1,l=!0);var w=m&&(s||c&&(null==b||b<12.11)),x=r||o&&a>=9;function k(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var C,S=function(e,t){var r=e.className,n=k(t).exec(r);if(n){var i=r.slice(n.index+n[0].length);e.className=r.slice(0,n.index)+(i?n[1]+i:"")}};function L(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function T(e,t){return L(e).appendChild(t)}function M(e,t,r,n){var i=document.createElement(e);if(r&&(i.className=r),n&&(i.style.cssText=n),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function N(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if(11==t.nodeType&&(t=t.host),t==e)return!0}while(t=t.parentNode)}function A(){var e;try{e=document.activeElement}catch(t){e=document.body||null}for(;e&&e.root&&e.root.activeElement;)e=e.root.activeElement;return e}function O(e,t){var r=e.className;k(t).test(r)||(e.className+=(r?" ":"")+t)}function W(e,t){for(var r=e.split(" "),n=0;n<r.length;n++)r[n]&&!k(r[n]).test(t)&&(t+=" "+r[n]);return t}C=document.createRange?function(e,t,r,n){var i=document.createRange();return i.setEnd(n||e,r),i.setStart(e,t),i}:function(e,t,r){var n=document.body.createTextRange();try{n.moveToElementText(e.parentNode)}catch(e){return n}return n.collapse(!0),n.moveEnd("character",r),n.moveStart("character",t),n};var z=function(e){e.select()};function D(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function H(e,t,r){for(var n in t||(t={}),e)!e.hasOwnProperty(n)||!1===r&&t.hasOwnProperty(n)||(t[n]=e[n]);return t}function P(e,t,r,n,i){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=n||0,a=i||0;;){var l=e.indexOf("\t",o);if(l<0||l>=t)return a+(t-o);a+=l-o,a+=r-a%r,o=l+1}}function I(){this.id=null}function E(e,t){for(var r=0;r<e.length;++r)if(e[r]==t)return r;return-1}p?z=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:o&&(z=function(e){try{e.select()}catch(e){}}),I.prototype.set=function(e,t){clearTimeout(this.id),this.id=setTimeout(t,e)};var F={toString:function(){return"CodeMirror.Pass"}},B={scroll:!1},R={origin:"*mouse"},j={origin:"+move"};function V(e,t,r){for(var n=0,i=0;;){var o=e.indexOf("\t",n);-1==o&&(o=e.length);var a=o-n;if(o==e.length||i+a>=t)return n+Math.min(a,t-i);if(i+=o-n,n=o+1,(i+=r-i%r)>=t)return n}}var K=[""];function G(e){for(;K.length<=e;)K.push(U(K)+" ");return K[e]}function U(e){return e[e.length-1]}function q(e,t){for(var r=[],n=0;n<e.length;n++)r[n]=t(e[n],n);return r}function $(){}function _(e,t){var r;return Object.create?r=Object.create(e):($.prototype=e,r=new $),t&&H(t,r),r}var X=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function Y(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||X.test(e))}function Z(e,t){return t?!!(t.source.indexOf("\\w")>-1&&Y(e))||t.test(e):Y(e)}function Q(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var J=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function ee(e){return e.charCodeAt(0)>=768&&J.test(e)}function te(e,t,n){var i=this;this.input=n,i.scrollbarFiller=M("div",null,"CodeMirror-scrollbar-filler"),i.scrollbarFiller.setAttribute("cm-not-content","true"),i.gutterFiller=M("div",null,"CodeMirror-gutter-filler"),i.gutterFiller.setAttribute("cm-not-content","true"),i.lineDiv=M("div",null,"CodeMirror-code"),i.selectionDiv=M("div",null,null,"position: relative; z-index: 1"),i.cursorDiv=M("div",null,"CodeMirror-cursors"),i.measure=M("div",null,"CodeMirror-measure"),i.lineMeasure=M("div",null,"CodeMirror-measure"),i.lineSpace=M("div",[i.measure,i.lineMeasure,i.selectionDiv,i.cursorDiv,i.lineDiv],null,"position: relative; outline: none"),i.mover=M("div",[M("div",[i.lineSpace],"CodeMirror-lines")],null,"position: relative"),i.sizer=M("div",[i.mover],"CodeMirror-sizer"),i.sizerWidth=null,i.heightForcer=M("div",null,null,"position: absolute; height: 30px; width: 1px;"),i.gutters=M("div",null,"CodeMirror-gutters"),i.lineGutter=null,i.scroller=M("div",[i.sizer,i.heightForcer,i.gutters],"CodeMirror-scroll"),i.scroller.setAttribute("tabIndex","-1"),i.wrapper=M("div",[i.scrollbarFiller,i.gutterFiller,i.scroller],"CodeMirror"),o&&a<8&&(i.gutters.style.zIndex=-1,i.scroller.style.paddingRight=0),l||r&&g||(i.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(i.wrapper):e(i.wrapper)),i.viewFrom=i.viewTo=t.first,i.reportedViewFrom=i.reportedViewTo=t.first,i.view=[],i.renderedView=null,i.externalMeasured=null,i.viewOffset=0,i.lastWrapHeight=i.lastWrapWidth=0,i.updateLineNumbers=null,i.nativeBarWidth=i.barHeight=i.barWidth=0,i.scrollbarsClipped=!1,i.lineNumWidth=i.lineNumInnerWidth=i.lineNumChars=null,i.alignWidgets=!1,i.cachedCharWidth=i.cachedTextHeight=i.cachedPaddingH=null,i.maxLine=null,i.maxLineLength=0,i.maxLineChanged=!1,i.wheelDX=i.wheelDY=i.wheelStartX=i.wheelStartY=null,i.shift=!1,i.selForContextMenu=null,i.activeTouch=null,n.init(i)}function re(e,t){if((t-=e.first)<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var r=e;!r.lines;)for(var n=0;;++n){var i=r.children[n],o=i.chunkSize();if(t<o){r=i;break}t-=o}return r.lines[t]}function ne(e,t,r){var n=[],i=t.line;return e.iter(t.line,r.line+1,(function(e){var o=e.text;i==r.line&&(o=o.slice(0,r.ch)),i==t.line&&(o=o.slice(t.ch)),n.push(o),++i})),n}function ie(e,t,r){var n=[];return e.iter(t,r,(function(e){n.push(e.text)})),n}function oe(e,t){var r=t-e.height;if(r)for(var n=e;n;n=n.parent)n.height+=r}function ae(e){if(null==e.parent)return null;for(var t=e.parent,r=E(t.lines,e),n=t.parent;n;t=n,n=n.parent)for(var i=0;n.children[i]!=t;++i)r+=n.children[i].chunkSize();return r+t.first}function le(e,t){var r=e.first;e:do{for(var n=0;n<e.children.length;++n){var i=e.children[n],o=i.height;if(t<o){e=i;continue e}t-=o,r+=i.chunkSize()}return r}while(!e.lines);for(var a=0;a<e.lines.length;++a){var l=e.lines[a].height;if(t<l)break;t-=l}return r+a}function se(e,t){return t>=e.first&&t<e.first+e.size}function ue(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function ce(e,t){if(!(this instanceof ce))return new ce(e,t);this.line=e,this.ch=t}function de(e,t){return e.line-t.line||e.ch-t.ch}function fe(e){return ce(e.line,e.ch)}function he(e,t){return de(e,t)<0?t:e}function pe(e,t){return de(e,t)<0?e:t}function ge(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function me(e,t){if(t.line<e.first)return ce(e.first,0);var r=e.first+e.size-1;return t.line>r?ce(r,re(e,r).text.length):function(e,t){var r=e.ch;return null==r||r>t?ce(e.line,t):r<0?ce(e.line,0):e}(t,re(e,t.line).text.length)}function ve(e,t){for(var r=[],n=0;n<t.length;n++)r[n]=me(e,t[n]);return r}var ye=!1,be=!1;function we(e,t,r){this.marker=e,this.from=t,this.to=r}function xe(e,t){if(e)for(var r=0;r<e.length;++r){var n=e[r];if(n.marker==t)return n}}function ke(e,t){for(var r,n=0;n<e.length;++n)e[n]!=t&&(r||(r=[])).push(e[n]);return r}function Ce(e,t){if(t.full)return null;var r=se(e,t.from.line)&&re(e,t.from.line).markedSpans,n=se(e,t.to.line)&&re(e,t.to.line).markedSpans;if(!r&&!n)return null;var i=t.from.ch,o=t.to.ch,a=0==de(t.from,t.to),l=function(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker;if(null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t)||o.from==t&&"bookmark"==a.type&&(!r||!o.marker.insertLeft)){var l=null==o.to||(a.inclusiveRight?o.to>=t:o.to>t);(n||(n=[])).push(new we(a,o.from,l?null:o.to))}}return n}(r,i,a),s=function(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker;if(null==o.to||(a.inclusiveRight?o.to>=t:o.to>t)||o.from==t&&"bookmark"==a.type&&(!r||o.marker.insertLeft)){var l=null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t);(n||(n=[])).push(new we(a,l?null:o.from-t,null==o.to?null:o.to-t))}}return n}(n,o,a),u=1==t.text.length,c=U(t.text).length+(u?i:0);if(l)for(var d=0;d<l.length;++d){var f=l[d];if(null==f.to){var h=xe(s,f.marker);h?u&&(f.to=null==h.to?null:h.to+c):f.to=i}}if(s)for(var p=0;p<s.length;++p){var g=s[p];if(null!=g.to&&(g.to+=c),null==g.from)xe(l,g.marker)||(g.from=c,u&&(l||(l=[])).push(g));else g.from+=c,u&&(l||(l=[])).push(g)}l&&(l=Se(l)),s&&s!=l&&(s=Se(s));var m=[l];if(!u){var v,y=t.text.length-2;if(y>0&&l)for(var b=0;b<l.length;++b)null==l[b].to&&(v||(v=[])).push(new we(l[b].marker,null,null));for(var w=0;w<y;++w)m.push(v);m.push(s)}return m}function Se(e){for(var t=0;t<e.length;++t){var r=e[t];null!=r.from&&r.from==r.to&&!1!==r.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function Le(e){var t=e.markedSpans;if(t){for(var r=0;r<t.length;++r)t[r].marker.detachLine(e);e.markedSpans=null}}function Te(e,t){if(t){for(var r=0;r<t.length;++r)t[r].marker.attachLine(e);e.markedSpans=t}}function Me(e){return e.inclusiveLeft?-1:0}function Ne(e){return e.inclusiveRight?1:0}function Ae(e,t){var r=e.lines.length-t.lines.length;if(0!=r)return r;var n=e.find(),i=t.find(),o=de(n.from,i.from)||Me(e)-Me(t);if(o)return-o;var a=de(n.to,i.to)||Ne(e)-Ne(t);return a||t.id-e.id}function Oe(e,t){var r,n=be&&e.markedSpans;if(n)for(var i=void 0,o=0;o<n.length;++o)(i=n[o]).marker.collapsed&&null==(t?i.from:i.to)&&(!r||Ae(r,i.marker)<0)&&(r=i.marker);return r}function We(e){return Oe(e,!0)}function ze(e){return Oe(e,!1)}function De(e,t,r,n,i){var o=re(e,t),a=be&&o.markedSpans;if(a)for(var l=0;l<a.length;++l){var s=a[l];if(s.marker.collapsed){var u=s.marker.find(0),c=de(u.from,r)||Me(s.marker)-Me(i),d=de(u.to,n)||Ne(s.marker)-Ne(i);if(!(c>=0&&d<=0||c<=0&&d>=0)&&(c<=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?de(u.to,r)>=0:de(u.to,r)>0)||c>=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?de(u.from,n)<=0:de(u.from,n)<0)))return!0}}}function He(e){for(var t;t=We(e);)e=t.find(-1,!0).line;return e}function Pe(e,t){var r=re(e,t),n=He(r);return r==n?t:ae(n)}function Ie(e,t){if(t>e.lastLine())return t;var r,n=re(e,t);if(!Ee(e,n))return t;for(;r=ze(n);)n=r.find(1,!0).line;return ae(n)+1}function Ee(e,t){var r=be&&t.markedSpans;if(r)for(var n=void 0,i=0;i<r.length;++i)if((n=r[i]).marker.collapsed){if(null==n.from)return!0;if(!n.marker.widgetNode&&0==n.from&&n.marker.inclusiveLeft&&Fe(e,t,n))return!0}}function Fe(e,t,r){if(null==r.to){var n=r.marker.find(1,!0);return Fe(e,n.line,xe(n.line.markedSpans,r.marker))}if(r.marker.inclusiveRight&&r.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if((i=t.markedSpans[o]).marker.collapsed&&!i.marker.widgetNode&&i.from==r.to&&(null==i.to||i.to!=r.from)&&(i.marker.inclusiveLeft||r.marker.inclusiveRight)&&Fe(e,t,i))return!0}function Be(e){for(var t=0,r=(e=He(e)).parent,n=0;n<r.lines.length;++n){var i=r.lines[n];if(i==e)break;t+=i.height}for(var o=r.parent;o;o=(r=o).parent)for(var a=0;a<o.children.length;++a){var l=o.children[a];if(l==r)break;t+=l.height}return t}function Re(e){if(0==e.height)return 0;for(var t,r=e.text.length,n=e;t=We(n);){var i=t.find(0,!0);n=i.from.line,r+=i.from.ch-i.to.ch}for(n=e;t=ze(n);){var o=t.find(0,!0);r-=n.text.length-o.from.ch,r+=(n=o.to.line).text.length-o.to.ch}return r}function je(e){var t=e.display,r=e.doc;t.maxLine=re(r,r.first),t.maxLineLength=Re(t.maxLine),t.maxLineChanged=!0,r.iter((function(e){var r=Re(e);r>t.maxLineLength&&(t.maxLineLength=r,t.maxLine=e)}))}function Ve(e){return e.level%2?e.to:e.from}function Ke(e){return e.level%2?e.from:e.to}function Ge(e){var t=Je(e);return t?Ve(t[0]):0}function Ue(e){var t=Je(e);return t?Ke(U(t)):e.text.length}function qe(e,t,r){var n=e[0].level;return t==n||r!=n&&t<r}var $e=null;function _e(e,t){var r;$e=null;for(var n=0;n<e.length;++n){var i=e[n];if(i.from<t&&i.to>t)return n;if(i.from==t||i.to==t){if(null!=r)return qe(e,i.level,e[r].level)?(i.from!=i.to&&($e=r),n):(i.from!=i.to&&($e=n),r);r=n}}return r}function Xe(e,t,r,n){if(!n)return t+r;do{t+=r}while(t>0&&ee(e.text.charAt(t)));return t}function Ye(e,t,r,n){var i=Je(e);if(!i)return Ze(e,t,r,n);for(var o=_e(i,t),a=i[o],l=Xe(e,t,a.level%2?-r:r,n);;){if(l>a.from&&l<a.to)return l;if(l==a.from||l==a.to)return _e(i,l)==o?l:r>0==(a=i[o+=r]).level%2?a.to:a.from;if(!(a=i[o+=r]))return null;l=r>0==a.level%2?Xe(e,a.to,-1,n):Xe(e,a.from,1,n)}}function Ze(e,t,r,n){var i=t+r;if(n)for(;i>0&&ee(e.text.charAt(i));)i+=r;return i<0||i>e.text.length?null:i}var Qe=function(){var e=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,t=/[stwN]/,r=/[LRr]/,n=/[Lb1n]/,i=/[1n]/,o="L";function a(e,t,r){this.level=e,this.from=t,this.to=r}return function(l){if(!e.test(l))return!1;for(var s,u=l.length,c=[],d=0;d<u;++d)c.push((s=l.charCodeAt(d))<=247?"bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN".charAt(s):1424<=s&&s<=1524?"R":1536<=s&&s<=1773?"rrrrrrrrrrrr,rNNmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmrrrrrrrnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmNmmmm".charAt(s-1536):1774<=s&&s<=2220?"r":8192<=s&&s<=8203?"w":8204==s?"b":"L");for(var f=0,h=o;f<u;++f){var p=c[f];"m"==p?c[f]=h:h=p}for(var g=0,m=o;g<u;++g){var v=c[g];"1"==v&&"r"==m?c[g]="n":r.test(v)&&(m=v,"r"==v&&(c[g]="R"))}for(var y=1,b=c[0];y<u-1;++y){var w=c[y];"+"==w&&"1"==b&&"1"==c[y+1]?c[y]="1":","!=w||b!=c[y+1]||"1"!=b&&"n"!=b||(c[y]=b),b=w}for(var x=0;x<u;++x){var k=c[x];if(","==k)c[x]="N";else if("%"==k){var C=void 0;for(C=x+1;C<u&&"%"==c[C];++C);for(var S=x&&"!"==c[x-1]||C<u&&"1"==c[C]?"1":"N",L=x;L<C;++L)c[L]=S;x=C-1}}for(var T=0,M=o;T<u;++T){var N=c[T];"L"==M&&"1"==N?c[T]="L":r.test(N)&&(M=N)}for(var A=0;A<u;++A)if(t.test(c[A])){var O=void 0;for(O=A+1;O<u&&t.test(c[O]);++O);for(var W="L"==(A?c[A-1]:o),z="L"==(O<u?c[O]:o),D=W||z?"L":"R",H=A;H<O;++H)c[H]=D;A=O-1}for(var P,I=[],E=0;E<u;)if(n.test(c[E])){var F=E;for(++E;E<u&&n.test(c[E]);++E);I.push(new a(0,F,E))}else{var B=E,R=I.length;for(++E;E<u&&"L"!=c[E];++E);for(var j=B;j<E;)if(i.test(c[j])){B<j&&I.splice(R,0,new a(1,B,j));var V=j;for(++j;j<E&&i.test(c[j]);++j);I.splice(R,0,new a(2,V,j)),B=j}else++j;B<E&&I.splice(R,0,new a(1,B,E))}return 1==I[0].level&&(P=l.match(/^\s+/))&&(I[0].from=P[0].length,I.unshift(new a(0,0,P[0].length))),1==U(I).level&&(P=l.match(/\s+$/))&&(U(I).to-=P[0].length,I.push(new a(0,u-P[0].length,u))),2==I[0].level&&I.unshift(new a(1,I[0].to,I[0].to)),I[0].level!=U(I).level&&I.push(new a(I[0].level,u,u)),I}}();function Je(e){var t=e.order;return null==t&&(t=e.order=Qe(e.text)),t}var et=[],tt=function(e,t,r){if(e.addEventListener)e.addEventListener(t,r,!1);else if(e.attachEvent)e.attachEvent("on"+t,r);else{var n=e._handlers||(e._handlers={});n[t]=(n[t]||et).concat(r)}};function rt(e,t){return e._handlers&&e._handlers[t]||et}function nt(e,t,r){if(e.removeEventListener)e.removeEventListener(t,r,!1);else if(e.detachEvent)e.detachEvent("on"+t,r);else{var n=e._handlers,i=n&&n[t];if(i){var o=E(i,r);o>-1&&(n[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function it(e,t){var r=rt(e,t);if(r.length)for(var n=Array.prototype.slice.call(arguments,2),i=0;i<r.length;++i)r[i].apply(null,n)}function ot(e,t,r){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),it(e,r||t.type,e,t),dt(t)||t.codemirrorIgnore}function at(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var r=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),n=0;n<t.length;++n)-1==E(r,t[n])&&r.push(t[n])}function lt(e,t){return rt(e,t).length>0}function st(e){e.prototype.on=function(e,t){tt(this,e,t)},e.prototype.off=function(e,t){nt(this,e,t)}}function ut(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function ct(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function dt(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function ft(e){ut(e),ct(e)}function ht(e){return e.target||e.srcElement}function pt(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),m&&e.ctrlKey&&1==t&&(t=3),t}var gt,mt,vt=function(){if(o&&a<9)return!1;var e=M("div");return"draggable"in e||"dragDrop"in e}();function yt(e){if(null==gt){var t=M("span","​");T(e,M("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(gt=t.offsetWidth<=1&&t.offsetHeight>2&&!(o&&a<8))}var r=gt?M("span","​"):M("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return r.setAttribute("cm-text",""),r}function bt(e){if(null!=mt)return mt;var t=T(e,document.createTextNode("AخA")),r=C(t,0,1).getBoundingClientRect(),n=C(t,1,2).getBoundingClientRect();return L(e),!(!r||r.left==r.right)&&(mt=n.right-r.right<3)}var wt,xt=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,r=[],n=e.length;t<=n;){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),a=o.indexOf("\r");-1!=a?(r.push(o.slice(0,a)),t+=a+1):(r.push(o),t=i+1)}return r}:function(e){return e.split(/\r\n?|\n/)},kt=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(e){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(e){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},Ct="oncopy"in(wt=M("div"))||(wt.setAttribute("oncopy","return;"),"function"==typeof wt.oncopy),St=null;var Lt={},Tt={};function Mt(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),Lt[e]=t}function Nt(e){if("string"==typeof e&&Tt.hasOwnProperty(e))e=Tt[e];else if(e&&"string"==typeof e.name&&Tt.hasOwnProperty(e.name)){var t=Tt[e.name];"string"==typeof t&&(t={name:t}),(e=_(t,e)).name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return Nt("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return Nt("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function At(e,t){t=Nt(t);var r=Lt[t.name];if(!r)return At(e,"text/plain");var n=r(e,t);if(Ot.hasOwnProperty(t.name)){var i=Ot[t.name];for(var o in i)i.hasOwnProperty(o)&&(n.hasOwnProperty(o)&&(n["_"+o]=n[o]),n[o]=i[o])}if(n.name=t.name,t.helperType&&(n.helperType=t.helperType),t.modeProps)for(var a in t.modeProps)n[a]=t.modeProps[a];return n}var Ot={};function Wt(e,t){H(t,Ot.hasOwnProperty(e)?Ot[e]:Ot[e]={})}function zt(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var r={};for(var n in t){var i=t[n];i instanceof Array&&(i=i.concat([])),r[n]=i}return r}function Dt(e,t){for(var r;e.innerMode&&(r=e.innerMode(t))&&r.mode!=e;)t=r.state,e=r.mode;return r||{mode:e,state:t}}function Ht(e,t,r){return!e.startState||e.startState(t,r)}var Pt=function(e,t){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0};function It(e,t,r,n){var i=[e.state.modeGen],o={};Gt(e,t.text,e.doc.mode,r,(function(e,t){return i.push(e,t)}),o,n);for(var a=function(r){var n=e.state.overlays[r],a=1,l=0;Gt(e,t.text,n.mode,!0,(function(e,t){for(var r=a;l<e;){var o=i[a];o>e&&i.splice(a,1,e,i[a+1],o),a+=2,l=Math.min(e,o)}if(t)if(n.opaque)i.splice(r,a-r,e,"overlay "+t),a=r+2;else for(;r<a;r+=2){var s=i[r+1];i[r+1]=(s?s+" ":"")+"overlay "+t}}),o)},l=0;l<e.state.overlays.length;++l)a(l);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function Et(e,t,r){if(!t.styles||t.styles[0]!=e.state.modeGen){var n=Ft(e,ae(t)),i=It(e,t,t.text.length>e.options.maxHighlightLength?zt(e.doc.mode,n):n);t.stateAfter=n,t.styles=i.styles,i.classes?t.styleClasses=i.classes:t.styleClasses&&(t.styleClasses=null),r===e.doc.frontier&&e.doc.frontier++}return t.styles}function Ft(e,t,r){var n=e.doc,i=e.display;if(!n.mode.startState)return!0;var o=function(e,t,r){for(var n,i,o=e.doc,a=r?-1:t-(e.doc.mode.innerMode?1e3:100),l=t;l>a;--l){if(l<=o.first)return o.first;var s=re(o,l-1);if(s.stateAfter&&(!r||l<=o.frontier))return l;var u=P(s.text,null,e.options.tabSize);(null==i||n>u)&&(i=l-1,n=u)}return i}(e,t,r),a=o>n.first&&re(n,o-1).stateAfter;return a=a?zt(n.mode,a):Ht(n.mode),n.iter(o,t,(function(r){Bt(e,r.text,a);var l=o==t-1||o%5==0||o>=i.viewFrom&&o<i.viewTo;r.stateAfter=l?zt(n.mode,a):null,++o})),r&&(n.frontier=o),a}function Bt(e,t,r,n){var i=e.doc.mode,o=new Pt(t,e.options.tabSize);for(o.start=o.pos=n||0,""==t&&Rt(i,r);!o.eol();)jt(i,o,r),o.start=o.pos}function Rt(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var r=Dt(e,t);return r.mode.blankLine?r.mode.blankLine(r.state):void 0}}function jt(e,t,r,n){for(var i=0;i<10;i++){n&&(n[0]=Dt(e,r).mode);var o=e.token(t,r);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}function Vt(e,t,r,n){var i,o=function(e){return{start:d.start,end:d.pos,string:d.current(),type:i||null,state:e?zt(a.mode,c):c}},a=e.doc,l=a.mode;t=me(a,t);var s,u=re(a,t.line),c=Ft(e,t.line,r),d=new Pt(u.text,e.options.tabSize);for(n&&(s=[]);(n||d.pos<t.ch)&&!d.eol();)d.start=d.pos,i=jt(l,d,c),n&&s.push(o(!0));return n?s:o()}function Kt(e,t){if(e)for(;;){var r=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!r)break;e=e.slice(0,r.index)+e.slice(r.index+r[0].length);var n=r[1]?"bgClass":"textClass";null==t[n]?t[n]=r[2]:new RegExp("(?:^|s)"+r[2]+"(?:$|s)").test(t[n])||(t[n]+=" "+r[2])}return e}function Gt(e,t,r,n,i,o,a){var l=r.flattenSpans;null==l&&(l=e.options.flattenSpans);var s,u=0,c=null,d=new Pt(t,e.options.tabSize),f=e.options.addModeClass&&[null];for(""==t&&Kt(Rt(r,n),o);!d.eol();){if(d.pos>e.options.maxHighlightLength?(l=!1,a&&Bt(e,t,n,d.pos),d.pos=t.length,s=null):s=Kt(jt(r,d,n,f),o),f){var h=f[0].name;h&&(s="m-"+(s?h+" "+s:h))}if(!l||c!=s){for(;u<d.start;)i(u=Math.min(d.start,u+5e3),c);c=s}d.start=d.pos}for(;u<d.pos;){var p=Math.min(d.pos,u+5e3);i(p,c),u=p}}function Ut(e,t,r){this.text=e,Te(this,t),this.height=r?r(this):1}function qt(e){e.parent=null,Le(e)}Pt.prototype={eol:function(){return this.pos>=this.string.length},sol:function(){return this.pos==this.lineStart},peek:function(){return this.string.charAt(this.pos)||void 0},next:function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},eat:function(e){var t=this.string.charAt(this.pos);if("string"==typeof e?t==e:t&&(e.test?e.test(t):e(t)))return++this.pos,t},eatWhile:function(e){for(var t=this.pos;this.eat(e););return this.pos>t},eatSpace:function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},skipToEnd:function(){this.pos=this.string.length},skipTo:function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},backUp:function(e){this.pos-=e},column:function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=P(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?P(this.string,this.lineStart,this.tabSize):0)},indentation:function(){return P(this.string,null,this.tabSize)-(this.lineStart?P(this.string,this.lineStart,this.tabSize):0)},match:function(e,t,r){if("string"!=typeof e){var n=this.string.slice(this.pos).match(e);return n&&n.index>0?null:(n&&!1!==t&&(this.pos+=n[0].length),n)}var i=function(e){return r?e.toLowerCase():e};if(i(this.string.substr(this.pos,e.length))==i(e))return!1!==t&&(this.pos+=e.length),!0},current:function(){return this.string.slice(this.start,this.pos)},hideFirstChars:function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}}},st(Ut),Ut.prototype.lineNo=function(){return ae(this)};var $t={},_t={};function Xt(e,t){if(!e||/^\s*$/.test(e))return null;var r=t.addModeClass?_t:$t;return r[e]||(r[e]=e.replace(/\S+/g,"cm-$&"))}function Yt(e,t){var r=M("span",null,null,l?"padding-right: .1px":null),n={pre:M("pre",[r],"CodeMirror-line"),content:r,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:(o||l)&&e.getOption("lineWrapping")};t.measure={};for(var i=0;i<=(t.rest?t.rest.length:0);i++){var a=i?t.rest[i-1]:t.line,s=void 0;n.pos=0,n.addToken=Qt,bt(e.display.measure)&&(s=Je(a))&&(n.addToken=Jt(n.addToken,s)),n.map=[],tr(a,n,Et(e,a,t!=e.display.externalMeasured&&ae(a))),a.styleClasses&&(a.styleClasses.bgClass&&(n.bgClass=W(a.styleClasses.bgClass,n.bgClass||"")),a.styleClasses.textClass&&(n.textClass=W(a.styleClasses.textClass,n.textClass||""))),0==n.map.length&&n.map.push(0,0,n.content.appendChild(yt(e.display.measure))),0==i?(t.measure.map=n.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(n.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(l){var u=n.content.lastChild;(/\bcm-tab\b/.test(u.className)||u.querySelector&&u.querySelector(".cm-tab"))&&(n.content.className="cm-tab-wrap-hack")}return it(e,"renderLine",e,t.line,n.pre),n.pre.className&&(n.textClass=W(n.pre.className,n.textClass||"")),n}function Zt(e){var t=M("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function Qt(e,t,r,n,i,l,s){if(t){var u,c=e.splitSpaces?function(e,t){if(e.length>1&&!/  /.test(e))return e;for(var r=t,n="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!r||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),n+=o,r=" "==o}return n}(t,e.trailingSpace):t,d=e.cm.state.specialChars,f=!1;if(d.test(t)){u=document.createDocumentFragment();for(var h=0;;){d.lastIndex=h;var p=d.exec(t),g=p?p.index-h:t.length-h;if(g){var m=document.createTextNode(c.slice(h,h+g));o&&a<9?u.appendChild(M("span",[m])):u.appendChild(m),e.map.push(e.pos,e.pos+g,m),e.col+=g,e.pos+=g}if(!p)break;h+=g+1;var v=void 0;if("\t"==p[0]){var y=e.cm.options.tabSize,b=y-e.col%y;(v=u.appendChild(M("span",G(b),"cm-tab"))).setAttribute("role","presentation"),v.setAttribute("cm-text","\t"),e.col+=b}else"\r"==p[0]||"\n"==p[0]?((v=u.appendChild(M("span","\r"==p[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",p[0]),e.col+=1):((v=e.cm.options.specialCharPlaceholder(p[0])).setAttribute("cm-text",p[0]),o&&a<9?u.appendChild(M("span",[v])):u.appendChild(v),e.col+=1);e.map.push(e.pos,e.pos+1,v),e.pos++}}else e.col+=t.length,u=document.createTextNode(c),e.map.push(e.pos,e.pos+t.length,u),o&&a<9&&(f=!0),e.pos+=t.length;if(e.trailingSpace=32==c.charCodeAt(t.length-1),r||n||i||f||s){var w=r||"";n&&(w+=n),i&&(w+=i);var x=M("span",[u],w,s);return l&&(x.title=l),e.content.appendChild(x)}e.content.appendChild(u)}}function Jt(e,t){return function(r,n,i,o,a,l,s){i=i?i+" cm-force-border":"cm-force-border";for(var u=r.pos,c=u+n.length;;){for(var d=void 0,f=0;f<t.length&&!((d=t[f]).to>u&&d.from<=u);f++);if(d.to>=c)return e(r,n,i,o,a,l,s);e(r,n.slice(0,d.to-u),i,o,null,l,s),o=null,n=n.slice(d.to-u),u=d.to}}}function er(e,t,r,n){var i=!n&&r.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!n&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",r.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function tr(e,t,r){var n=e.markedSpans,i=e.text,o=0;if(n)for(var a,l,s,u,c,d,f,h=i.length,p=0,g=1,m="",v=0;;){if(v==p){s=u=c=d=l="",f=null,v=1/0;for(var y=[],b=void 0,w=0;w<n.length;++w){var x=n[w],k=x.marker;"bookmark"==k.type&&x.from==p&&k.widgetNode?y.push(k):x.from<=p&&(null==x.to||x.to>p||k.collapsed&&x.to==p&&x.from==p)?(null!=x.to&&x.to!=p&&v>x.to&&(v=x.to,u=""),k.className&&(s+=" "+k.className),k.css&&(l=(l?l+";":"")+k.css),k.startStyle&&x.from==p&&(c+=" "+k.startStyle),k.endStyle&&x.to==v&&(b||(b=[])).push(k.endStyle,x.to),k.title&&!d&&(d=k.title),k.collapsed&&(!f||Ae(f.marker,k)<0)&&(f=x)):x.from>p&&v>x.from&&(v=x.from)}if(b)for(var C=0;C<b.length;C+=2)b[C+1]==v&&(u+=" "+b[C]);if(!f||f.from==p)for(var S=0;S<y.length;++S)er(t,0,y[S]);if(f&&(f.from||0)==p){if(er(t,(null==f.to?h+1:f.to)-p,f.marker,null==f.from),null==f.to)return;f.to==p&&(f=!1)}}if(p>=h)break;for(var L=Math.min(h,v);;){if(m){var T=p+m.length;if(!f){var M=T>L?m.slice(0,L-p):m;t.addToken(t,M,a?a+s:s,c,p+M.length==v?u:"",d,l)}if(T>=L){m=m.slice(L-p),p=L;break}p=T,c=""}m=i.slice(o,o=r[g++]),a=Xt(r[g++],t.cm.options)}}else for(var N=1;N<r.length;N+=2)t.addToken(t,i.slice(o,o=r[N]),Xt(r[N+1],t.cm.options))}function rr(e,t,r){this.line=t,this.rest=function(e){for(var t,r;t=ze(e);)e=t.find(1,!0).line,(r||(r=[])).push(e);return r}(t),this.size=this.rest?ae(U(this.rest))-r+1:1,this.node=this.text=null,this.hidden=Ee(e,t)}function nr(e,t,r){for(var n,i=[],o=t;o<r;o=n){var a=new rr(e.doc,re(e.doc,o),o);n=o+a.size,i.push(a)}return i}var ir=null;var or=null;function ar(e,t){var r=rt(e,t);if(r.length){var n,i=Array.prototype.slice.call(arguments,2);ir?n=ir.delayedCallbacks:or?n=or:(n=or=[],setTimeout(lr,0));for(var o=function(e){n.push((function(){return r[e].apply(null,i)}))},a=0;a<r.length;++a)o(a)}}function lr(){var e=or;or=null;for(var t=0;t<e.length;++t)e[t]()}function sr(e,t,r,n){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?dr(e,t):"gutter"==o?hr(e,t,r,n):"class"==o?fr(t):"widget"==o&&pr(e,t,n)}t.changes=null}function ur(e){return e.node==e.text&&(e.node=M("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),o&&a<8&&(e.node.style.zIndex=2)),e.node}function cr(e,t){var r=e.display.externalMeasured;return r&&r.line==t.line?(e.display.externalMeasured=null,t.measure=r.measure,r.built):Yt(e,t)}function dr(e,t){var r=t.text.className,n=cr(e,t);t.text==t.node&&(t.node=n.pre),t.text.parentNode.replaceChild(n.pre,t.text),t.text=n.pre,n.bgClass!=t.bgClass||n.textClass!=t.textClass?(t.bgClass=n.bgClass,t.textClass=n.textClass,fr(t)):r&&(t.text.className=r)}function fr(e){!function(e){var t=e.bgClass?e.bgClass+" "+(e.line.bgClass||""):e.line.bgClass;if(t&&(t+=" CodeMirror-linebackground"),e.background)t?e.background.className=t:(e.background.parentNode.removeChild(e.background),e.background=null);else if(t){var r=ur(e);e.background=r.insertBefore(M("div",null,t),r.firstChild)}}(e),e.line.wrapClass?ur(e).className=e.line.wrapClass:e.node!=e.text&&(e.node.className="");var t=e.textClass?e.textClass+" "+(e.line.textClass||""):e.line.textClass;e.text.className=t||""}function hr(e,t,r,n){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var i=ur(t);t.gutterBackground=M("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px; width: "+n.gutterTotalWidth+"px"),i.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var a=ur(t),l=t.gutter=M("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px");if(e.display.input.setUneditable(l),a.insertBefore(l,t.text),t.line.gutterClass&&(l.className+=" "+t.line.gutterClass),!e.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(t.lineNumber=l.appendChild(M("div",ue(e.options,r),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+n.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var s=0;s<e.options.gutters.length;++s){var u=e.options.gutters[s],c=o.hasOwnProperty(u)&&o[u];c&&l.appendChild(M("div",[c],"CodeMirror-gutter-elt","left: "+n.gutterLeft[u]+"px; width: "+n.gutterWidth[u]+"px"))}}}function pr(e,t,r){t.alignable&&(t.alignable=null);for(var n=t.node.firstChild,i=void 0;n;n=i)i=n.nextSibling,"CodeMirror-linewidget"==n.className&&t.node.removeChild(n);mr(e,t,r)}function gr(e,t,r,n){var i=cr(e,t);return t.text=t.node=i.pre,i.bgClass&&(t.bgClass=i.bgClass),i.textClass&&(t.textClass=i.textClass),fr(t),hr(e,t,r,n),mr(e,t,n),t.node}function mr(e,t,r){if(vr(e,t.line,t,r,!0),t.rest)for(var n=0;n<t.rest.length;n++)vr(e,t.rest[n],t,r,!1)}function vr(e,t,r,n,i){if(t.widgets)for(var o=ur(r),a=0,l=t.widgets;a<l.length;++a){var s=l[a],u=M("div",[s.node],"CodeMirror-linewidget");s.handleMouseEvents||u.setAttribute("cm-ignore-events","true"),yr(s,u,r,n),e.display.input.setUneditable(u),i&&s.above?o.insertBefore(u,r.gutter||r.text):o.appendChild(u),ar(s,"redraw")}}function yr(e,t,r,n){if(e.noHScroll){(r.alignable||(r.alignable=[])).push(t);var i=n.wrapperWidth;t.style.left=n.fixedPos+"px",e.coverGutter||(i-=n.gutterTotalWidth,t.style.paddingLeft=n.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-n.gutterTotalWidth+"px"))}function br(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!N(document.body,e.node)){var r="position: relative;";e.coverGutter&&(r+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(r+="width: "+t.display.wrapper.clientWidth+"px;"),T(t.display.measure,M("div",[e.node],null,r))}return e.height=e.node.parentNode.offsetHeight}function wr(e,t){for(var r=ht(t);r!=e.wrapper;r=r.parentNode)if(!r||1==r.nodeType&&"true"==r.getAttribute("cm-ignore-events")||r.parentNode==e.sizer&&r!=e.mover)return!0}function xr(e){return e.lineSpace.offsetTop}function kr(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function Cr(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=T(e.measure,M("pre","x")),r=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,n={left:parseInt(r.paddingLeft),right:parseInt(r.paddingRight)};return isNaN(n.left)||isNaN(n.right)||(e.cachedPaddingH=n),n}function Sr(e){return 30-e.display.nativeBarWidth}function Lr(e){return e.display.scroller.clientWidth-Sr(e)-e.display.barWidth}function Tr(e){return e.display.scroller.clientHeight-Sr(e)-e.display.barHeight}function Mr(e,t,r){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};for(var n=0;n<e.rest.length;n++)if(e.rest[n]==t)return{map:e.measure.maps[n],cache:e.measure.caches[n]};for(var i=0;i<e.rest.length;i++)if(ae(e.rest[i])>r)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}function Nr(e,t,r,n){return Wr(e,Or(e,t),r,n)}function Ar(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[rn(e,t)];var r=e.display.externalMeasured;return r&&t>=r.lineN&&t<r.lineN+r.size?r:void 0}function Or(e,t){var r=ae(t),n=Ar(e,r);n&&!n.text?n=null:n&&n.changes&&(sr(e,n,r,Zr(e)),e.curOp.forceUpdate=!0),n||(n=function(e,t){var r=ae(t=He(t)),n=e.display.externalMeasured=new rr(e.doc,t,r);n.lineN=r;var i=n.built=Yt(e,n);return n.text=i.pre,T(e.display.lineMeasure,i.pre),n}(e,t));var i=Mr(n,t,r);return{line:t,view:n,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function Wr(e,t,r,n,i){t.before&&(r=-1);var l,s=r+(n||"");return t.cache.hasOwnProperty(s)?l=t.cache[s]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(!function(e,t,r){var n=e.options.lineWrapping,i=n&&Lr(e);if(!t.measure.heights||n&&t.measure.width!=i){var o=t.measure.heights=[];if(n){t.measure.width=i;for(var a=t.text.firstChild.getClientRects(),l=0;l<a.length-1;l++){var s=a[l],u=a[l+1];Math.abs(s.bottom-u.bottom)>2&&o.push((s.bottom+u.top)/2-r.top)}}o.push(r.bottom-r.top)}}(e,t.view,t.rect),t.hasHeights=!0),(l=function(e,t,r,n){var i,l=Hr(t.map,r,n),s=l.node,u=l.start,c=l.end,d=l.collapse;if(3==s.nodeType){for(var f=0;f<4;f++){for(;u&&ee(t.line.text.charAt(l.coverStart+u));)--u;for(;l.coverStart+c<l.coverEnd&&ee(t.line.text.charAt(l.coverStart+c));)++c;if((i=o&&a<9&&0==u&&c==l.coverEnd-l.coverStart?s.parentNode.getBoundingClientRect():Pr(C(s,u,c).getClientRects(),n)).left||i.right||0==u)break;c=u,u-=1,d="right"}o&&a<11&&(i=function(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!function(e){if(null!=St)return St;var t=T(e,M("span","x")),r=t.getBoundingClientRect(),n=C(t,0,1).getBoundingClientRect();return St=Math.abs(r.left-n.left)>1}(e))return t;var r=screen.logicalXDPI/screen.deviceXDPI,n=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*r,right:t.right*r,top:t.top*n,bottom:t.bottom*n}}(e.display.measure,i))}else{var h;u>0&&(d=n="right"),i=e.options.lineWrapping&&(h=s.getClientRects()).length>1?h["right"==n?h.length-1:0]:s.getBoundingClientRect()}if(o&&a<9&&!u&&(!i||!i.left&&!i.right)){var p=s.parentNode.getClientRects()[0];i=p?{left:p.left,right:p.left+Yr(e.display),top:p.top,bottom:p.bottom}:Dr}for(var g=i.top-t.rect.top,m=i.bottom-t.rect.top,v=(g+m)/2,y=t.view.measure.heights,b=0;b<y.length-1&&!(v<y[b]);b++);var w=b?y[b-1]:0,x=y[b],k={left:("right"==d?i.right:i.left)-t.rect.left,right:("left"==d?i.left:i.right)-t.rect.left,top:w,bottom:x};i.left||i.right||(k.bogus=!0);e.options.singleCursorHeightPerLine||(k.rtop=g,k.rbottom=m);return k}(e,t,r,n)).bogus||(t.cache[s]=l)),{left:l.left,right:l.right,top:i?l.rtop:l.top,bottom:i?l.rbottom:l.bottom}}var zr,Dr={left:0,right:0,top:0,bottom:0};function Hr(e,t,r){for(var n,i,o,a,l,s,u=0;u<e.length;u+=3)if(l=e[u],s=e[u+1],t<l?(i=0,o=1,a="left"):t<s?o=(i=t-l)+1:(u==e.length-3||t==s&&e[u+3]>t)&&(i=(o=s-l)-1,t>=s&&(a="right")),null!=i){if(n=e[u+2],l==s&&r==(n.insertLeft?"left":"right")&&(a=r),"left"==r&&0==i)for(;u&&e[u-2]==e[u-3]&&e[u-1].insertLeft;)n=e[2+(u-=3)],a="left";if("right"==r&&i==s-l)for(;u<e.length-3&&e[u+3]==e[u+4]&&!e[u+5].insertLeft;)n=e[(u+=3)+2],a="right";break}return{node:n,start:i,end:o,collapse:a,coverStart:l,coverEnd:s}}function Pr(e,t){var r=Dr;if("left"==t)for(var n=0;n<e.length&&(r=e[n]).left==r.right;n++);else for(var i=e.length-1;i>=0&&(r=e[i]).left==r.right;i--);return r}function Ir(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function Er(e){e.display.externalMeasure=null,L(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)Ir(e.display.view[t])}function Fr(e){Er(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function Br(){return window.pageXOffset||(document.documentElement||document.body).scrollLeft}function Rr(){return window.pageYOffset||(document.documentElement||document.body).scrollTop}function jr(e,t,r,n,i){if(!i&&t.widgets)for(var o=0;o<t.widgets.length;++o)if(t.widgets[o].above){var a=br(t.widgets[o]);r.top+=a,r.bottom+=a}if("line"==n)return r;n||(n="local");var l=Be(t);if("local"==n?l+=xr(e.display):l-=e.display.viewOffset,"page"==n||"window"==n){var s=e.display.lineSpace.getBoundingClientRect();l+=s.top+("window"==n?0:Rr());var u=s.left+("window"==n?0:Br());r.left+=u,r.right+=u}return r.top+=l,r.bottom+=l,r}function Vr(e,t,r){if("div"==r)return t;var n=t.left,i=t.top;if("page"==r)n-=Br(),i-=Rr();else if("local"==r||!r){var o=e.display.sizer.getBoundingClientRect();n+=o.left,i+=o.top}var a=e.display.lineSpace.getBoundingClientRect();return{left:n-a.left,top:i-a.top}}function Kr(e,t,r,n,i){return n||(n=re(e.doc,t.line)),jr(e,n,Nr(e,n,t.ch,i),r)}function Gr(e,t,r,n,i,o){function a(t,a){var l=Wr(e,i,t,a?"right":"left",o);return a?l.left=l.right:l.right=l.left,jr(e,n,l,r)}function l(e,t){var r=s[t],n=r.level%2;return e==Ve(r)&&t&&r.level<s[t-1].level?(e=Ke(r=s[--t])-(r.level%2?0:1),n=!0):e==Ke(r)&&t<s.length-1&&r.level<s[t+1].level&&(e=Ve(r=s[++t])-r.level%2,n=!1),n&&e==r.to&&e>r.from?a(e-1):a(e,n)}n=n||re(e.doc,t.line),i||(i=Or(e,n));var s=Je(n),u=t.ch;if(!s)return a(u);var c=l(u,_e(s,u));return null!=$e&&(c.other=l(u,$e)),c}function Ur(e,t){var r=0;t=me(e.doc,t),e.options.lineWrapping||(r=Yr(e.display)*t.ch);var n=re(e.doc,t.line),i=Be(n)+xr(e.display);return{left:r,right:r,top:i,bottom:i+n.height}}function qr(e,t,r,n){var i=ce(e,t);return i.xRel=n,r&&(i.outside=!0),i}function $r(e,t,r){var n=e.doc;if((r+=e.display.viewOffset)<0)return qr(n.first,0,!0,-1);var i=le(n,r),o=n.first+n.size-1;if(i>o)return qr(n.first+n.size-1,re(n,o).text.length,!0,1);t<0&&(t=0);for(var a=re(n,i);;){var l=_r(e,a,i,t,r),s=ze(a),u=s&&s.find(0,!0);if(!s||!(l.ch>u.from.ch||l.ch==u.from.ch&&l.xRel>0))return l;i=ae(a=u.to.line)}}function _r(e,t,r,n,i){var o=i-Be(t),a=!1,l=2*e.display.wrapper.clientWidth,s=Or(e,t);function u(n){var i=Gr(e,ce(r,n),"line",t,s);return a=!0,o>i.bottom?i.left-l:o<i.top?i.left+l:(a=!1,i.left)}var c=Je(t),d=t.text.length,f=Ge(t),h=Ue(t),p=u(f),g=a,m=u(h),v=a;if(n>m)return qr(r,h,v,1);for(;;){if(c?h==f||h==Ye(t,f,1):h-f<=1){var y=n<p||n-p<=m-n?f:h,b=y==f?g:v,w=n-(y==f?p:m);if(v&&!c&&!/\s/.test(t.text.charAt(y))&&w>0&&y<t.text.length&&s.view.measure.heights.length>1){var x=Wr(e,s,y,"right");o<=x.bottom&&o>=x.top&&Math.abs(n-x.right)<w&&(b=!1,y++,w=n-x.right)}for(;ee(t.text.charAt(y));)++y;return qr(r,y,b,w<-1?-1:w>1?1:0)}var k=Math.ceil(d/2),C=f+k;if(c){C=f;for(var S=0;S<k;++S)C=Ye(t,C,1)}var L=u(C);L>n?(h=C,m=L,(v=a)&&(m+=1e3),d=k):(f=C,p=L,g=a,d-=k)}}function Xr(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==zr){zr=M("pre");for(var t=0;t<49;++t)zr.appendChild(document.createTextNode("x")),zr.appendChild(M("br"));zr.appendChild(document.createTextNode("x"))}T(e.measure,zr);var r=zr.offsetHeight/50;return r>3&&(e.cachedTextHeight=r),L(e.measure),r||1}function Yr(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=M("span","xxxxxxxxxx"),r=M("pre",[t]);T(e.measure,r);var n=t.getBoundingClientRect(),i=(n.right-n.left)/10;return i>2&&(e.cachedCharWidth=i),i||10}function Zr(e){for(var t=e.display,r={},n={},i=t.gutters.clientLeft,o=t.gutters.firstChild,a=0;o;o=o.nextSibling,++a)r[e.options.gutters[a]]=o.offsetLeft+o.clientLeft+i,n[e.options.gutters[a]]=o.clientWidth;return{fixedPos:Qr(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:r,gutterWidth:n,wrapperWidth:t.wrapper.clientWidth}}function Qr(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function Jr(e){var t=Xr(e.display),r=e.options.lineWrapping,n=r&&Math.max(5,e.display.scroller.clientWidth/Yr(e.display)-3);return function(i){if(Ee(e.doc,i))return 0;var o=0;if(i.widgets)for(var a=0;a<i.widgets.length;a++)i.widgets[a].height&&(o+=i.widgets[a].height);return r?o+(Math.ceil(i.text.length/n)||1)*t:o+t}}function en(e){var t=e.doc,r=Jr(e);t.iter((function(e){var t=r(e);t!=e.height&&oe(e,t)}))}function tn(e,t,r,n){var i=e.display;if(!r&&"true"==ht(t).getAttribute("cm-not-content"))return null;var o,a,l=i.lineSpace.getBoundingClientRect();try{o=t.clientX-l.left,a=t.clientY-l.top}catch(t){return null}var s,u=$r(e,o,a);if(n&&1==u.xRel&&(s=re(e.doc,u.line).text).length==u.ch){var c=P(s,s.length,e.options.tabSize)-s.length;u=ce(u.line,Math.max(0,Math.round((o-Cr(e.display).left)/Yr(e.display))-c))}return u}function rn(e,t){if(t>=e.display.viewTo)return null;if((t-=e.display.viewFrom)<0)return null;for(var r=e.display.view,n=0;n<r.length;n++)if((t-=r[n].size)<0)return n}function nn(e){e.display.input.showSelection(e.display.input.prepareSelection())}function on(e,t){for(var r=e.doc,n={},i=n.cursors=document.createDocumentFragment(),o=n.selection=document.createDocumentFragment(),a=0;a<r.sel.ranges.length;a++)if(!1!==t||a!=r.sel.primIndex){var l=r.sel.ranges[a];if(!(l.from().line>=e.display.viewTo||l.to().line<e.display.viewFrom)){var s=l.empty();(s||e.options.showCursorWhenSelecting)&&an(e,l.head,i),s||ln(e,l,o)}}return n}function an(e,t,r){var n=Gr(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),i=r.appendChild(M("div"," ","CodeMirror-cursor"));if(i.style.left=n.left+"px",i.style.top=n.top+"px",i.style.height=Math.max(0,n.bottom-n.top)*e.options.cursorHeight+"px",n.other){var o=r.appendChild(M("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));o.style.display="",o.style.left=n.other.left+"px",o.style.top=n.other.top+"px",o.style.height=.85*(n.other.bottom-n.other.top)+"px"}}function ln(e,t,r){var n=e.display,i=e.doc,o=document.createDocumentFragment(),a=Cr(e.display),l=a.left,s=Math.max(n.sizerWidth,Lr(e)-n.sizer.offsetLeft)-a.right;function u(e,t,r,n){t<0&&(t=0),t=Math.round(t),n=Math.round(n),o.appendChild(M("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==r?s-e:r)+"px;\n                             height: "+(n-t)+"px"))}function c(t,r,n){var o,a,c=re(i,t),d=c.text.length;function f(r,n){return Kr(e,ce(t,r),"div",c,n)}return function(e,t,r,n){if(!e)return n(t,r,"ltr");for(var i=!1,o=0;o<e.length;++o){var a=e[o];(a.from<r&&a.to>t||t==r&&a.to==t)&&(n(Math.max(a.from,t),Math.min(a.to,r),1==a.level?"rtl":"ltr"),i=!0)}i||n(t,r,"ltr")}(Je(c),r||0,null==n?d:n,(function(e,t,i){var c,h,p,g=f(e,"left");if(e==t)c=g,h=p=g.left;else{if(c=f(t-1,"right"),"rtl"==i){var m=g;g=c,c=m}h=g.left,p=c.right}null==r&&0==e&&(h=l),c.top-g.top>3&&(u(h,g.top,null,g.bottom),h=l,g.bottom<c.top&&u(h,g.bottom,null,c.top)),null==n&&t==d&&(p=s),(!o||g.top<o.top||g.top==o.top&&g.left<o.left)&&(o=g),(!a||c.bottom>a.bottom||c.bottom==a.bottom&&c.right>a.right)&&(a=c),h<l+1&&(h=l),u(h,c.top,p-h,c.bottom)})),{start:o,end:a}}var d=t.from(),f=t.to();if(d.line==f.line)c(d.line,d.ch,f.ch);else{var h=re(i,d.line),p=re(i,f.line),g=He(h)==He(p),m=c(d.line,d.ch,g?h.text.length+1:null).end,v=c(f.line,g?0:null,f.ch).start;g&&(m.top<v.top-2?(u(m.right,m.top,null,m.bottom),u(l,v.top,v.left,v.bottom)):u(m.right,m.top,v.left-m.right,m.bottom)),m.bottom<v.top&&u(l,m.bottom,null,v.top)}r.appendChild(o)}function sn(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var r=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval((function(){return t.cursorDiv.style.visibility=(r=!r)?"":"hidden"}),e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function un(e){e.state.focused||(e.display.input.focus(),cn(e))}function cn(e,t){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(it(e,"focus",e,t),e.state.focused=!0,O(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),l&&setTimeout((function(){return e.display.input.reset(!0)}),20)),e.display.input.receivedFocus()),sn(e))}function dn(e,t){e.state.delayingBlurEvent||(e.state.focused&&(it(e,"blur",e,t),e.state.focused=!1,S(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout((function(){e.state.focused||(e.display.shift=!1)}),150))}function fn(e){var t=e.display,r=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var n=Qr(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=n+"px",a=0;a<r.length;a++)if(!r[a].hidden){e.options.fixedGutter&&(r[a].gutter&&(r[a].gutter.style.left=o),r[a].gutterBackground&&(r[a].gutterBackground.style.left=o));var l=r[a].alignable;if(l)for(var s=0;s<l.length;s++)l[s].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=n+i+"px")}}function hn(e){if(!e.options.lineNumbers)return!1;var t=e.doc,r=ue(e.options,t.first+t.size-1),n=e.display;if(r.length!=n.lineNumChars){var i=n.measure.appendChild(M("div",[M("div",r)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,a=i.offsetWidth-o;return n.lineGutter.style.width="",n.lineNumInnerWidth=Math.max(o,n.lineGutter.offsetWidth-a)+1,n.lineNumWidth=n.lineNumInnerWidth+a,n.lineNumChars=n.lineNumInnerWidth?r.length:-1,n.lineGutter.style.width=n.lineNumWidth+"px",ii(e),!0}return!1}function pn(e){for(var t=e.display,r=t.lineDiv.offsetTop,n=0;n<t.view.length;n++){var i=t.view[n],l=void 0;if(!i.hidden){if(o&&a<8){var s=i.node.offsetTop+i.node.offsetHeight;l=s-r,r=s}else{var u=i.node.getBoundingClientRect();l=u.bottom-u.top}var c=i.line.height-l;if(l<2&&(l=Xr(t)),(c>.001||c<-.001)&&(oe(i.line,l),gn(i.line),i.rest))for(var d=0;d<i.rest.length;d++)gn(i.rest[d])}}}function gn(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t)e.widgets[t].height=e.widgets[t].node.parentNode.offsetHeight}function mn(e,t,r){var n=r&&null!=r.top?Math.max(0,r.top):e.scroller.scrollTop;n=Math.floor(n-xr(e));var i=r&&null!=r.bottom?r.bottom:n+e.wrapper.clientHeight,o=le(t,n),a=le(t,i);if(r&&r.ensure){var l=r.ensure.from.line,s=r.ensure.to.line;l<o?(o=l,a=le(t,Be(re(t,l))+e.wrapper.clientHeight)):Math.min(s,t.lastLine())>=a&&(o=le(t,Be(re(t,s))-e.wrapper.clientHeight),a=s)}return{from:o,to:Math.max(a,o+1)}}function vn(e,t){Math.abs(e.doc.scrollTop-t)<2||(e.doc.scrollTop=t,r||ni(e,{top:t}),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t),e.display.scrollbars.setScrollTop(t),r&&ni(e),Qn(e,100))}function yn(e,t,r){(r?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)||(t=Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth),e.doc.scrollLeft=t,fn(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}var bn=0,wn=null;function xn(e){var t=e.wheelDeltaX,r=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==r&&e.detail&&e.axis==e.VERTICAL_AXIS?r=e.detail:null==r&&(r=e.wheelDelta),{x:t,y:r}}function kn(e){var t=xn(e);return t.x*=wn,t.y*=wn,t}function Cn(e,t){var n=xn(t),i=n.x,o=n.y,a=e.display,s=a.scroller,u=s.scrollWidth>s.clientWidth,d=s.scrollHeight>s.clientHeight;if(i&&u||o&&d){if(o&&m&&l)e:for(var f=t.target,h=a.view;f!=s;f=f.parentNode)for(var p=0;p<h.length;p++)if(h[p].node==f){e.display.currentWheelTarget=f;break e}if(i&&!r&&!c&&null!=wn)return o&&d&&vn(e,Math.max(0,Math.min(s.scrollTop+o*wn,s.scrollHeight-s.clientHeight))),yn(e,Math.max(0,Math.min(s.scrollLeft+i*wn,s.scrollWidth-s.clientWidth))),(!o||o&&d)&&ut(t),void(a.wheelStartX=null);if(o&&null!=wn){var g=o*wn,v=e.doc.scrollTop,y=v+a.wrapper.clientHeight;g<0?v=Math.max(0,v+g-50):y=Math.min(e.doc.height,y+g+50),ni(e,{top:v,bottom:y})}bn<20&&(null==a.wheelStartX?(a.wheelStartX=s.scrollLeft,a.wheelStartY=s.scrollTop,a.wheelDX=i,a.wheelDY=o,setTimeout((function(){if(null!=a.wheelStartX){var e=s.scrollLeft-a.wheelStartX,t=s.scrollTop-a.wheelStartY,r=t&&a.wheelDY&&t/a.wheelDY||e&&a.wheelDX&&e/a.wheelDX;a.wheelStartX=a.wheelStartY=null,r&&(wn=(wn*bn+r)/(bn+1),++bn)}}),200)):(a.wheelDX+=i,a.wheelDY+=o))}}function Sn(e){var t=e.display,r=t.gutters.offsetWidth,n=Math.round(e.doc.height+kr(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?r:0,docHeight:n,scrollHeight:n+Sr(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:r}}function Ln(e,t,r){this.cm=r;var n=this.vert=M("div",[M("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=M("div",[M("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");e(n),e(i),tt(n,"scroll",(function(){n.clientHeight&&t(n.scrollTop,"vertical")})),tt(i,"scroll",(function(){i.clientWidth&&t(i.scrollLeft,"horizontal")})),this.checkedZeroWidth=!1,o&&a<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")}function Tn(){}function Mn(e,t){t||(t=Sn(e));var r=e.display.barWidth,n=e.display.barHeight;Nn(e,t);for(var i=0;i<4&&r!=e.display.barWidth||n!=e.display.barHeight;i++)r!=e.display.barWidth&&e.options.lineWrapping&&pn(e),Nn(e,Sn(e)),r=e.display.barWidth,n=e.display.barHeight}function Nn(e,t){var r=e.display,n=r.scrollbars.update(t);r.sizer.style.paddingRight=(r.barWidth=n.right)+"px",r.sizer.style.paddingBottom=(r.barHeight=n.bottom)+"px",r.heightForcer.style.borderBottom=n.bottom+"px solid transparent",n.right&&n.bottom?(r.scrollbarFiller.style.display="block",r.scrollbarFiller.style.height=n.bottom+"px",r.scrollbarFiller.style.width=n.right+"px"):r.scrollbarFiller.style.display="",n.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(r.gutterFiller.style.display="block",r.gutterFiller.style.height=n.bottom+"px",r.gutterFiller.style.width=t.gutterWidth+"px"):r.gutterFiller.style.display=""}o?wn=-.53:r?wn=15:u?wn=-.7:d&&(wn=-1/3),Ln.prototype=H({update:function(e){var t=e.scrollWidth>e.clientWidth+1,r=e.scrollHeight>e.clientHeight+1,n=e.nativeBarWidth;if(r){this.vert.style.display="block",this.vert.style.bottom=t?n+"px":"0";var i=e.viewHeight-(t?n:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=r?n+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(r?n:0);this.horiz.firstChild.style.width=e.scrollWidth-e.clientWidth+o+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(0==n&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:r?n:0,bottom:t?n:0}},setScrollLeft:function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz)},setScrollTop:function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert)},zeroWidthHack:function(){var e=m&&!f?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.pointerEvents=this.vert.style.pointerEvents="none",this.disableHoriz=new I,this.disableVert=new I},enableZeroWidthBar:function(e,t){e.style.pointerEvents="auto",t.set(1e3,(function r(){var n=e.getBoundingClientRect();document.elementFromPoint(n.left+1,n.bottom-1)!=e?e.style.pointerEvents="none":t.set(1e3,r)}))},clear:function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)}},Ln.prototype),Tn.prototype=H({update:function(){return{bottom:0,right:0}},setScrollLeft:function(){},setScrollTop:function(){},clear:function(){}},Tn.prototype);var An={native:Ln,null:Tn};function On(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&S(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new An[e.options.scrollbarStyle]((function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),tt(t,"mousedown",(function(){e.state.focused&&setTimeout((function(){return e.display.input.focus()}),0)})),t.setAttribute("cm-not-content","true")}),(function(t,r){"horizontal"==r?yn(e,t):vn(e,t)}),e),e.display.scrollbars.addClass&&O(e.display.wrapper,e.display.scrollbars.addClass)}function Wn(e,t,r,n,i){var o=e.display,a=Xr(e.display);r<0&&(r=0);var l=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:o.scroller.scrollTop,s=Tr(e),u={};i-r>s&&(i=r+s);var c=e.doc.height+kr(o),d=r<a,f=i>c-a;if(r<l)u.scrollTop=d?0:r;else if(i>l+s){var h=Math.min(r,(f?c:i)-s);h!=l&&(u.scrollTop=h)}var p=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:o.scroller.scrollLeft,g=Lr(e)-(e.options.fixedGutter?o.gutters.offsetWidth:0),m=n-t>g;return m&&(n=t+g),t<10?u.scrollLeft=0:t<p?u.scrollLeft=Math.max(0,t-(m?0:10)):n>g+p-3&&(u.scrollLeft=n+(m?0:10)-g),u}function zn(e,t,r){null==t&&null==r||Hn(e),null!=t&&(e.curOp.scrollLeft=(null==e.curOp.scrollLeft?e.doc.scrollLeft:e.curOp.scrollLeft)+t),null!=r&&(e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+r)}function Dn(e){Hn(e);var t=e.getCursor(),r=t,n=t;e.options.lineWrapping||(r=t.ch?ce(t.line,t.ch-1):t,n=ce(t.line,t.ch+1)),e.curOp.scrollToPos={from:r,to:n,margin:e.options.cursorScrollMargin,isCursor:!0}}function Hn(e){var t=e.curOp.scrollToPos;if(t){e.curOp.scrollToPos=null;var r=Ur(e,t.from),n=Ur(e,t.to),i=Wn(e,Math.min(r.left,n.left),Math.min(r.top,n.top)-t.margin,Math.max(r.right,n.right),Math.max(r.bottom,n.bottom)+t.margin);e.scrollTo(i.scrollLeft,i.scrollTop)}}var Pn=0;function In(e){var t;e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:null,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++Pn},t=e.curOp,ir?ir.ops.push(t):t.ownsGroup=ir={ops:[t],delayedCallbacks:[]}}function En(e){!function(e,t){var r=e.ownsGroup;if(r)try{!function(e){var t=e.delayedCallbacks,r=0;do{for(;r<t.length;r++)t[r].call(null);for(var n=0;n<e.ops.length;n++){var i=e.ops[n];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(r<t.length)}(r)}finally{ir=null,t(r)}}(e.curOp,(function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;!function(e){for(var t=e.ops,r=0;r<t.length;r++)Fn(t[r]);for(var n=0;n<t.length;n++)Bn(t[n]);for(var i=0;i<t.length;i++)Rn(t[i]);for(var o=0;o<t.length;o++)jn(t[o]);for(var a=0;a<t.length;a++)Vn(t[a])}(e)}))}function Fn(e){var t=e.cm,r=t.display;!function(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=Sr(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=Sr(e)+"px",t.scrollbarsClipped=!0)}(t),e.updateMaxLine&&je(t),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<r.viewFrom||e.scrollToPos.to.line>=r.viewTo)||r.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new ei(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function Bn(e){e.updatedDisplay=e.mustUpdate&&ti(e.cm,e.update)}function Rn(e){var t=e.cm,r=t.display;e.updatedDisplay&&pn(t),e.barMeasure=Sn(t),r.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=Nr(t,r.maxLine,r.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(r.scroller.clientWidth,r.sizer.offsetLeft+e.adjustWidthTo+Sr(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,r.sizer.offsetLeft+e.adjustWidthTo-Lr(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=r.input.prepareSelection(e.focus))}function jn(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&yn(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var r=e.focus&&e.focus==A()&&(!document.hasFocus||document.hasFocus());e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,r),(e.updatedDisplay||e.startHeight!=t.doc.height)&&Mn(t,e.barMeasure),e.updatedDisplay&&oi(t,e.barMeasure),e.selectionChanged&&sn(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),r&&un(e.cm)}function Vn(e){var t=e.cm,r=t.display,n=t.doc;if(e.updatedDisplay&&ri(t,e.update),null==r.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(r.wheelStartX=r.wheelStartY=null),null==e.scrollTop||r.scroller.scrollTop==e.scrollTop&&!e.forceScroll||(n.scrollTop=Math.max(0,Math.min(r.scroller.scrollHeight-r.scroller.clientHeight,e.scrollTop)),r.scrollbars.setScrollTop(n.scrollTop),r.scroller.scrollTop=n.scrollTop),null==e.scrollLeft||r.scroller.scrollLeft==e.scrollLeft&&!e.forceScroll||(n.scrollLeft=Math.max(0,Math.min(r.scroller.scrollWidth-r.scroller.clientWidth,e.scrollLeft)),r.scrollbars.setScrollLeft(n.scrollLeft),r.scroller.scrollLeft=n.scrollLeft,fn(t)),e.scrollToPos){var i=function(e,t,r,n){var i;null==n&&(n=0);for(var o=0;o<5;o++){var a=!1;i=Gr(e,t);var l=r&&r!=t?Gr(e,r):i,s=Wn(e,Math.min(i.left,l.left),Math.min(i.top,l.top)-n,Math.max(i.left,l.left),Math.max(i.bottom,l.bottom)+n),u=e.doc.scrollTop,c=e.doc.scrollLeft;if(null!=s.scrollTop&&(vn(e,s.scrollTop),Math.abs(e.doc.scrollTop-u)>1&&(a=!0)),null!=s.scrollLeft&&(yn(e,s.scrollLeft),Math.abs(e.doc.scrollLeft-c)>1&&(a=!0)),!a)break}return i}(t,me(n,e.scrollToPos.from),me(n,e.scrollToPos.to),e.scrollToPos.margin);e.scrollToPos.isCursor&&t.state.focused&&function(e,t){if(!ot(e,"scrollCursorIntoView")){var r=e.display,n=r.sizer.getBoundingClientRect(),i=null;if(t.top+n.top<0?i=!0:t.bottom+n.top>(window.innerHeight||document.documentElement.clientHeight)&&(i=!1),null!=i&&!h){var o=M("div","​",null,"position: absolute;\n                         top: "+(t.top-r.viewOffset-xr(e.display))+"px;\n                         height: "+(t.bottom-t.top+Sr(e)+r.barHeight)+"px;\n                         left: "+t.left+"px; width: 2px;");e.display.lineSpace.appendChild(o),o.scrollIntoView(i),e.display.lineSpace.removeChild(o)}}}(t,i)}var o=e.maybeHiddenMarkers,a=e.maybeUnhiddenMarkers;if(o)for(var l=0;l<o.length;++l)o[l].lines.length||it(o[l],"hide");if(a)for(var s=0;s<a.length;++s)a[s].lines.length&&it(a[s],"unhide");r.wrapper.offsetHeight&&(n.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&it(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function Kn(e,t){if(e.curOp)return t();In(e);try{return t()}finally{En(e)}}function Gn(e,t){return function(){if(e.curOp)return t.apply(e,arguments);In(e);try{return t.apply(e,arguments)}finally{En(e)}}}function Un(e){return function(){if(this.curOp)return e.apply(this,arguments);In(this);try{return e.apply(this,arguments)}finally{En(this)}}}function qn(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);In(t);try{return e.apply(this,arguments)}finally{En(t)}}}function $n(e,t,r,n){null==t&&(t=e.doc.first),null==r&&(r=e.doc.first+e.doc.size),n||(n=0);var i=e.display;if(n&&r<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)be&&Pe(e.doc,t)<i.viewTo&&Xn(e);else if(r<=i.viewFrom)be&&Ie(e.doc,r+n)>i.viewFrom?Xn(e):(i.viewFrom+=n,i.viewTo+=n);else if(t<=i.viewFrom&&r>=i.viewTo)Xn(e);else if(t<=i.viewFrom){var o=Yn(e,r,r+n,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=n):Xn(e)}else if(r>=i.viewTo){var a=Yn(e,t,t,-1);a?(i.view=i.view.slice(0,a.index),i.viewTo=a.lineN):Xn(e)}else{var l=Yn(e,t,t,-1),s=Yn(e,r,r+n,1);l&&s?(i.view=i.view.slice(0,l.index).concat(nr(e,l.lineN,s.lineN)).concat(i.view.slice(s.index)),i.viewTo+=n):Xn(e)}var u=i.externalMeasured;u&&(r<u.lineN?u.lineN+=n:t<u.lineN+u.size&&(i.externalMeasured=null))}function _n(e,t,r){e.curOp.viewChanged=!0;var n=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(n.externalMeasured=null),!(t<n.viewFrom||t>=n.viewTo)){var o=n.view[rn(e,t)];if(null!=o.node){var a=o.changes||(o.changes=[]);-1==E(a,r)&&a.push(r)}}}function Xn(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function Yn(e,t,r,n){var i,o=rn(e,t),a=e.display.view;if(!be||r==e.doc.first+e.doc.size)return{index:o,lineN:r};for(var l=e.display.viewFrom,s=0;s<o;s++)l+=a[s].size;if(l!=t){if(n>0){if(o==a.length-1)return null;i=l+a[o].size-t,o++}else i=l-t;t+=i,r+=i}for(;Pe(e.doc,r)!=r;){if(o==(n<0?0:a.length-1))return null;r+=n*a[o-(n<0?1:0)].size,o+=n}return{index:o,lineN:r}}function Zn(e){for(var t=e.display.view,r=0,n=0;n<t.length;n++){var i=t[n];i.hidden||i.node&&!i.changes||++r}return r}function Qn(e,t){e.doc.mode.startState&&e.doc.frontier<e.display.viewTo&&e.state.highlight.set(t,D(Jn,e))}function Jn(e){var t=e.doc;if(t.frontier<t.first&&(t.frontier=t.first),!(t.frontier>=e.display.viewTo)){var r=+new Date+e.options.workTime,n=zt(t.mode,Ft(e,t.frontier)),i=[];t.iter(t.frontier,Math.min(t.first+t.size,e.display.viewTo+500),(function(o){if(t.frontier>=e.display.viewFrom){var a=o.styles,l=o.text.length>e.options.maxHighlightLength,s=It(e,o,l?zt(t.mode,n):n,!0);o.styles=s.styles;var u=o.styleClasses,c=s.classes;c?o.styleClasses=c:u&&(o.styleClasses=null);for(var d=!a||a.length!=o.styles.length||u!=c&&(!u||!c||u.bgClass!=c.bgClass||u.textClass!=c.textClass),f=0;!d&&f<a.length;++f)d=a[f]!=o.styles[f];d&&i.push(t.frontier),o.stateAfter=l?n:zt(t.mode,n)}else o.text.length<=e.options.maxHighlightLength&&Bt(e,o.text,n),o.stateAfter=t.frontier%5==0?zt(t.mode,n):null;if(++t.frontier,+new Date>r)return Qn(e,e.options.workDelay),!0})),i.length&&Kn(e,(function(){for(var t=0;t<i.length;t++)_n(e,i[t],"text")}))}}function ei(e,t,r){var n=e.display;this.viewport=t,this.visible=mn(n,e.doc,t),this.editorIsHidden=!n.wrapper.offsetWidth,this.wrapperHeight=n.wrapper.clientHeight,this.wrapperWidth=n.wrapper.clientWidth,this.oldDisplayWidth=Lr(e),this.force=r,this.dims=Zr(e),this.events=[]}function ti(e,t){var r=e.display,n=e.doc;if(t.editorIsHidden)return Xn(e),!1;if(!t.force&&t.visible.from>=r.viewFrom&&t.visible.to<=r.viewTo&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo)&&r.renderedView==r.view&&0==Zn(e))return!1;hn(e)&&(Xn(e),t.dims=Zr(e));var i=n.first+n.size,o=Math.max(t.visible.from-e.options.viewportMargin,n.first),a=Math.min(i,t.visible.to+e.options.viewportMargin);r.viewFrom<o&&o-r.viewFrom<20&&(o=Math.max(n.first,r.viewFrom)),r.viewTo>a&&r.viewTo-a<20&&(a=Math.min(i,r.viewTo)),be&&(o=Pe(e.doc,o),a=Ie(e.doc,a));var s=o!=r.viewFrom||a!=r.viewTo||r.lastWrapHeight!=t.wrapperHeight||r.lastWrapWidth!=t.wrapperWidth;!function(e,t,r){var n=e.display;0==n.view.length||t>=n.viewTo||r<=n.viewFrom?(n.view=nr(e,t,r),n.viewFrom=t):(n.viewFrom>t?n.view=nr(e,t,n.viewFrom).concat(n.view):n.viewFrom<t&&(n.view=n.view.slice(rn(e,t))),n.viewFrom=t,n.viewTo<r?n.view=n.view.concat(nr(e,n.viewTo,r)):n.viewTo>r&&(n.view=n.view.slice(0,rn(e,r)))),n.viewTo=r}(e,o,a),r.viewOffset=Be(re(e.doc,r.viewFrom)),e.display.mover.style.top=r.viewOffset+"px";var u=Zn(e);if(!s&&0==u&&!t.force&&r.renderedView==r.view&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo))return!1;var c=A();return u>4&&(r.lineDiv.style.display="none"),function(e,t,r){var n=e.display,i=e.options.lineNumbers,o=n.lineDiv,a=o.firstChild;function s(t){var r=t.nextSibling;return l&&m&&e.display.currentWheelTarget==t?t.style.display="none":t.parentNode.removeChild(t),r}for(var u=n.view,c=n.viewFrom,d=0;d<u.length;d++){var f=u[d];if(f.hidden);else if(f.node&&f.node.parentNode==o){for(;a!=f.node;)a=s(a);var h=i&&null!=t&&t<=c&&f.lineNumber;f.changes&&(E(f.changes,"gutter")>-1&&(h=!1),sr(e,f,c,r)),h&&(L(f.lineNumber),f.lineNumber.appendChild(document.createTextNode(ue(e.options,c)))),a=f.node.nextSibling}else{var p=gr(e,f,c,r);o.insertBefore(p,a)}c+=f.size}for(;a;)a=s(a)}(e,r.updateLineNumbers,t.dims),u>4&&(r.lineDiv.style.display=""),r.renderedView=r.view,c&&A()!=c&&c.offsetHeight&&c.focus(),L(r.cursorDiv),L(r.selectionDiv),r.gutters.style.height=r.sizer.style.minHeight=0,s&&(r.lastWrapHeight=t.wrapperHeight,r.lastWrapWidth=t.wrapperWidth,Qn(e,400)),r.updateLineNumbers=null,!0}function ri(e,t){for(var r=t.viewport,n=!0;(n&&e.options.lineWrapping&&t.oldDisplayWidth!=Lr(e)||(r&&null!=r.top&&(r={top:Math.min(e.doc.height+kr(e.display)-Tr(e),r.top)}),t.visible=mn(e.display,e.doc,r),!(t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)))&&ti(e,t);n=!1){pn(e);var i=Sn(e);nn(e),Mn(e,i),oi(e,i)}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function ni(e,t){var r=new ei(e,t);if(ti(e,r)){pn(e),ri(e,r);var n=Sn(e);nn(e),Mn(e,n),oi(e,n),r.finish()}}function ii(e){var t=e.display.gutters.offsetWidth;e.display.sizer.style.marginLeft=t+"px"}function oi(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+Sr(e)+"px"}function ai(e){var t=e.display.gutters,r=e.options.gutters;L(t);for(var n=0;n<r.length;++n){var i=r[n],o=t.appendChild(M("div",null,"CodeMirror-gutter "+i));"CodeMirror-linenumbers"==i&&(e.display.lineGutter=o,o.style.width=(e.display.lineNumWidth||1)+"px")}t.style.display=n?"":"none",ii(e)}function li(e){var t=E(e.gutters,"CodeMirror-linenumbers");-1==t&&e.lineNumbers?e.gutters=e.gutters.concat(["CodeMirror-linenumbers"]):t>-1&&!e.lineNumbers&&(e.gutters=e.gutters.slice(0),e.gutters.splice(t,1))}function si(e,t){this.ranges=e,this.primIndex=t}function ui(e,t){this.anchor=e,this.head=t}function ci(e,t){var r=e[t];e.sort((function(e,t){return de(e.from(),t.from())})),t=E(e,r);for(var n=1;n<e.length;n++){var i=e[n],o=e[n-1];if(de(o.to(),i.from())>=0){var a=pe(o.from(),i.from()),l=he(o.to(),i.to()),s=o.empty()?i.from()==i.head:o.from()==o.head;n<=t&&--t,e.splice(--n,2,new ui(s?l:a,s?a:l))}}return new si(e,t)}function di(e,t){return new si([new ui(e,t||e)],0)}function fi(e){return e.text?ce(e.from.line+e.text.length-1,U(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function hi(e,t){if(de(e,t.from)<0)return e;if(de(e,t.to)<=0)return fi(t);var r=e.line+t.text.length-(t.to.line-t.from.line)-1,n=e.ch;return e.line==t.to.line&&(n+=fi(t).ch-t.to.ch),ce(r,n)}function pi(e,t){for(var r=[],n=0;n<e.sel.ranges.length;n++){var i=e.sel.ranges[n];r.push(new ui(hi(i.anchor,t),hi(i.head,t)))}return ci(r,e.sel.primIndex)}function gi(e,t,r){return e.line==t.line?ce(r.line,e.ch-t.ch+r.ch):ce(r.line+(e.line-t.line),e.ch)}function mi(e){e.doc.mode=At(e.options,e.doc.modeOption),vi(e)}function vi(e){e.doc.iter((function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)})),e.doc.frontier=e.doc.first,Qn(e,100),e.state.modeGen++,e.curOp&&$n(e)}function yi(e,t){return 0==t.from.ch&&0==t.to.ch&&""==U(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function bi(e,t,r,n){function i(e){return r?r[e]:null}function o(e,r,i){!function(e,t,r,n){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),Le(e),Te(e,r);var i=n?n(e):1;i!=e.height&&oe(e,i)}(e,r,i,n),ar(e,"change",e,t)}function a(e,t){for(var r=[],o=e;o<t;++o)r.push(new Ut(u[o],i(o),n));return r}var l=t.from,s=t.to,u=t.text,c=re(e,l.line),d=re(e,s.line),f=U(u),h=i(u.length-1),p=s.line-l.line;if(t.full)e.insert(0,a(0,u.length)),e.remove(u.length,e.size-u.length);else if(yi(e,t)){var g=a(0,u.length-1);o(d,d.text,h),p&&e.remove(l.line,p),g.length&&e.insert(l.line,g)}else if(c==d)if(1==u.length)o(c,c.text.slice(0,l.ch)+f+c.text.slice(s.ch),h);else{var m=a(1,u.length-1);m.push(new Ut(f+c.text.slice(s.ch),h,n)),o(c,c.text.slice(0,l.ch)+u[0],i(0)),e.insert(l.line+1,m)}else if(1==u.length)o(c,c.text.slice(0,l.ch)+u[0]+d.text.slice(s.ch),i(0)),e.remove(l.line+1,p);else{o(c,c.text.slice(0,l.ch)+u[0],i(0)),o(d,f+d.text.slice(s.ch),h);var v=a(1,u.length-1);p>1&&e.remove(l.line+1,p-1),e.insert(l.line+1,v)}ar(e,"change",e,t)}function wi(e,t,r){!function e(n,i,o){if(n.linked)for(var a=0;a<n.linked.length;++a){var l=n.linked[a];if(l.doc!=i){var s=o&&l.sharedHist;r&&!s||(t(l.doc,s),e(l.doc,n,s))}}}(e,null,!0)}function xi(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,en(e),mi(e),e.options.lineWrapping||je(e),e.options.mode=t.modeOption,$n(e)}function ki(e){this.done=[],this.undone=[],this.undoDepth=1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e||1}function Ci(e,t){var r={from:fe(t.from),to:fi(t),text:ne(e,t.from,t.to)};return Ni(e,r,t.from.line,t.to.line+1),wi(e,(function(e){return Ni(e,r,t.from.line,t.to.line+1)}),!0),r}function Si(e){for(;e.length;){if(!U(e).ranges)break;e.pop()}}function Li(e,t,r,n){var i=e.history;i.undone.length=0;var o,a,l=+new Date;if((i.lastOp==n||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&e.cm&&i.lastModTime>l-e.cm.options.historyEventDelay||"*"==t.origin.charAt(0)))&&(o=function(e,t){return t?(Si(e.done),U(e.done)):e.done.length&&!U(e.done).ranges?U(e.done):e.done.length>1&&!e.done[e.done.length-2].ranges?(e.done.pop(),U(e.done)):void 0}(i,i.lastOp==n)))a=U(o.changes),0==de(t.from,t.to)&&0==de(t.from,a.to)?a.to=fi(t):o.changes.push(Ci(e,t));else{var s=U(i.done);for(s&&s.ranges||Mi(e.sel,i.done),o={changes:[Ci(e,t)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(r),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=l,i.lastOp=i.lastSelOp=n,i.lastOrigin=i.lastSelOrigin=t.origin,a||it(e,"historyAdded")}function Ti(e,t,r,n){var i=e.history,o=n&&n.origin;r==i.lastSelOp||o&&i.lastSelOrigin==o&&(i.lastModTime==i.lastSelTime&&i.lastOrigin==o||function(e,t,r,n){var i=t.charAt(0);return"*"==i||"+"==i&&r.ranges.length==n.ranges.length&&r.somethingSelected()==n.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}(e,o,U(i.done),t))?i.done[i.done.length-1]=t:Mi(t,i.done),i.lastSelTime=+new Date,i.lastSelOrigin=o,i.lastSelOp=r,n&&!1!==n.clearRedo&&Si(i.undone)}function Mi(e,t){var r=U(t);r&&r.ranges&&r.equals(e)||t.push(e)}function Ni(e,t,r,n){var i=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,r),Math.min(e.first+e.size,n),(function(r){r.markedSpans&&((i||(i=t["spans_"+e.id]={}))[o]=r.markedSpans),++o}))}function Ai(e){if(!e)return null;for(var t,r=0;r<e.length;++r)e[r].marker.explicitlyCleared?t||(t=e.slice(0,r)):t&&t.push(e[r]);return t?t.length?t:null:e}function Oi(e,t){var r=function(e,t){var r=t["spans_"+e.id];if(!r)return null;for(var n=[],i=0;i<t.text.length;++i)n.push(Ai(r[i]));return n}(e,t),n=Ce(e,t);if(!r)return n;if(!n)return r;for(var i=0;i<r.length;++i){var o=r[i],a=n[i];if(o&&a)e:for(var l=0;l<a.length;++l){for(var s=a[l],u=0;u<o.length;++u)if(o[u].marker==s.marker)continue e;o.push(s)}else a&&(r[i]=a)}return r}function Wi(e,t,r){for(var n=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)n.push(r?si.prototype.deepCopy.call(o):o);else{var a=o.changes,l=[];n.push({changes:l});for(var s=0;s<a.length;++s){var u=a[s],c=void 0;if(l.push({from:u.from,to:u.to,text:u.text}),t)for(var d in u)(c=d.match(/^spans_(\d+)$/))&&E(t,Number(c[1]))>-1&&(U(l)[d]=u[d],delete u[d])}}}return n}function zi(e,t,r,n){if(e.cm&&e.cm.display.shift||e.extend){var i=t.anchor;if(n){var o=de(r,i)<0;o!=de(n,i)<0?(i=r,r=n):o!=de(r,n)<0&&(r=n)}return new ui(i,r)}return new ui(n||r,r)}function Di(e,t,r,n){Fi(e,new si([zi(e,e.sel.primary(),t,r)],0),n)}function Hi(e,t,r){for(var n=[],i=0;i<e.sel.ranges.length;i++)n[i]=zi(e,e.sel.ranges[i],t[i],null);Fi(e,ci(n,e.sel.primIndex),r)}function Pi(e,t,r,n){var i=e.sel.ranges.slice(0);i[t]=r,Fi(e,ci(i,e.sel.primIndex),n)}function Ii(e,t,r,n){Fi(e,di(t,r),n)}function Ei(e,t,r){var n=e.history.done,i=U(n);i&&i.ranges?(n[n.length-1]=t,Bi(e,t,r)):Fi(e,t,r)}function Fi(e,t,r){Bi(e,t,r),Ti(e,e.sel,e.cm?e.cm.curOp.id:NaN,r)}function Bi(e,t,r){(lt(e,"beforeSelectionChange")||e.cm&&lt(e.cm,"beforeSelectionChange"))&&(t=function(e,t,r){var n={ranges:t.ranges,update:function(t){this.ranges=[];for(var r=0;r<t.length;r++)this.ranges[r]=new ui(me(e,t[r].anchor),me(e,t[r].head))},origin:r&&r.origin};return it(e,"beforeSelectionChange",e,n),e.cm&&it(e.cm,"beforeSelectionChange",e.cm,n),n.ranges!=t.ranges?ci(n.ranges,n.ranges.length-1):t}(e,t,r));var n=r&&r.bias||(de(t.primary().head,e.sel.primary().head)<0?-1:1);Ri(e,Vi(e,t,n,!0)),r&&!1===r.scroll||!e.cm||Dn(e.cm)}function Ri(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=e.cm.curOp.selectionChanged=!0,at(e.cm)),ar(e,"cursorActivity",e))}function ji(e){Ri(e,Vi(e,e.sel,null,!1))}function Vi(e,t,r,n){for(var i,o=0;o<t.ranges.length;o++){var a=t.ranges[o],l=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],s=Gi(e,a.anchor,l&&l.anchor,r,n),u=Gi(e,a.head,l&&l.head,r,n);(i||s!=a.anchor||u!=a.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new ui(s,u))}return i?ci(i,t.primIndex):t}function Ki(e,t,r,n,i){var o=re(e,t.line);if(o.markedSpans)for(var a=0;a<o.markedSpans.length;++a){var l=o.markedSpans[a],s=l.marker;if((null==l.from||(s.inclusiveLeft?l.from<=t.ch:l.from<t.ch))&&(null==l.to||(s.inclusiveRight?l.to>=t.ch:l.to>t.ch))){if(i&&(it(s,"beforeCursorEnter"),s.explicitlyCleared)){if(o.markedSpans){--a;continue}break}if(!s.atomic)continue;if(r){var u=s.find(n<0?1:-1),c=void 0;if((n<0?s.inclusiveRight:s.inclusiveLeft)&&(u=Ui(e,u,-n,u&&u.line==t.line?o:null)),u&&u.line==t.line&&(c=de(u,r))&&(n<0?c<0:c>0))return Ki(e,u,t,n,i)}var d=s.find(n<0?-1:1);return(n<0?s.inclusiveLeft:s.inclusiveRight)&&(d=Ui(e,d,n,d.line==t.line?o:null)),d?Ki(e,d,t,n,i):null}}return t}function Gi(e,t,r,n,i){var o=n||1,a=Ki(e,t,r,o,i)||!i&&Ki(e,t,r,o,!0)||Ki(e,t,r,-o,i)||!i&&Ki(e,t,r,-o,!0);return a||(e.cantEdit=!0,ce(e.first,0))}function Ui(e,t,r,n){return r<0&&0==t.ch?t.line>e.first?me(e,ce(t.line-1)):null:r>0&&t.ch==(n||re(e,t.line)).text.length?t.line<e.first+e.size-1?ce(t.line+1,0):null:new ce(t.line,t.ch+r)}function qi(e){e.setSelection(ce(e.firstLine(),0),ce(e.lastLine()),B)}function $i(e,t,r){var n={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return n.canceled=!0}};return r&&(n.update=function(t,r,i,o){t&&(n.from=me(e,t)),r&&(n.to=me(e,r)),i&&(n.text=i),void 0!==o&&(n.origin=o)}),it(e,"beforeChange",e,n),e.cm&&it(e.cm,"beforeChange",e.cm,n),n.canceled?null:{from:n.from,to:n.to,text:n.text,origin:n.origin}}function _i(e,t,r){if(e.cm){if(!e.cm.curOp)return Gn(e.cm,_i)(e,t,r);if(e.cm.state.suppressEdits)return}if(!(lt(e,"beforeChange")||e.cm&&lt(e.cm,"beforeChange"))||(t=$i(e,t,!0))){var n=ye&&!r&&function(e,t,r){var n=null;if(e.iter(t.line,r.line+1,(function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var r=e.markedSpans[t].marker;!r.readOnly||n&&-1!=E(n,r)||(n||(n=[])).push(r)}})),!n)return null;for(var i=[{from:t,to:r}],o=0;o<n.length;++o)for(var a=n[o],l=a.find(0),s=0;s<i.length;++s){var u=i[s];if(!(de(u.to,l.from)<0||de(u.from,l.to)>0)){var c=[s,1],d=de(u.from,l.from),f=de(u.to,l.to);(d<0||!a.inclusiveLeft&&!d)&&c.push({from:u.from,to:l.from}),(f>0||!a.inclusiveRight&&!f)&&c.push({from:l.to,to:u.to}),i.splice.apply(i,c),s+=c.length-1}}return i}(e,t.from,t.to);if(n)for(var i=n.length-1;i>=0;--i)Xi(e,{from:n[i].from,to:n[i].to,text:i?[""]:t.text});else Xi(e,t)}}function Xi(e,t){if(1!=t.text.length||""!=t.text[0]||0!=de(t.from,t.to)){var r=pi(e,t);Li(e,t,r,e.cm?e.cm.curOp.id:NaN),Qi(e,t,r,Ce(e,t));var n=[];wi(e,(function(e,r){r||-1!=E(n,e.history)||(ro(e.history,t),n.push(e.history)),Qi(e,t,null,Ce(e,t))}))}}function Yi(e,t,r){if(!e.cm||!e.cm.state.suppressEdits||r){for(var n,i=e.history,o=e.sel,a="undo"==t?i.done:i.undone,l="undo"==t?i.undone:i.done,s=0;s<a.length&&(n=a[s],r?!n.ranges||n.equals(e.sel):n.ranges);s++);if(s!=a.length){for(i.lastOrigin=i.lastSelOrigin=null;(n=a.pop()).ranges;){if(Mi(n,l),r&&!n.equals(e.sel))return void Fi(e,n,{clearRedo:!1});o=n}var u=[];Mi(o,l),l.push({changes:u,generation:i.generation}),i.generation=n.generation||++i.maxGeneration;for(var c=lt(e,"beforeChange")||e.cm&&lt(e.cm,"beforeChange"),d=function(r){var i=n.changes[r];if(i.origin=t,c&&!$i(e,i,!1))return a.length=0,{};u.push(Ci(e,i));var o=r?pi(e,i):U(a);Qi(e,i,o,Oi(e,i)),!r&&e.cm&&e.cm.scrollIntoView({from:i.from,to:fi(i)});var l=[];wi(e,(function(e,t){t||-1!=E(l,e.history)||(ro(e.history,i),l.push(e.history)),Qi(e,i,null,Oi(e,i))}))},f=n.changes.length-1;f>=0;--f){var h=d(f);if(h)return h.v}}}}function Zi(e,t){if(0!=t&&(e.first+=t,e.sel=new si(q(e.sel.ranges,(function(e){return new ui(ce(e.anchor.line+t,e.anchor.ch),ce(e.head.line+t,e.head.ch))})),e.sel.primIndex),e.cm)){$n(e.cm,e.first,e.first-t,t);for(var r=e.cm.display,n=r.viewFrom;n<r.viewTo;n++)_n(e.cm,n,"gutter")}}function Qi(e,t,r,n){if(e.cm&&!e.cm.curOp)return Gn(e.cm,Qi)(e,t,r,n);if(t.to.line<e.first)Zi(e,t.text.length-1-(t.to.line-t.from.line));else if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);Zi(e,i),t={from:ce(e.first,0),to:ce(t.to.line+i,t.to.ch),text:[U(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:ce(o,re(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=ne(e,t.from,t.to),r||(r=pi(e,t)),e.cm?function(e,t,r){var n=e.doc,i=e.display,o=t.from,a=t.to,l=!1,s=o.line;e.options.lineWrapping||(s=ae(He(re(n,o.line))),n.iter(s,a.line+1,(function(e){if(e==i.maxLine)return l=!0,!0})));n.sel.contains(t.from,t.to)>-1&&at(e);bi(n,t,r,Jr(e)),e.options.lineWrapping||(n.iter(s,o.line+t.text.length,(function(e){var t=Re(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,l=!1)})),l&&(e.curOp.updateMaxLine=!0));n.frontier=Math.min(n.frontier,o.line),Qn(e,400);var u=t.text.length-(a.line-o.line)-1;t.full?$n(e):o.line!=a.line||1!=t.text.length||yi(e.doc,t)?$n(e,o.line,a.line+1,u):_n(e,o.line,"text");var c=lt(e,"changes"),d=lt(e,"change");if(d||c){var f={from:o,to:a,text:t.text,removed:t.removed,origin:t.origin};d&&ar(e,"change",e,f),c&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(f)}e.display.selForContextMenu=null}(e.cm,t,n):bi(e,t,n),Bi(e,r,B)}}function Ji(e,t,r,n,i){if(n||(n=r),de(n,r)<0){var o=n;n=r,r=o}"string"==typeof t&&(t=e.splitLines(t)),_i(e,{from:r,to:n,text:t,origin:i})}function eo(e,t,r,n){r<e.line?e.line+=n:t<e.line&&(e.line=t,e.ch=0)}function to(e,t,r,n){for(var i=0;i<e.length;++i){var o=e[i],a=!0;if(o.ranges){o.copied||((o=e[i]=o.deepCopy()).copied=!0);for(var l=0;l<o.ranges.length;l++)eo(o.ranges[l].anchor,t,r,n),eo(o.ranges[l].head,t,r,n)}else{for(var s=0;s<o.changes.length;++s){var u=o.changes[s];if(r<u.from.line)u.from=ce(u.from.line+n,u.from.ch),u.to=ce(u.to.line+n,u.to.ch);else if(t<=u.to.line){a=!1;break}}a||(e.splice(0,i+1),i=0)}}}function ro(e,t){var r=t.from.line,n=t.to.line,i=t.text.length-(n-r)-1;to(e.done,r,n,i),to(e.undone,r,n,i)}function no(e,t,r,n){var i=t,o=t;return"number"==typeof t?o=re(e,ge(e,t)):i=ae(t),null==i?null:(n(o,i)&&e.cm&&_n(e.cm,i,r),o)}function io(e){this.lines=e,this.parent=null;for(var t=0,r=0;r<e.length;++r)e[r].parent=this,t+=e[r].height;this.height=t}function oo(e){this.children=e;for(var t=0,r=0,n=0;n<e.length;++n){var i=e[n];t+=i.chunkSize(),r+=i.height,i.parent=this}this.size=t,this.height=r,this.parent=null}function ao(e,t,r){if(r)for(var n in r)r.hasOwnProperty(n)&&(this[n]=r[n]);this.doc=e,this.node=t}function lo(e,t,r){Be(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&zn(e,null,r)}ei.prototype.signal=function(e,t){lt(e,t)&&this.events.push(arguments)},ei.prototype.finish=function(){for(var e=0;e<this.events.length;e++)it.apply(null,this.events[e])},si.prototype={primary:function(){return this.ranges[this.primIndex]},equals:function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var r=this.ranges[t],n=e.ranges[t];if(0!=de(r.anchor,n.anchor)||0!=de(r.head,n.head))return!1}return!0},deepCopy:function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new ui(fe(this.ranges[t].anchor),fe(this.ranges[t].head));return new si(e,this.primIndex)},somethingSelected:function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},contains:function(e,t){t||(t=e);for(var r=0;r<this.ranges.length;r++){var n=this.ranges[r];if(de(t,n.from())>=0&&de(e,n.to())<=0)return r}return-1}},ui.prototype={from:function(){return pe(this.anchor,this.head)},to:function(){return he(this.anchor,this.head)},empty:function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch}},io.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var r=e,n=e+t;r<n;++r){var i=this.lines[r];this.height-=i.height,qt(i),ar(i,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,r){this.height+=r,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var n=0;n<t.length;++n)t[n].parent=this},iterN:function(e,t,r){for(var n=e+t;e<n;++e)if(r(this.lines[e]))return!0}},oo.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){var r=this;this.size-=t;for(var n=0;n<this.children.length;++n){var i=r.children[n],o=i.chunkSize();if(e<o){var a=Math.min(t,o-e),l=i.height;if(i.removeInner(e,a),r.height-=l-i.height,o==a&&(r.children.splice(n--,1),i.parent=null),0==(t-=a))break;e=0}else e-=o}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof io))){var s=[];this.collapse(s),this.children=[new io(s)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,r){var n=this;this.size+=t.length,this.height+=r;for(var i=0;i<this.children.length;++i){var o=n.children[i],a=o.chunkSize();if(e<=a){if(o.insertInner(e,t,r),o.lines&&o.lines.length>50){for(var l=o.lines.length%25+25,s=l;s<o.lines.length;){var u=new io(o.lines.slice(s,s+=25));o.height-=u.height,n.children.splice(++i,0,u),u.parent=n}o.lines=o.lines.slice(0,l),n.maybeSpill()}break}e-=a}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=new oo(e.children.splice(e.children.length-5,5));if(e.parent){e.size-=t.size,e.height-=t.height;var r=E(e.parent.children,e);e.parent.children.splice(r+1,0,t)}else{var n=new oo(e.children);n.parent=e,e.children=[n,t],e=n}t.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,r){for(var n=0;n<this.children.length;++n){var i=this.children[n],o=i.chunkSize();if(e<o){var a=Math.min(t,o-e);if(i.iterN(e,a,r))return!0;if(0==(t-=a))break;e=0}else e-=o}}},st(ao),ao.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,r=this.line,n=ae(r);if(null!=n&&t){for(var i=0;i<t.length;++i)t[i]==this&&t.splice(i--,1);t.length||(r.widgets=null);var o=br(this);oe(r,Math.max(0,r.height-o)),e&&Kn(e,(function(){lo(e,r,-o),_n(e,n,"widget")}))}},ao.prototype.changed=function(){var e=this.height,t=this.doc.cm,r=this.line;this.height=null;var n=br(this)-e;n&&(oe(r,r.height+n),t&&Kn(t,(function(){t.curOp.forceUpdate=!0,lo(t,r,n)})))};var so=0;function uo(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++so}function co(e,t,r,n,i){if(n&&n.shared)return function(e,t,r,n,i){(n=H(n)).shared=!1;var o=[co(e,t,r,n,i)],a=o[0],l=n.widgetNode;return wi(e,(function(e){l&&(n.widgetNode=l.cloneNode(!0)),o.push(co(e,me(e,t),me(e,r),n,i));for(var s=0;s<e.linked.length;++s)if(e.linked[s].isParent)return;a=U(o)})),new fo(o,a)}(e,t,r,n,i);if(e.cm&&!e.cm.curOp)return Gn(e.cm,co)(e,t,r,n,i);var o=new uo(e,i),a=de(t,r);if(n&&H(n,o,!1),a>0||0==a&&!1!==o.clearWhenEmpty)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=M("span",[o.replacedWith],"CodeMirror-widget"),n.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),n.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(De(e,t.line,t,r,o)||t.line!=r.line&&De(e,r.line,t,r,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");be=!0}o.addToHistory&&Li(e,{from:t,to:r,origin:"markText"},e.sel,NaN);var l,s=t.line,u=e.cm;if(e.iter(s,r.line+1,(function(e){u&&o.collapsed&&!u.options.lineWrapping&&He(e)==u.display.maxLine&&(l=!0),o.collapsed&&s!=t.line&&oe(e,0),function(e,t){e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],t.marker.attachLine(e)}(e,new we(o,s==t.line?t.ch:null,s==r.line?r.ch:null)),++s})),o.collapsed&&e.iter(t.line,r.line+1,(function(t){Ee(e,t)&&oe(t,0)})),o.clearOnEnter&&tt(o,"beforeCursorEnter",(function(){return o.clear()})),o.readOnly&&(ye=!0,(e.history.done.length||e.history.undone.length)&&e.clearHistory()),o.collapsed&&(o.id=++so,o.atomic=!0),u){if(l&&(u.curOp.updateMaxLine=!0),o.collapsed)$n(u,t.line,r.line+1);else if(o.className||o.title||o.startStyle||o.endStyle||o.css)for(var c=t.line;c<=r.line;c++)_n(u,c,"text");o.atomic&&ji(u.doc),ar(u,"markerAdded",u,o)}return o}function fo(e,t){this.markers=e,this.primary=t;for(var r=0;r<e.length;++r)e[r].parent=this}function ho(e){return e.findMarks(ce(e.first,0),e.clipPos(ce(e.lastLine())),(function(e){return e.parent}))}function po(e){for(var t=function(t){var r=e[t],n=[r.primary.doc];wi(r.primary.doc,(function(e){return n.push(e)}));for(var i=0;i<r.markers.length;i++){var o=r.markers[i];-1==E(n,o.doc)&&(o.parent=null,r.markers.splice(i--,1))}},r=0;r<e.length;r++)t(r)}st(uo),uo.prototype.clear=function(){var e=this;if(!this.explicitlyCleared){var t=this.doc.cm,r=t&&!t.curOp;if(r&&In(t),lt(this,"clear")){var n=this.find();n&&ar(this,"clear",n.from,n.to)}for(var i=null,o=null,a=0;a<this.lines.length;++a){var l=e.lines[a],s=xe(l.markedSpans,e);t&&!e.collapsed?_n(t,ae(l),"text"):t&&(null!=s.to&&(o=ae(l)),null!=s.from&&(i=ae(l))),l.markedSpans=ke(l.markedSpans,s),null==s.from&&e.collapsed&&!Ee(e.doc,l)&&t&&oe(l,Xr(t.display))}if(t&&this.collapsed&&!t.options.lineWrapping)for(var u=0;u<this.lines.length;++u){var c=He(e.lines[u]),d=Re(c);d>t.display.maxLineLength&&(t.display.maxLine=c,t.display.maxLineLength=d,t.display.maxLineChanged=!0)}null!=i&&t&&this.collapsed&&$n(t,i,o+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,t&&ji(t.doc)),t&&ar(t,"markerCleared",t,this),r&&En(t),this.parent&&this.parent.clear()}},uo.prototype.find=function(e,t){var r,n;null==e&&"bookmark"==this.type&&(e=1);for(var i=0;i<this.lines.length;++i){var o=this.lines[i],a=xe(o.markedSpans,this);if(null!=a.from&&(r=ce(t?o:ae(o),a.from),-1==e))return r;if(null!=a.to&&(n=ce(t?o:ae(o),a.to),1==e))return n}return r&&{from:r,to:n}},uo.prototype.changed=function(){var e=this.find(-1,!0),t=this,r=this.doc.cm;e&&r&&Kn(r,(function(){var n=e.line,i=ae(e.line),o=Ar(r,i);if(o&&(Ir(o),r.curOp.selectionChanged=r.curOp.forceUpdate=!0),r.curOp.updateMaxLine=!0,!Ee(t.doc,n)&&null!=t.height){var a=t.height;t.height=null;var l=br(t)-a;l&&oe(n,n.height+l)}}))},uo.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&-1!=E(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},uo.prototype.detachLine=function(e){if(this.lines.splice(E(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},st(fo),fo.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();ar(this,"clear")}},fo.prototype.find=function(e,t){return this.primary.find(e,t)};var go=0,mo=function(e,t,r,n){if(!(this instanceof mo))return new mo(e,t,r,n);null==r&&(r=0),oo.call(this,[new io([new Ut("",null)])]),this.first=r,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.frontier=r;var i=ce(r,0);this.sel=di(i),this.history=new ki(null),this.id=++go,this.modeOption=t,this.lineSep=n,this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),bi(this,{from:i,to:i,text:e}),Fi(this,di(i),B)};mo.prototype=_(oo.prototype,{constructor:mo,iter:function(e,t,r){r?this.iterN(e-this.first,t-e,r):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var r=0,n=0;n<t.length;++n)r+=t[n].height;this.insertInner(e-this.first,t,r)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=ie(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:qn((function(e){var t=ce(this.first,0),r=this.first+this.size-1;_i(this,{from:t,to:ce(r,re(this,r).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),Fi(this,di(t))})),replaceRange:function(e,t,r,n){Ji(this,e,t=me(this,t),r=r?me(this,r):t,n)},getRange:function(e,t,r){var n=ne(this,me(this,e),me(this,t));return!1===r?n:n.join(r||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(se(this,e))return re(this,e)},getLineNumber:function(e){return ae(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=re(this,e)),He(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return me(this,e)},getCursor:function(e){var t=this.sel.primary();return null==e||"head"==e?t.head:"anchor"==e?t.anchor:"end"==e||"to"==e||!1===e?t.to():t.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:qn((function(e,t,r){Ii(this,me(this,"number"==typeof e?ce(e,t||0):e),null,r)})),setSelection:qn((function(e,t,r){Ii(this,me(this,e),me(this,t||e),r)})),extendSelection:qn((function(e,t,r){Di(this,me(this,e),t&&me(this,t),r)})),extendSelections:qn((function(e,t){Hi(this,ve(this,e),t)})),extendSelectionsBy:qn((function(e,t){Hi(this,ve(this,q(this.sel.ranges,e)),t)})),setSelections:qn((function(e,t,r){if(e.length){for(var n=[],i=0;i<e.length;i++)n[i]=new ui(me(this,e[i].anchor),me(this,e[i].head));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),Fi(this,ci(n,t),r)}})),addSelection:qn((function(e,t,r){var n=this.sel.ranges.slice(0);n.push(new ui(me(this,e),me(this,t||e))),Fi(this,ci(n,n.length-1),r)})),getSelection:function(e){for(var t,r=this.sel.ranges,n=0;n<r.length;n++){var i=ne(this,r[n].from(),r[n].to());t=t?t.concat(i):i}return!1===e?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],r=this.sel.ranges,n=0;n<r.length;n++){var i=ne(this,r[n].from(),r[n].to());!1!==e&&(i=i.join(e||this.lineSeparator())),t[n]=i}return t},replaceSelection:function(e,t,r){for(var n=[],i=0;i<this.sel.ranges.length;i++)n[i]=e;this.replaceSelections(n,t,r||"+input")},replaceSelections:qn((function(e,t,r){for(var n=[],i=this.sel,o=0;o<i.ranges.length;o++){var a=i.ranges[o];n[o]={from:a.from(),to:a.to(),text:this.splitLines(e[o]),origin:r}}for(var l=t&&"end"!=t&&function(e,t,r){for(var n=[],i=ce(e.first,0),o=i,a=0;a<t.length;a++){var l=t[a],s=gi(l.from,i,o),u=gi(fi(l),i,o);if(i=l.to,o=u,"around"==r){var c=e.sel.ranges[a],d=de(c.head,c.anchor)<0;n[a]=new ui(d?u:s,d?s:u)}else n[a]=new ui(s,s)}return new si(n,e.sel.primIndex)}(this,n,t),s=n.length-1;s>=0;s--)_i(this,n[s]);l?Ei(this,l):this.cm&&Dn(this.cm)})),undo:qn((function(){Yi(this,"undo")})),redo:qn((function(){Yi(this,"redo")})),undoSelection:qn((function(){Yi(this,"undo",!0)})),redoSelection:qn((function(){Yi(this,"redo",!0)})),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,r=0,n=0;n<e.done.length;n++)e.done[n].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++r;return{undo:t,redo:r}},clearHistory:function(){this.history=new ki(this.history.maxGeneration)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:Wi(this.history.done),undone:Wi(this.history.undone)}},setHistory:function(e){var t=this.history=new ki(this.history.maxGeneration);t.done=Wi(e.done.slice(0),null,!0),t.undone=Wi(e.undone.slice(0),null,!0)},setGutterMarker:qn((function(e,t,r){return no(this,e,"gutter",(function(e){var n=e.gutterMarkers||(e.gutterMarkers={});return n[t]=r,!r&&Q(n)&&(e.gutterMarkers=null),!0}))})),clearGutter:qn((function(e){var t=this;this.first;this.iter((function(r){r.gutterMarkers&&r.gutterMarkers[e]&&no(t,r,"gutter",(function(){return r.gutterMarkers[e]=null,Q(r.gutterMarkers)&&(r.gutterMarkers=null),!0}))}))})),lineInfo:function(e){var t;if("number"==typeof e){if(!se(this,e))return null;if(t=e,!(e=re(this,e)))return null}else if(null==(t=ae(e)))return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:qn((function(e,t,r){return no(this,e,"gutter"==t?"gutter":"class",(function(e){var n="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass";if(e[n]){if(k(r).test(e[n]))return!1;e[n]+=" "+r}else e[n]=r;return!0}))})),removeLineClass:qn((function(e,t,r){return no(this,e,"gutter"==t?"gutter":"class",(function(e){var n="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass",i=e[n];if(!i)return!1;if(null==r)e[n]=null;else{var o=i.match(k(r));if(!o)return!1;var a=o.index+o[0].length;e[n]=i.slice(0,o.index)+(o.index&&a!=i.length?" ":"")+i.slice(a)||null}return!0}))})),addLineWidget:qn((function(e,t,r){return function(e,t,r,n){var i=new ao(e,r,n),o=e.cm;return o&&i.noHScroll&&(o.display.alignWidgets=!0),no(e,t,"widget",(function(t){var r=t.widgets||(t.widgets=[]);if(null==i.insertAt?r.push(i):r.splice(Math.min(r.length-1,Math.max(0,i.insertAt)),0,i),i.line=t,o&&!Ee(e,t)){var n=Be(t)<e.scrollTop;oe(t,t.height+br(i)),n&&zn(o,null,i.height),o.curOp.forceUpdate=!0}return!0})),i}(this,e,t,r)})),removeLineWidget:function(e){e.clear()},markText:function(e,t,r){return co(this,me(this,e),me(this,t),r,r&&r.type||"range")},setBookmark:function(e,t){var r={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return co(this,e=me(this,e),e,r,"bookmark")},findMarksAt:function(e){var t=[],r=re(this,(e=me(this,e)).line).markedSpans;if(r)for(var n=0;n<r.length;++n){var i=r[n];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(e,t,r){e=me(this,e),t=me(this,t);var n=[],i=e.line;return this.iter(e.line,t.line+1,(function(o){var a=o.markedSpans;if(a)for(var l=0;l<a.length;l++){var s=a[l];null!=s.to&&i==e.line&&e.ch>=s.to||null==s.from&&i!=e.line||null!=s.from&&i==t.line&&s.from>=t.ch||r&&!r(s.marker)||n.push(s.marker.parent||s.marker)}++i})),n},getAllMarks:function(){var e=[];return this.iter((function(t){var r=t.markedSpans;if(r)for(var n=0;n<r.length;++n)null!=r[n].from&&e.push(r[n].marker)})),e},posFromIndex:function(e){var t,r=this.first,n=this.lineSeparator().length;return this.iter((function(i){var o=i.text.length+n;if(o>e)return t=e,!0;e-=o,++r})),me(this,ce(r,t))},indexFromPos:function(e){var t=(e=me(this,e)).ch;if(e.line<this.first||e.ch<0)return 0;var r=this.lineSeparator().length;return this.iter(this.first,e.line,(function(e){t+=e.text.length+r})),t},copy:function(e){var t=new mo(ie(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,r=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<r&&(r=e.to);var n=new mo(ie(this,t,r),e.mode||this.modeOption,t,this.lineSep);return e.sharedHist&&(n.history=this.history),(this.linked||(this.linked=[])).push({doc:n,sharedHist:e.sharedHist}),n.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r],i=n.find(),o=e.clipPos(i.from),a=e.clipPos(i.to);if(de(o,a)){var l=co(e,o,a,n.primary,n.primary.type);n.markers.push(l),l.parent=n}}}(n,ho(this)),n},unlinkDoc:function(e){var t=this;if(e instanceof aa&&(e=e.doc),this.linked)for(var r=0;r<this.linked.length;++r){if(t.linked[r].doc==e){t.linked.splice(r,1),e.unlinkDoc(t),po(ho(t));break}}if(e.history==this.history){var n=[e.id];wi(e,(function(e){return n.push(e.id)}),!0),e.history=new ki(null),e.history.done=Wi(this.history.done,n),e.history.undone=Wi(this.history.undone,n)}},iterLinkedDocs:function(e){wi(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):xt(e)},lineSeparator:function(){return this.lineSep||"\n"}}),mo.prototype.eachLine=mo.prototype.iter;var vo=0;function yo(e){var t=this;if(bo(t),!ot(t,e)&&!wr(t.display,e)){ut(e),o&&(vo=+new Date);var r=tn(t,e,!0),n=e.dataTransfer.files;if(r&&!t.isReadOnly())if(n&&n.length&&window.FileReader&&window.File)for(var i=n.length,a=Array(i),l=0,s=function(e,n){if(!t.options.allowDropFileTypes||-1!=E(t.options.allowDropFileTypes,e.type)){var o=new FileReader;o.onload=Gn(t,(function(){var e=o.result;if(/[\x00-\x08\x0e-\x1f]{2}/.test(e)&&(e=""),a[n]=e,++l==i){var s={from:r=me(t.doc,r),to:r,text:t.doc.splitLines(a.join(t.doc.lineSeparator())),origin:"paste"};_i(t.doc,s),Ei(t.doc,di(r,fi(s)))}})),o.readAsText(e)}},u=0;u<i;++u)s(n[u],u);else{if(t.state.draggingText&&t.doc.sel.contains(r)>-1)return t.state.draggingText(e),void setTimeout((function(){return t.display.input.focus()}),20);try{var c=e.dataTransfer.getData("Text");if(c){var d;if(t.state.draggingText&&!t.state.draggingText.copy&&(d=t.listSelections()),Bi(t.doc,di(r,r)),d)for(var f=0;f<d.length;++f)Ji(t.doc,"",d[f].anchor,d[f].head,"drag");t.replaceSelection(c,"around","paste"),t.display.input.focus()}}catch(e){}}}}function bo(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function wo(e){if(document.body.getElementsByClassName)for(var t=document.body.getElementsByClassName("CodeMirror"),r=0;r<t.length;r++){var n=t[r].CodeMirror;n&&e(n)}}var xo=!1;function ko(){var e;xo||(tt(window,"resize",(function(){null==e&&(e=setTimeout((function(){e=null,wo(Co)}),100))})),tt(window,"blur",(function(){return wo(dn)})),xo=!0)}function Co(e){var t=e.display;t.lastWrapHeight==t.wrapper.clientHeight&&t.lastWrapWidth==t.wrapper.clientWidth||(t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize())}for(var So={3:"Enter",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",127:"Delete",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},Lo=0;Lo<10;Lo++)So[Lo+48]=So[Lo+96]=String(Lo);for(var To=65;To<=90;To++)So[To]=String.fromCharCode(To);for(var Mo=1;Mo<=12;Mo++)So[Mo+111]=So[Mo+63235]="F"+Mo;var No={};function Ao(e){var t,r,n,i,o=e.split(/-(?!$)/);e=o[o.length-1];for(var a=0;a<o.length-1;a++){var l=o[a];if(/^(cmd|meta|m)$/i.test(l))i=!0;else if(/^a(lt)?$/i.test(l))t=!0;else if(/^(c|ctrl|control)$/i.test(l))r=!0;else{if(!/^s(hift)?$/i.test(l))throw new Error("Unrecognized modifier name: "+l);n=!0}}return t&&(e="Alt-"+e),r&&(e="Ctrl-"+e),i&&(e="Cmd-"+e),n&&(e="Shift-"+e),e}function Oo(e){var t={};for(var r in e)if(e.hasOwnProperty(r)){var n=e[r];if(/^(name|fallthrough|(de|at)tach)$/.test(r))continue;if("..."==n){delete e[r];continue}for(var i=q(r.split(" "),Ao),o=0;o<i.length;o++){var a=void 0,l=void 0;o==i.length-1?(l=i.join(" "),a=n):(l=i.slice(0,o+1).join(" "),a="...");var s=t[l];if(s){if(s!=a)throw new Error("Inconsistent bindings for "+l)}else t[l]=a}delete e[r]}for(var u in t)e[u]=t[u];return e}function Wo(e,t,r,n){var i=(t=Ho(t)).call?t.call(e,n):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&r(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return Wo(e,t.fallthrough,r,n);for(var o=0;o<t.fallthrough.length;o++){var a=Wo(e,t.fallthrough[o],r,n);if(a)return a}}}function zo(e){var t="string"==typeof e?e:So[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function Do(e,t){if(c&&34==e.keyCode&&e.char)return!1;var r=So[e.keyCode],n=r;return null!=n&&!e.altGraphKey&&(e.altKey&&"Alt"!=r&&(n="Alt-"+n),(w?e.metaKey:e.ctrlKey)&&"Ctrl"!=r&&(n="Ctrl-"+n),(w?e.ctrlKey:e.metaKey)&&"Cmd"!=r&&(n="Cmd-"+n),!t&&e.shiftKey&&"Shift"!=r&&(n="Shift-"+n),n)}function Ho(e){return"string"==typeof e?No[e]:e}function Po(e,t){for(var r=e.doc.sel.ranges,n=[],i=0;i<r.length;i++){for(var o=t(r[i]);n.length&&de(o.from,U(n).to)<=0;){var a=n.pop();if(de(a.from,o.from)<0){o.from=a.from;break}}n.push(o)}Kn(e,(function(){for(var t=n.length-1;t>=0;t--)Ji(e.doc,"",n[t].from,n[t].to,"+delete");Dn(e)}))}No.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},No.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},No.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Alt-F":"goWordRight","Alt-B":"goWordLeft","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-D":"delWordAfter","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},No.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},No.default=m?No.macDefault:No.pcDefault;var Io={selectAll:qi,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),B)},killLine:function(e){return Po(e,(function(t){if(t.empty()){var r=re(e.doc,t.head.line).text.length;return t.head.ch==r&&t.head.line<e.lastLine()?{from:t.head,to:ce(t.head.line+1,0)}:{from:t.head,to:ce(t.head.line,r)}}return{from:t.from(),to:t.to()}}))},deleteLine:function(e){return Po(e,(function(t){return{from:ce(t.from().line,0),to:me(e.doc,ce(t.to().line+1,0))}}))},delLineLeft:function(e){return Po(e,(function(e){return{from:ce(e.from().line,0),to:e.from()}}))},delWrappedLineLeft:function(e){return Po(e,(function(t){var r=e.charCoords(t.head,"div").top+5;return{from:e.coordsChar({left:0,top:r},"div"),to:t.from()}}))},delWrappedLineRight:function(e){return Po(e,(function(t){var r=e.charCoords(t.head,"div").top+5,n=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div");return{from:t.from(),to:n}}))},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(ce(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(ce(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy((function(t){return Eo(e,t.head.line)}),{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy((function(t){return Fo(e,t.head)}),{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy((function(t){return function(e,t){var r,n=re(e.doc,t);for(;r=ze(n);)n=r.find(1,!0).line,t=null;var i=Je(n),o=i?i[0].level%2?Ge(n):Ue(n):n.text.length;return ce(null==t?ae(n):t,o)}(e,t.head.line)}),{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy((function(t){var r=e.charCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div")}),j)},goLineLeft:function(e){return e.extendSelectionsBy((function(t){var r=e.charCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:r},"div")}),j)},goLineLeftSmart:function(e){return e.extendSelectionsBy((function(t){var r=e.charCoords(t.head,"div").top+5,n=e.coordsChar({left:0,top:r},"div");return n.ch<e.getLine(n.line).search(/\S/)?Fo(e,t.head):n}),j)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"char")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],r=e.listSelections(),n=e.options.tabSize,i=0;i<r.length;i++){var o=r[i].from(),a=P(e.getLine(o.line),o.ch,n);t.push(G(n-a%n))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return Kn(e,(function(){for(var t=e.listSelections(),r=[],n=0;n<t.length;n++)if(t[n].empty()){var i=t[n].head,o=re(e.doc,i.line).text;if(o)if(i.ch==o.length&&(i=new ce(i.line,i.ch-1)),i.ch>0)i=new ce(i.line,i.ch+1),e.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),ce(i.line,i.ch-2),i,"+transpose");else if(i.line>e.doc.first){var a=re(e.doc,i.line-1).text;a&&(i=new ce(i.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+a.charAt(a.length-1),ce(i.line-1,a.length-1),i,"+transpose"))}r.push(new ui(i,i))}e.setSelections(r)}))},newlineAndIndent:function(e){return Kn(e,(function(){for(var t=e.listSelections(),r=t.length-1;r>=0;r--)e.replaceRange(e.doc.lineSeparator(),t[r].anchor,t[r].head,"+input");t=e.listSelections();for(var n=0;n<t.length;n++)e.indentLine(t[n].from().line,null,!0);Dn(e)}))},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function Eo(e,t){var r=re(e.doc,t),n=He(r);n!=r&&(t=ae(n));var i=Je(n);return ce(t,i?i[0].level%2?Ue(n):Ge(n):0)}function Fo(e,t){var r=Eo(e,t.line),n=re(e.doc,r.line),i=Je(n);if(!i||0==i[0].level){var o=Math.max(0,n.text.search(/\S/)),a=t.line==r.line&&t.ch<=o&&t.ch;return ce(r.line,a?0:o)}return r}function Bo(e,t,r){if("string"==typeof t&&!(t=Io[t]))return!1;e.display.input.ensurePolled();var n=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),r&&(e.display.shift=!1),i=t(e)!=F}finally{e.display.shift=n,e.state.suppressEdits=!1}return i}var Ro=new I;function jo(e,t,r,n){var i=e.state.keySeq;if(i){if(zo(t))return"handled";Ro.set(50,(function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())})),t=i+" "+t}var o=function(e,t,r){for(var n=0;n<e.state.keyMaps.length;n++){var i=Wo(t,e.state.keyMaps[n],r,e);if(i)return i}return e.options.extraKeys&&Wo(t,e.options.extraKeys,r,e)||Wo(t,e.options.keyMap,r,e)}(e,t,n);return"multi"==o&&(e.state.keySeq=t),"handled"==o&&ar(e,"keyHandled",e,t,r),"handled"!=o&&"multi"!=o||(ut(r),sn(e)),i&&!o&&/\'$/.test(t)?(ut(r),!0):!!o}function Vo(e,t){var r=Do(t,!0);return!!r&&(t.shiftKey&&!e.state.keySeq?jo(e,"Shift-"+r,t,(function(t){return Bo(e,t,!0)}))||jo(e,r,t,(function(t){if("string"==typeof t?/^go[A-Z]/.test(t):t.motion)return Bo(e,t)})):jo(e,r,t,(function(t){return Bo(e,t)})))}var Ko,Go,Uo=null;function qo(e){var t=this;if(t.curOp.focus=A(),!ot(t,e)){o&&a<11&&27==e.keyCode&&(e.returnValue=!1);var r=e.keyCode;t.display.shift=16==r||e.shiftKey;var n=Vo(t,e);c&&(Uo=n?r:null,n||88!=r||Ct||!(m?e.metaKey:e.ctrlKey)||t.replaceSelection("",null,"cut")),18!=r||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||function(e){var t=e.display.lineDiv;function r(e){18!=e.keyCode&&e.altKey||(S(t,"CodeMirror-crosshair"),nt(document,"keyup",r),nt(document,"mouseover",r))}O(t,"CodeMirror-crosshair"),tt(document,"keyup",r),tt(document,"mouseover",r)}(t)}}function $o(e){16==e.keyCode&&(this.doc.sel.shift=!1),ot(this,e)}function _o(e){var t=this;if(!(wr(t.display,e)||ot(t,e)||e.ctrlKey&&!e.altKey||m&&e.metaKey)){var r=e.keyCode,n=e.charCode;if(c&&r==Uo)return Uo=null,void ut(e);if(!c||e.which&&!(e.which<10)||!Vo(t,e)){var i=String.fromCharCode(null==n?r:n);"\b"!=i&&(function(e,t,r){return jo(e,"'"+r+"'",t,(function(t){return Bo(e,t,!0)}))}(t,e,i)||t.display.input.onKeyPress(e))}}}function Xo(e){var t=this,r=t.display;if(!(ot(t,e)||r.activeTouch&&r.input.supportsTouch()))if(r.input.ensurePolled(),r.shift=e.shiftKey,wr(r,e))l||(r.scroller.draggable=!1,setTimeout((function(){return r.scroller.draggable=!0}),100));else if(!Zo(t,e)){var n=tn(t,e);switch(window.focus(),pt(e)){case 1:t.state.selectingText?t.state.selectingText(e):n?function(e,t,r){o?setTimeout(D(un,e),0):e.curOp.focus=A();var n,i=+new Date;Go&&Go.time>i-400&&0==de(Go.pos,r)?n="triple":Ko&&Ko.time>i-400&&0==de(Ko.pos,r)?(n="double",Go={time:i,pos:r}):(n="single",Ko={time:i,pos:r});var s,u=e.doc.sel,c=m?t.metaKey:t.ctrlKey;e.options.dragDrop&&vt&&!e.isReadOnly()&&"single"==n&&(s=u.contains(r))>-1&&(de((s=u.ranges[s]).from(),r)<0||r.xRel>0)&&(de(s.to(),r)>0||r.xRel<0)?function(e,t,r,n){var i=e.display,s=+new Date,u=Gn(e,(function(c){l&&(i.scroller.draggable=!1),e.state.draggingText=!1,nt(document,"mouseup",u),nt(i.scroller,"drop",u),Math.abs(t.clientX-c.clientX)+Math.abs(t.clientY-c.clientY)<10&&(ut(c),!n&&+new Date-200<s&&Di(e.doc,r),l||o&&9==a?setTimeout((function(){document.body.focus(),i.input.focus()}),20):i.input.focus())}));l&&(i.scroller.draggable=!0);e.state.draggingText=u,u.copy=m?t.altKey:t.ctrlKey,i.scroller.dragDrop&&i.scroller.dragDrop();tt(document,"mouseup",u),tt(i.scroller,"drop",u)}(e,t,r,c):function(e,t,r,n,i){var o=e.display,a=e.doc;ut(t);var l,s,u=a.sel,c=u.ranges;i&&!t.shiftKey?(s=a.sel.contains(r),l=s>-1?c[s]:new ui(r,r)):(l=a.sel.primary(),s=a.sel.primIndex);if(v?t.shiftKey&&t.metaKey:t.altKey)n="rect",i||(l=new ui(r,r)),r=tn(e,t,!0,!0),s=-1;else if("double"==n){var d=e.findWordAt(r);l=e.display.shift||a.extend?zi(a,l,d.anchor,d.head):d}else if("triple"==n){var f=new ui(ce(r.line,0),me(a,ce(r.line+1,0)));l=e.display.shift||a.extend?zi(a,l,f.anchor,f.head):f}else l=zi(a,l,r);i?-1==s?(s=c.length,Fi(a,ci(c.concat([l]),s),{scroll:!1,origin:"*mouse"})):c.length>1&&c[s].empty()&&"single"==n&&!t.shiftKey?(Fi(a,ci(c.slice(0,s).concat(c.slice(s+1)),0),{scroll:!1,origin:"*mouse"}),u=a.sel):Pi(a,s,l,R):(s=0,Fi(a,new si([l],0),R),u=a.sel);var h=r;function p(t){if(0!=de(h,t))if(h=t,"rect"==n){for(var i=[],o=e.options.tabSize,c=P(re(a,r.line).text,r.ch,o),d=P(re(a,t.line).text,t.ch,o),f=Math.min(c,d),p=Math.max(c,d),g=Math.min(r.line,t.line),m=Math.min(e.lastLine(),Math.max(r.line,t.line));g<=m;g++){var v=re(a,g).text,y=V(v,f,o);f==p?i.push(new ui(ce(g,y),ce(g,y))):v.length>y&&i.push(new ui(ce(g,y),ce(g,V(v,p,o))))}i.length||i.push(new ui(r,r)),Fi(a,ci(u.ranges.slice(0,s).concat(i),s),{origin:"*mouse",scroll:!1}),e.scrollIntoView(t)}else{var b,w=l,x=w.anchor,k=t;if("single"!=n)de((b="double"==n?e.findWordAt(t):new ui(ce(t.line,0),me(a,ce(t.line+1,0)))).anchor,x)>0?(k=b.head,x=pe(w.from(),b.anchor)):(k=b.anchor,x=he(w.to(),b.head));var C=u.ranges.slice(0);C[s]=new ui(me(a,x),k),Fi(a,ci(C,s),R)}}var g=o.wrapper.getBoundingClientRect(),m=0;function y(t){var r=++m,i=tn(e,t,!0,"rect"==n);if(i)if(0!=de(i,h)){e.curOp.focus=A(),p(i);var l=mn(o,a);(i.line>=l.to||i.line<l.from)&&setTimeout(Gn(e,(function(){m==r&&y(t)})),150)}else{var s=t.clientY<g.top?-20:t.clientY>g.bottom?20:0;s&&setTimeout(Gn(e,(function(){m==r&&(o.scroller.scrollTop+=s,y(t))})),50)}}function b(t){e.state.selectingText=!1,m=1/0,ut(t),o.input.focus(),nt(document,"mousemove",w),nt(document,"mouseup",x),a.history.lastSelOrigin=null}var w=Gn(e,(function(e){pt(e)?y(e):b(e)})),x=Gn(e,b);e.state.selectingText=x,tt(document,"mousemove",w),tt(document,"mouseup",x)}(e,t,r,n,c)}(t,e,n):ht(e)==r.scroller&&ut(e);break;case 2:l&&(t.state.lastMiddleDown=+new Date),n&&Di(t.doc,n),setTimeout((function(){return r.input.focus()}),20),ut(e);break;case 3:x?Qo(t,e):function(e){e.state.delayingBlurEvent=!0,setTimeout((function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,dn(e))}),100)}(t)}}}function Yo(e,t,r,n){var i,o;try{i=t.clientX,o=t.clientY}catch(t){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;n&&ut(t);var a=e.display,l=a.lineDiv.getBoundingClientRect();if(o>l.bottom||!lt(e,r))return dt(t);o-=l.top-a.viewOffset;for(var s=0;s<e.options.gutters.length;++s){var u=a.gutters.childNodes[s];if(u&&u.getBoundingClientRect().right>=i)return it(e,r,e,le(e.doc,o),e.options.gutters[s],t),dt(t)}}function Zo(e,t){return Yo(e,t,"gutterClick",!0)}function Qo(e,t){wr(e.display,t)||function(e,t){if(!lt(e,"gutterContextMenu"))return!1;return Yo(e,t,"gutterContextMenu",!1)}(e,t)||ot(e,t,"contextmenu")||e.display.input.onContextMenu(t)}function Jo(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),Fr(e)}var ea={toString:function(){return"CodeMirror.Init"}},ta={},ra={};function na(e){ai(e),$n(e),setTimeout((function(){return fn(e)}),20)}function ia(e,t,r){if(!t!=!(r&&r!=ea)){var n=e.display.dragFunctions,i=t?tt:nt;i(e.display.scroller,"dragstart",n.start),i(e.display.scroller,"dragenter",n.enter),i(e.display.scroller,"dragover",n.over),i(e.display.scroller,"dragleave",n.leave),i(e.display.scroller,"drop",n.drop)}}function oa(e){e.options.lineWrapping?(O(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(S(e.display.wrapper,"CodeMirror-wrap"),je(e)),en(e),$n(e),Fr(e),setTimeout((function(){return Mn(e)}),100)}function aa(e,t){var r=this;if(!(this instanceof aa))return new aa(e,t);this.options=t=t?H(t):{},H(ta,t,!1),li(t);var n=t.value;"string"==typeof n&&(n=new mo(n,t.mode,null,t.lineSeparator)),this.doc=n;var i=new aa.inputStyles[t.inputStyle](this),s=this.display=new te(e,n,i);for(var u in s.wrapper.CodeMirror=this,ai(this),Jo(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),t.autofocus&&!g&&s.input.focus(),On(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:!1,cutIncoming:!1,selectingText:!1,draggingText:!1,highlight:new I,keySeq:null,specialChars:null},o&&a<11&&setTimeout((function(){return r.display.input.reset(!0)}),20),function(e){var t=e.display;tt(t.scroller,"mousedown",Gn(e,Xo)),tt(t.scroller,"dblclick",o&&a<11?Gn(e,(function(t){if(!ot(e,t)){var r=tn(e,t);if(r&&!Zo(e,t)&&!wr(e.display,t)){ut(t);var n=e.findWordAt(r);Di(e.doc,n.anchor,n.head)}}})):function(t){return ot(e,t)||ut(t)});x||tt(t.scroller,"contextmenu",(function(t){return Qo(e,t)}));var r,n={end:0};function i(){t.activeTouch&&(r=setTimeout((function(){return t.activeTouch=null}),1e3),(n=t.activeTouch).end=+new Date)}function l(e){if(1!=e.touches.length)return!1;var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}function s(e,t){if(null==t.left)return!0;var r=t.left-e.left,n=t.top-e.top;return r*r+n*n>400}tt(t.scroller,"touchstart",(function(i){if(!ot(e,i)&&!l(i)){t.input.ensurePolled(),clearTimeout(r);var o=+new Date;t.activeTouch={start:o,moved:!1,prev:o-n.end<=300?n:null},1==i.touches.length&&(t.activeTouch.left=i.touches[0].pageX,t.activeTouch.top=i.touches[0].pageY)}})),tt(t.scroller,"touchmove",(function(){t.activeTouch&&(t.activeTouch.moved=!0)})),tt(t.scroller,"touchend",(function(r){var n=t.activeTouch;if(n&&!wr(t,r)&&null!=n.left&&!n.moved&&new Date-n.start<300){var o,a=e.coordsChar(t.activeTouch,"page");o=!n.prev||s(n,n.prev)?new ui(a,a):!n.prev.prev||s(n,n.prev.prev)?e.findWordAt(a):new ui(ce(a.line,0),me(e.doc,ce(a.line+1,0))),e.setSelection(o.anchor,o.head),e.focus(),ut(r)}i()})),tt(t.scroller,"touchcancel",i),tt(t.scroller,"scroll",(function(){t.scroller.clientHeight&&(vn(e,t.scroller.scrollTop),yn(e,t.scroller.scrollLeft,!0),it(e,"scroll",e))})),tt(t.scroller,"mousewheel",(function(t){return Cn(e,t)})),tt(t.scroller,"DOMMouseScroll",(function(t){return Cn(e,t)})),tt(t.wrapper,"scroll",(function(){return t.wrapper.scrollTop=t.wrapper.scrollLeft=0})),t.dragFunctions={enter:function(t){ot(e,t)||ft(t)},over:function(t){ot(e,t)||(!function(e,t){var r=tn(e,t);if(r){var n=document.createDocumentFragment();an(e,r,n),e.display.dragCursor||(e.display.dragCursor=M("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),T(e.display.dragCursor,n)}}(e,t),ft(t))},start:function(t){return function(e,t){if(o&&(!e.state.draggingText||+new Date-vo<100))ft(t);else if(!ot(e,t)&&!wr(e.display,t)&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!d)){var r=M("img",null,null,"position: fixed; left: 0; top: 0;");r.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",c&&(r.width=r.height=1,e.display.wrapper.appendChild(r),r._top=r.offsetTop),t.dataTransfer.setDragImage(r,0,0),c&&r.parentNode.removeChild(r)}}(e,t)},drop:Gn(e,yo),leave:function(t){ot(e,t)||bo(e)}};var u=t.input.getField();tt(u,"keyup",(function(t){return $o.call(e,t)})),tt(u,"keydown",Gn(e,qo)),tt(u,"keypress",Gn(e,_o)),tt(u,"focus",(function(t){return cn(e,t)})),tt(u,"blur",(function(t){return dn(e,t)}))}(this),ko(),In(this),this.curOp.forceUpdate=!0,xi(this,n),t.autofocus&&!g||this.hasFocus()?setTimeout(D(cn,this),20):dn(this),ra)ra.hasOwnProperty(u)&&ra[u](r,t[u],ea);hn(this),t.finishInit&&t.finishInit(this);for(var f=0;f<la.length;++f)la[f](r);En(this),l&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(s.lineDiv).textRendering&&(s.lineDiv.style.textRendering="auto")}aa.defaults=ta,aa.optionHandlers=ra;var la=[];function sa(e,t,r,n){var i,o=e.doc;null==r&&(r="add"),"smart"==r&&(o.mode.indent?i=Ft(e,t):r="prev");var a=e.options.tabSize,l=re(o,t),s=P(l.text,null,a);l.stateAfter&&(l.stateAfter=null);var u,c=l.text.match(/^\s*/)[0];if(n||/\S/.test(l.text)){if("smart"==r&&((u=o.mode.indent(i,l.text.slice(c.length),l.text))==F||u>150)){if(!n)return;r="prev"}}else u=0,r="not";"prev"==r?u=t>o.first?P(re(o,t-1).text,null,a):0:"add"==r?u=s+e.options.indentUnit:"subtract"==r?u=s-e.options.indentUnit:"number"==typeof r&&(u=s+r),u=Math.max(0,u);var d="",f=0;if(e.options.indentWithTabs)for(var h=Math.floor(u/a);h;--h)f+=a,d+="\t";if(f<u&&(d+=G(u-f)),d!=c)return Ji(o,d,ce(t,0),ce(t,c.length),"+input"),l.stateAfter=null,!0;for(var p=0;p<o.sel.ranges.length;p++){var g=o.sel.ranges[p];if(g.head.line==t&&g.head.ch<c.length){var m=ce(t,c.length);Pi(o,p,new ui(m,m));break}}}aa.defineInitHook=function(e){return la.push(e)};var ua=null;function ca(e){ua=e}function da(e,t,r,n,i){var o=e.doc;e.display.shift=!1,n||(n=o.sel);var a,l=e.state.pasteIncoming||"paste"==i,s=xt(t),u=null;if(l&&n.ranges.length>1)if(ua&&ua.text.join("\n")==t){if(n.ranges.length%ua.text.length==0){u=[];for(var c=0;c<ua.text.length;c++)u.push(o.splitLines(ua.text[c]))}}else s.length==n.ranges.length&&(u=q(s,(function(e){return[e]})));for(var d=n.ranges.length-1;d>=0;d--){var f=n.ranges[d],h=f.from(),p=f.to();f.empty()&&(r&&r>0?h=ce(h.line,h.ch-r):e.state.overwrite&&!l?p=ce(p.line,Math.min(re(o,p.line).text.length,p.ch+U(s).length)):ua&&ua.lineWise&&ua.text.join("\n")==t&&(h=p=ce(h.line,0))),a=e.curOp.updateInput;var g={from:h,to:p,text:u?u[d%u.length]:s,origin:i||(l?"paste":e.state.cutIncoming?"cut":"+input")};_i(e.doc,g),ar(e,"inputRead",e,g)}t&&!l&&ha(e,t),Dn(e),e.curOp.updateInput=a,e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=!1}function fa(e,t){var r=e.clipboardData&&e.clipboardData.getData("Text");if(r)return e.preventDefault(),t.isReadOnly()||t.options.disableInput||Kn(t,(function(){return da(t,r,0,null,"paste")})),!0}function ha(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var r=e.doc.sel,n=r.ranges.length-1;n>=0;n--){var i=r.ranges[n];if(!(i.head.ch>100||n&&r.ranges[n-1].head.line==i.head.line)){var o=e.getModeAt(i.head),a=!1;if(o.electricChars){for(var l=0;l<o.electricChars.length;l++)if(t.indexOf(o.electricChars.charAt(l))>-1){a=sa(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(re(e.doc,i.head.line).text.slice(0,i.head.ch))&&(a=sa(e,i.head.line,"smart"));a&&ar(e,"electricInput",e,i.head.line)}}}function pa(e){for(var t=[],r=[],n=0;n<e.doc.sel.ranges.length;n++){var i=e.doc.sel.ranges[n].head.line,o={anchor:ce(i,0),head:ce(i+1,0)};r.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:r}}function ga(e,t){e.setAttribute("autocorrect","off"),e.setAttribute("autocapitalize","off"),e.setAttribute("spellcheck",!!t)}function ma(){var e=M("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; outline: none"),t=M("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return l?e.style.width="1000px":e.setAttribute("wrap","off"),p&&(e.style.border="1px solid black"),ga(e),t}function va(e,t,r,n,i){var o=t.line,a=t.ch,l=r,s=re(e,o);function u(t){var n,l=(i?Ye:Ze)(s,a,r,!0);if(null==l){if(t||(n=o+r)<e.first||n>=e.first+e.size||(o=n,!(s=re(e,n))))return!1;a=i?(r<0?Ue:Ge)(s):r<0?s.text.length:0}else a=l;return!0}if("char"==n)u();else if("column"==n)u(!0);else if("word"==n||"group"==n)for(var c=null,d="group"==n,f=e.cm&&e.cm.getHelper(t,"wordChars"),h=!0;!(r<0)||u(!h);h=!1){var p=s.text.charAt(a)||"\n",g=Z(p,f)?"w":d&&"\n"==p?"n":!d||/\s/.test(p)?null:"p";if(!d||h||g||(g="s"),c&&c!=g){r<0&&(r=1,u());break}if(g&&(c=g),r>0&&!u(!h))break}var m=Gi(e,ce(o,a),t,l,!0);return de(t,m)||(m.hitSide=!0),m}function ya(e,t,r,n){var i,o,a=e.doc,l=t.left;if("page"==n){var s=Math.min(e.display.wrapper.clientHeight,window.innerHeight||document.documentElement.clientHeight),u=Math.max(s-.5*Xr(e.display),3);i=(r>0?t.bottom:t.top)+r*u}else"line"==n&&(i=r>0?t.bottom+3:t.top-3);for(;(o=$r(e,l,i)).outside;){if(r<0?i<=0:i>=a.height){o.hitSide=!0;break}i+=5*r}return o}function ba(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new I,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null}function wa(e,t){var r=Ar(e,t.line);if(!r||r.hidden)return null;var n=re(e.doc,t.line),i=Mr(r,n,t.line),o=Je(n),a="left";o&&(a=_e(o,t.ch)%2?"right":"left");var l=Hr(i.map,t.ch,a);return l.offset="right"==l.collapse?l.end:l.start,l}function xa(e,t){return t&&(e.bad=!0),e}function ka(e,t,r){var n;if(t==e.display.lineDiv){if(!(n=e.display.lineDiv.childNodes[r]))return xa(e.clipPos(ce(e.display.viewTo-1)),!0);t=null,r=0}else for(n=t;;n=n.parentNode){if(!n||n==e.display.lineDiv)return null;if(n.parentNode&&n.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==n)return Ca(o,t,r)}}function Ca(e,t,r){var n=e.text.firstChild,i=!1;if(!t||!N(n,t))return xa(ce(ae(e.line),0),!0);if(t==n&&(i=!0,t=n.childNodes[r],r=0,!t)){var o=e.rest?U(e.rest):e.line;return xa(ce(ae(o),o.text.length),i)}var a=3==t.nodeType?t:null,l=t;for(a||1!=t.childNodes.length||3!=t.firstChild.nodeType||(a=t.firstChild,r&&(r=a.nodeValue.length));l.parentNode!=n;)l=l.parentNode;var s=e.measure,u=s.maps;function c(t,r,n){for(var i=-1;i<(u?u.length:0);i++)for(var o=i<0?s.map:u[i],a=0;a<o.length;a+=3){var l=o[a+2];if(l==t||l==r){var c=ae(i<0?e.line:e.rest[i]),d=o[a]+n;return(n<0||l!=t)&&(d=o[a+(n?1:0)]),ce(c,d)}}}var d=c(a,l,r);if(d)return xa(d,i);for(var f=l.nextSibling,h=a?a.nodeValue.length-r:0;f;f=f.nextSibling){if(d=c(f,f.firstChild,0))return xa(ce(d.line,d.ch-h),i);h+=f.textContent.length}for(var p=l.previousSibling,g=r;p;p=p.previousSibling){if(d=c(p,p.firstChild,-1))return xa(ce(d.line,d.ch+g),i);g+=p.textContent.length}}function Sa(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new I,this.inaccurateSelection=!1,this.hasSelection=!1,this.composing=null}ba.prototype=H({init:function(e){var t=this,r=this,n=r.cm,i=r.div=e.lineDiv;function o(e){if(!ot(n,e)){if(n.somethingSelected())ca({lineWise:!1,text:n.getSelections()}),"cut"==e.type&&n.replaceSelection("",null,"cut");else{if(!n.options.lineWiseCopyCut)return;var t=pa(n);ca({lineWise:!0,text:t.text}),"cut"==e.type&&n.operation((function(){n.setSelections(t.ranges,0,B),n.replaceSelection("",null,"cut")}))}if(e.clipboardData){e.clipboardData.clearData();var o=ua.text.join("\n");if(e.clipboardData.setData("Text",o),e.clipboardData.getData("Text")==o)return void e.preventDefault()}var a=ma(),l=a.firstChild;n.display.lineSpace.insertBefore(a,n.display.lineSpace.firstChild),l.value=ua.text.join("\n");var s=document.activeElement;z(l),setTimeout((function(){n.display.lineSpace.removeChild(a),s.focus(),s==i&&r.showPrimarySelection()}),50)}}ga(i,n.options.spellcheck),tt(i,"paste",(function(e){ot(n,e)||fa(e,n)||a<=11&&setTimeout(Gn(n,(function(){r.pollContent()||$n(n)})),20)})),tt(i,"compositionstart",(function(e){t.composing={data:e.data}})),tt(i,"compositionupdate",(function(e){t.composing||(t.composing={data:e.data})})),tt(i,"compositionend",(function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing=null)})),tt(i,"touchstart",(function(){return r.forceCompositionEnd()})),tt(i,"input",(function(){t.composing||t.readFromDOMSoon()})),tt(i,"copy",o),tt(i,"cut",o)},prepareSelection:function(){var e=on(this.cm,!1);return e.focus=this.cm.state.focused,e},showSelection:function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},showPrimarySelection:function(){var e=window.getSelection(),t=this.cm.doc.sel.primary(),n=ka(this.cm,e.anchorNode,e.anchorOffset),i=ka(this.cm,e.focusNode,e.focusOffset);if(!n||n.bad||!i||i.bad||0!=de(pe(n,i),t.from())||0!=de(he(n,i),t.to())){var o=wa(this.cm,t.from()),a=wa(this.cm,t.to());if(o||a){var l,s=this.cm.display.view,u=e.rangeCount&&e.getRangeAt(0);if(o){if(!a){var c=s[s.length-1].measure,d=c.maps?c.maps[c.maps.length-1]:c.map;a={node:d[d.length-1],offset:d[d.length-2]-d[d.length-3]}}}else o={node:s[0].measure.map[2],offset:0};try{l=C(o.node,o.offset,a.offset,a.node)}catch(e){}l&&(!r&&this.cm.state.focused?(e.collapse(o.node,o.offset),l.collapsed||(e.removeAllRanges(),e.addRange(l))):(e.removeAllRanges(),e.addRange(l)),u&&null==e.anchorNode?e.addRange(u):r&&this.startGracePeriod()),this.rememberSelection()}}},startGracePeriod:function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout((function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation((function(){return e.cm.curOp.selectionChanged=!0}))}),20)},showMultipleSelections:function(e){T(this.cm.display.cursorDiv,e.cursors),T(this.cm.display.selectionDiv,e.selection)},rememberSelection:function(){var e=window.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},selectionInEditor:function(){var e=window.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return N(this.div,t)},focus:function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()||this.showSelection(this.prepareSelection(),!0),this.div.focus())},blur:function(){this.div.blur()},getField:function(){return this.div},supportsTouch:function(){return!0},receivedFocus:function(){var e=this;this.selectionInEditor()?this.pollSelection():Kn(this.cm,(function(){return e.cm.curOp.selectionChanged=!0})),this.polling.set(this.cm.options.pollInterval,(function t(){e.cm.state.focused&&(e.pollSelection(),e.polling.set(e.cm.options.pollInterval,t))}))},selectionChanged:function(){var e=window.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},pollSelection:function(){if(!this.composing&&null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=window.getSelection(),t=this.cm;this.rememberSelection();var r=ka(t,e.anchorNode,e.anchorOffset),n=ka(t,e.focusNode,e.focusOffset);r&&n&&Kn(t,(function(){Fi(t.doc,di(r,n),B),(r.bad||n.bad)&&(t.curOp.selectionChanged=!0)}))}},pollContent:function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t,r,n=this.cm,i=n.display,o=n.doc.sel.primary(),a=o.from(),l=o.to();if(0==a.ch&&a.line>n.firstLine()&&(a=ce(a.line-1,re(n.doc,a.line-1).length)),l.ch==re(n.doc,l.line).text.length&&l.line<n.lastLine()&&(l=ce(l.line+1,0)),a.line<i.viewFrom||l.line>i.viewTo-1)return!1;a.line==i.viewFrom||0==(e=rn(n,a.line))?(t=ae(i.view[0].line),r=i.view[0].node):(t=ae(i.view[e].line),r=i.view[e-1].node.nextSibling);var s,u,c=rn(n,l.line);if(c==i.view.length-1?(s=i.viewTo-1,u=i.lineDiv.lastChild):(s=ae(i.view[c+1].line)-1,u=i.view[c+1].node.previousSibling),!r)return!1;for(var d=n.doc.splitLines(function(e,t,r,n,i){var o="",a=!1,l=e.doc.lineSeparator();function s(e){return function(t){return t.id==e}}function u(t){if(1==t.nodeType){var r=t.getAttribute("cm-text");if(null!=r)return void(o+=""==r?t.textContent.replace(/\u200b/g,""):r);var c,d=t.getAttribute("cm-marker");if(d){var f=e.findMarks(ce(n,0),ce(i+1,0),s(+d));return void(f.length&&(c=f[0].find())&&(o+=ne(e.doc,c.from,c.to).join(l)))}if("false"==t.getAttribute("contenteditable"))return;for(var h=0;h<t.childNodes.length;h++)u(t.childNodes[h]);/^(pre|div|p)$/i.test(t.nodeName)&&(a=!0)}else if(3==t.nodeType){var p=t.nodeValue;if(!p)return;a&&(o+=l,a=!1),o+=p}}for(;u(t),t!=r;)t=t.nextSibling;return o}(n,r,u,t,s)),f=ne(n.doc,ce(t,0),ce(s,re(n.doc,s).text.length));d.length>1&&f.length>1;)if(U(d)==U(f))d.pop(),f.pop(),s--;else{if(d[0]!=f[0])break;d.shift(),f.shift(),t++}for(var h=0,p=0,g=d[0],m=f[0],v=Math.min(g.length,m.length);h<v&&g.charCodeAt(h)==m.charCodeAt(h);)++h;for(var y=U(d),b=U(f),w=Math.min(y.length-(1==d.length?h:0),b.length-(1==f.length?h:0));p<w&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)++p;d[d.length-1]=y.slice(0,y.length-p).replace(/^\u200b+/,""),d[0]=d[0].slice(h).replace(/\u200b+$/,"");var x=ce(t,h),k=ce(s,f.length?U(f).length-p:0);return d.length>1||d[0]||de(x,k)?(Ji(n.doc,d,x,k,"+input"),!0):void 0},ensurePolled:function(){this.forceCompositionEnd()},reset:function(){this.forceCompositionEnd()},forceCompositionEnd:function(){this.composing&&(this.composing=null,this.pollContent()||$n(this.cm),this.div.blur(),this.div.focus())},readFromDOMSoon:function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout((function(){e.readDOMTimeout=null,e.composing||!e.cm.isReadOnly()&&e.pollContent()||Kn(e.cm,(function(){return $n(e.cm)}))}),80))},setUneditable:function(e){e.contentEditable="false"},onKeyPress:function(e){e.preventDefault(),this.cm.isReadOnly()||Gn(this.cm,da)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0)},readOnlyChanged:function(e){this.div.contentEditable=String("nocursor"!=e)},onContextMenu:$,resetPosition:$,needsContentAttribute:!0},ba.prototype),Sa.prototype=H({init:function(e){var t=this,r=this,n=this.cm,i=this.wrapper=ma(),l=this.textarea=i.firstChild;function s(e){if(!ot(n,e)){if(n.somethingSelected())ca({lineWise:!1,text:n.getSelections()}),r.inaccurateSelection&&(r.prevInput="",r.inaccurateSelection=!1,l.value=ua.text.join("\n"),z(l));else{if(!n.options.lineWiseCopyCut)return;var t=pa(n);ca({lineWise:!0,text:t.text}),"cut"==e.type?n.setSelections(t.ranges,null,B):(r.prevInput="",l.value=t.text.join("\n"),z(l))}"cut"==e.type&&(n.state.cutIncoming=!0)}}e.wrapper.insertBefore(i,e.wrapper.firstChild),p&&(l.style.width="0px"),tt(l,"input",(function(){o&&a>=9&&t.hasSelection&&(t.hasSelection=null),r.poll()})),tt(l,"paste",(function(e){ot(n,e)||fa(e,n)||(n.state.pasteIncoming=!0,r.fastPoll())})),tt(l,"cut",s),tt(l,"copy",s),tt(e.scroller,"paste",(function(t){wr(e,t)||ot(n,t)||(n.state.pasteIncoming=!0,r.focus())})),tt(e.lineSpace,"selectstart",(function(t){wr(e,t)||ut(t)})),tt(l,"compositionstart",(function(){var e=n.getCursor("from");r.composing&&r.composing.range.clear(),r.composing={start:e,range:n.markText(e,n.getCursor("to"),{className:"CodeMirror-composing"})}})),tt(l,"compositionend",(function(){r.composing&&(r.poll(),r.composing.range.clear(),r.composing=null)}))},prepareSelection:function(){var e=this.cm,t=e.display,r=e.doc,n=on(e);if(e.options.moveInputWithCursor){var i=Gr(e,r.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),a=t.lineDiv.getBoundingClientRect();n.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+a.top-o.top)),n.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+a.left-o.left))}return n},showSelection:function(e){var t=this.cm.display;T(t.cursorDiv,e.cursors),T(t.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},reset:function(e){if(!this.contextMenuPending){var t,r,n=this.cm,i=n.doc;if(n.somethingSelected()){this.prevInput="";var l=i.sel.primary(),s=(t=Ct&&(l.to().line-l.from().line>100||(r=n.getSelection()).length>1e3))?"-":r||n.getSelection();this.textarea.value=s,n.state.focused&&z(this.textarea),o&&a>=9&&(this.hasSelection=s)}else e||(this.prevInput=this.textarea.value="",o&&a>=9&&(this.hasSelection=null));this.inaccurateSelection=t}},getField:function(){return this.textarea},supportsTouch:function(){return!1},focus:function(){if("nocursor"!=this.cm.options.readOnly&&(!g||A()!=this.textarea))try{this.textarea.focus()}catch(e){}},blur:function(){this.textarea.blur()},resetPosition:function(){this.wrapper.style.top=this.wrapper.style.left=0},receivedFocus:function(){this.slowPoll()},slowPoll:function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,(function(){e.poll(),e.cm.state.focused&&e.slowPoll()}))},fastPoll:function(){var e=!1,t=this;t.pollingFast=!0,t.polling.set(20,(function r(){t.poll()||e?(t.pollingFast=!1,t.slowPoll()):(e=!0,t.polling.set(60,r))}))},poll:function(){var e=this,t=this.cm,r=this.textarea,n=this.prevInput;if(this.contextMenuPending||!t.state.focused||kt(r)&&!n&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=r.value;if(i==n&&!t.somethingSelected())return!1;if(o&&a>=9&&this.hasSelection===i||m&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var l=i.charCodeAt(0);if(8203!=l||n||(n="​"),8666==l)return this.reset(),this.cm.execCommand("undo")}for(var s=0,u=Math.min(n.length,i.length);s<u&&n.charCodeAt(s)==i.charCodeAt(s);)++s;return Kn(t,(function(){da(t,i.slice(s),n.length-s,null,e.composing?"*compose":null),i.length>1e3||i.indexOf("\n")>-1?r.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))})),!0},ensurePolled:function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},onKeyPress:function(){o&&a>=9&&(this.hasSelection=null),this.fastPoll()},onContextMenu:function(e){var t=this,r=t.cm,n=r.display,i=t.textarea,s=tn(r,e),u=n.scroller.scrollTop;if(s&&!c){r.options.resetSelectionOnContextMenu&&-1==r.doc.sel.contains(s)&&Gn(r,Fi)(r.doc,di(s),B);var d=i.style.cssText,f=t.wrapper.style.cssText;t.wrapper.style.cssText="position: absolute";var h,p=t.wrapper.getBoundingClientRect();if(i.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-p.top-5)+"px; left: "+(e.clientX-p.left-5)+"px;\n      z-index: 1000; background: "+(o?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",l&&(h=window.scrollY),n.input.focus(),l&&window.scrollTo(null,h),n.input.reset(),r.somethingSelected()||(i.value=t.prevInput=" "),t.contextMenuPending=!0,n.selForContextMenu=r.doc.sel,clearTimeout(n.detectingSelectAll),o&&a>=9&&m(),x){ft(e);var g=function(){nt(window,"mouseup",g),setTimeout(v,20)};tt(window,"mouseup",g)}else setTimeout(v,50)}function m(){if(null!=i.selectionStart){var e=r.somethingSelected(),o="​"+(e?i.value:"");i.value="⇚",i.value=o,t.prevInput=e?"":"​",i.selectionStart=1,i.selectionEnd=o.length,n.selForContextMenu=r.doc.sel}}function v(){if(t.contextMenuPending=!1,t.wrapper.style.cssText=f,i.style.cssText=d,o&&a<9&&n.scrollbars.setScrollTop(n.scroller.scrollTop=u),null!=i.selectionStart){(!o||o&&a<9)&&m();var e=0,l=function(){n.selForContextMenu==r.doc.sel&&0==i.selectionStart&&i.selectionEnd>0&&"​"==t.prevInput?Gn(r,qi)(r):e++<10?n.detectingSelectAll=setTimeout(l,500):n.input.reset()};n.detectingSelectAll=setTimeout(l,200)}}},readOnlyChanged:function(e){e||this.reset()},setUneditable:$,needsContentAttribute:!1},Sa.prototype),function(e){var t=e.optionHandlers;function r(r,n,i,o){e.defaults[r]=n,i&&(t[r]=o?function(e,t,r){r!=ea&&i(e,t,r)}:i)}e.defineOption=r,e.Init=ea,r("value","",(function(e,t){return e.setValue(t)}),!0),r("mode",null,(function(e,t){e.doc.modeOption=t,mi(e)}),!0),r("indentUnit",2,mi,!0),r("indentWithTabs",!1),r("smartIndent",!0),r("tabSize",4,(function(e){vi(e),Fr(e),$n(e)}),!0),r("lineSeparator",null,(function(e,t){if(e.doc.lineSep=t,t){var r=[],n=e.doc.first;e.doc.iter((function(e){for(var i=0;;){var o=e.text.indexOf(t,i);if(-1==o)break;i=o+t.length,r.push(ce(n,o))}n++}));for(var i=r.length-1;i>=0;i--)Ji(e.doc,t,r[i],ce(r[i].line,r[i].ch+t.length))}})),r("specialChars",/[\u0000-\u001f\u007f\u00ad\u200b-\u200f\u2028\u2029\ufeff]/g,(function(e,t,r){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),r!=ea&&e.refresh()})),r("specialCharPlaceholder",Zt,(function(e){return e.refresh()}),!0),r("electricChars",!0),r("inputStyle",g?"contenteditable":"textarea",(function(){throw new Error("inputStyle can not (yet) be changed in a running editor")}),!0),r("spellcheck",!1,(function(e,t){return e.getInputField().spellcheck=t}),!0),r("rtlMoveVisually",!y),r("wholeLineUpdateBefore",!0),r("theme","default",(function(e){Jo(e),na(e)}),!0),r("keyMap","default",(function(e,t,r){var n=Ho(t),i=r!=ea&&Ho(r);i&&i.detach&&i.detach(e,n),n.attach&&n.attach(e,i||null)})),r("extraKeys",null),r("lineWrapping",!1,oa,!0),r("gutters",[],(function(e){li(e.options),na(e)}),!0),r("fixedGutter",!0,(function(e,t){e.display.gutters.style.left=t?Qr(e.display)+"px":"0",e.refresh()}),!0),r("coverGutterNextToScrollbar",!1,(function(e){return Mn(e)}),!0),r("scrollbarStyle","native",(function(e){On(e),Mn(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)}),!0),r("lineNumbers",!1,(function(e){li(e.options),na(e)}),!0),r("firstLineNumber",1,na,!0),r("lineNumberFormatter",(function(e){return e}),na,!0),r("showCursorWhenSelecting",!1,nn,!0),r("resetSelectionOnContextMenu",!0),r("lineWiseCopyCut",!0),r("readOnly",!1,(function(e,t){"nocursor"==t?(dn(e),e.display.input.blur(),e.display.disabled=!0):e.display.disabled=!1,e.display.input.readOnlyChanged(t)})),r("disableInput",!1,(function(e,t){t||e.display.input.reset()}),!0),r("dragDrop",!0,ia),r("allowDropFileTypes",null),r("cursorBlinkRate",530),r("cursorScrollMargin",0),r("cursorHeight",1,nn,!0),r("singleCursorHeightPerLine",!0,nn,!0),r("workTime",100),r("workDelay",100),r("flattenSpans",!0,vi,!0),r("addModeClass",!1,vi,!0),r("pollInterval",100),r("undoDepth",200,(function(e,t){return e.doc.history.undoDepth=t})),r("historyEventDelay",1250),r("viewportMargin",10,(function(e){return e.refresh()}),!0),r("maxHighlightLength",1e4,vi,!0),r("moveInputWithCursor",!0,(function(e,t){t||e.display.input.resetPosition()})),r("tabindex",null,(function(e,t){return e.display.input.getField().tabIndex=t||""})),r("autofocus",null)}(aa),function(e){var t=e.optionHandlers,r=e.helpers={};e.prototype={constructor:e,focus:function(){window.focus(),this.display.input.focus()},setOption:function(e,r){var n=this.options,i=n[e];n[e]==r&&"mode"!=e||(n[e]=r,t.hasOwnProperty(e)&&Gn(this,t[e])(this,r,i))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](Ho(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,r=0;r<t.length;++r)if(t[r]==e||t[r].name==e)return t.splice(r,1),!0},addOverlay:Un((function(t,r){var n=t.token?t:e.getMode(this.options,t);if(n.startState)throw new Error("Overlays may not be stateful.");!function(e,t,r){for(var n=0,i=r(t);n<e.length&&r(e[n])<=i;)n++;e.splice(n,0,t)}(this.state.overlays,{mode:n,modeSpec:t,opaque:r&&r.opaque,priority:r&&r.priority||0},(function(e){return e.priority})),this.state.modeGen++,$n(this)})),removeOverlay:Un((function(e){for(var t=this.state.overlays,r=0;r<t.length;++r){var n=t[r].modeSpec;if(n==e||"string"==typeof e&&n.name==e)return t.splice(r,1),this.state.modeGen++,void $n(this)}})),indentLine:Un((function(e,t,r){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),se(this.doc,e)&&sa(this,e,t,r)})),indentSelection:Un((function(e){for(var t=this,r=this.doc.sel.ranges,n=-1,i=0;i<r.length;i++){var o=r[i];if(o.empty())o.head.line>n&&(sa(t,o.head.line,e,!0),n=o.head.line,i==t.doc.sel.primIndex&&Dn(t));else{var a=o.from(),l=o.to(),s=Math.max(n,a.line);n=Math.min(t.lastLine(),l.line-(l.ch?0:1))+1;for(var u=s;u<n;++u)sa(t,u,e);var c=t.doc.sel.ranges;0==a.ch&&r.length==c.length&&c[i].from().ch>0&&Pi(t.doc,i,new ui(a,c[i].to()),B)}}})),getTokenAt:function(e,t){return Vt(this,e,t)},getLineTokens:function(e,t){return Vt(this,ce(e),t,!0)},getTokenTypeAt:function(e){e=me(this.doc,e);var t,r=Et(this,re(this.doc,e.line)),n=0,i=(r.length-1)/2,o=e.ch;if(0==o)t=r[2];else for(;;){var a=n+i>>1;if((a?r[2*a-1]:0)>=o)i=a;else{if(!(r[2*a+1]<o)){t=r[2*a+2];break}n=a+1}}var l=t?t.indexOf("overlay "):-1;return l<0?t:0==l?null:t.slice(0,l-1)},getModeAt:function(t){var r=this.doc.mode;return r.innerMode?e.innerMode(r,this.getTokenAt(t).state).mode:r},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var n=[];if(!r.hasOwnProperty(t))return n;var i=r[t],o=this.getModeAt(e);if("string"==typeof o[t])i[o[t]]&&n.push(i[o[t]]);else if(o[t])for(var a=0;a<o[t].length;a++){var l=i[o[t][a]];l&&n.push(l)}else o.helperType&&i[o.helperType]?n.push(i[o.helperType]):i[o.name]&&n.push(i[o.name]);for(var s=0;s<i._global.length;s++){var u=i._global[s];u.pred(o,this)&&-1==E(n,u.val)&&n.push(u.val)}return n},getStateAfter:function(e,t){var r=this.doc;return Ft(this,(e=ge(r,null==e?r.first+r.size-1:e))+1,t)},cursorCoords:function(e,t){var r=this.doc.sel.primary();return Gr(this,null==e?r.head:"object"==typeof e?me(this.doc,e):e?r.from():r.to(),t||"page")},charCoords:function(e,t){return Kr(this,me(this.doc,e),t||"page")},coordsChar:function(e,t){return $r(this,(e=Vr(this,e,t||"page")).left,e.top)},lineAtHeight:function(e,t){return e=Vr(this,{top:e,left:0},t||"page").top,le(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,r){var n,i=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:e>o&&(e=o,i=!0),n=re(this.doc,e)}else n=e;return jr(this,n,{top:0,left:0},t||"page",r).top+(i?this.doc.height-Be(n):0)},defaultTextHeight:function(){return Xr(this.display)},defaultCharWidth:function(){return Yr(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,r,n,i){var o,a,l,s,u,c,d=this.display,f=(e=Gr(this,me(this.doc,e))).bottom,h=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),d.sizer.appendChild(t),"over"==n)f=e.top;else if("above"==n||"near"==n){var p=Math.max(d.wrapper.clientHeight,this.doc.height),g=Math.max(d.sizer.clientWidth,d.lineSpace.clientWidth);("above"==n||e.bottom+t.offsetHeight>p)&&e.top>t.offsetHeight?f=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=p&&(f=e.bottom),h+t.offsetWidth>g&&(h=g-t.offsetWidth)}t.style.top=f+"px",t.style.left=t.style.right="","right"==i?(h=d.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?h=0:"middle"==i&&(h=(d.sizer.clientWidth-t.offsetWidth)/2),t.style.left=h+"px"),r&&(o=this,a=h,l=f,s=h+t.offsetWidth,u=f+t.offsetHeight,null!=(c=Wn(o,a,l,s,u)).scrollTop&&vn(o,c.scrollTop),null!=c.scrollLeft&&yn(o,c.scrollLeft))},triggerOnKeyDown:Un(qo),triggerOnKeyPress:Un(_o),triggerOnKeyUp:$o,execCommand:function(e){if(Io.hasOwnProperty(e))return Io[e].call(null,this)},triggerElectric:Un((function(e){ha(this,e)})),findPosH:function(e,t,r,n){var i=1;t<0&&(i=-1,t=-t);for(var o=me(this.doc,e),a=0;a<t&&!(o=va(this.doc,o,i,r,n)).hitSide;++a);return o},moveH:Un((function(e,t){var r=this;this.extendSelectionsBy((function(n){return r.display.shift||r.doc.extend||n.empty()?va(r.doc,n.head,e,t,r.options.rtlMoveVisually):e<0?n.from():n.to()}),j)})),deleteH:Un((function(e,t){var r=this.doc.sel,n=this.doc;r.somethingSelected()?n.replaceSelection("",null,"+delete"):Po(this,(function(r){var i=va(n,r.head,e,t,!1);return e<0?{from:i,to:r.head}:{from:r.head,to:i}}))})),findPosV:function(e,t,r,n){var i=1,o=n;t<0&&(i=-1,t=-t);for(var a=me(this.doc,e),l=0;l<t;++l){var s=Gr(this,a,"div");if(null==o?o=s.left:s.left=o,(a=ya(this,s,i,r)).hitSide)break}return a},moveV:Un((function(e,t){var r=this,n=this.doc,i=[],o=!this.display.shift&&!n.extend&&n.sel.somethingSelected();if(n.extendSelectionsBy((function(a){if(o)return e<0?a.from():a.to();var l=Gr(r,a.head,"div");null!=a.goalColumn&&(l.left=a.goalColumn),i.push(l.left);var s=ya(r,l,e,t);return"page"==t&&a==n.sel.primary()&&zn(r,null,Kr(r,s,"div").top-l.top),s}),j),i.length)for(var a=0;a<n.sel.ranges.length;a++)n.sel.ranges[a].goalColumn=i[a]})),findWordAt:function(e){var t=re(this.doc,e.line).text,r=e.ch,n=e.ch;if(t){var i=this.getHelper(e,"wordChars");(e.xRel<0||n==t.length)&&r?--r:++n;for(var o=t.charAt(r),a=Z(o,i)?function(e){return Z(e,i)}:/\s/.test(o)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!Z(e)};r>0&&a(t.charAt(r-1));)--r;for(;n<t.length&&a(t.charAt(n));)++n}return new ui(ce(e.line,r),ce(e.line,n))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?O(this.display.cursorDiv,"CodeMirror-overwrite"):S(this.display.cursorDiv,"CodeMirror-overwrite"),it(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==A()},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:Un((function(e,t){null==e&&null==t||Hn(this),null!=e&&(this.curOp.scrollLeft=e),null!=t&&(this.curOp.scrollTop=t)})),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-Sr(this)-this.display.barHeight,width:e.scrollWidth-Sr(this)-this.display.barWidth,clientHeight:Tr(this),clientWidth:Lr(this)}},scrollIntoView:Un((function(e,t){if(null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:ce(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line)Hn(this),this.curOp.scrollToPos=e;else{var r=Wn(this,Math.min(e.from.left,e.to.left),Math.min(e.from.top,e.to.top)-e.margin,Math.max(e.from.right,e.to.right),Math.max(e.from.bottom,e.to.bottom)+e.margin);this.scrollTo(r.scrollLeft,r.scrollTop)}})),setSize:Un((function(e,t){var r=this,n=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=n(e)),null!=t&&(this.display.wrapper.style.height=n(t)),this.options.lineWrapping&&Er(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,(function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){_n(r,i,"widget");break}++i})),this.curOp.forceUpdate=!0,it(this,"refresh",this)})),operation:function(e){return Kn(this,e)},refresh:Un((function(){var e=this.display.cachedTextHeight;$n(this),this.curOp.forceUpdate=!0,Fr(this),this.scrollTo(this.doc.scrollLeft,this.doc.scrollTop),ii(this),(null==e||Math.abs(e-Xr(this.display))>.5)&&en(this),it(this,"refresh",this)})),swapDoc:Un((function(e){var t=this.doc;return t.cm=null,xi(this,e),Fr(this),this.display.input.reset(),this.scrollTo(e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,ar(this,"swapDoc",this,t),t})),getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},st(e),e.registerHelper=function(t,n,i){r.hasOwnProperty(t)||(r[t]=e[t]={_global:[]}),r[t][n]=i},e.registerGlobalHelper=function(t,n,i,o){e.registerHelper(t,n,o),r[t]._global.push({pred:i,val:o})}}(aa);var La="iter insert remove copy getEditor constructor".split(" ");for(var Ta in mo.prototype)mo.prototype.hasOwnProperty(Ta)&&E(La,Ta)<0&&(aa.prototype[Ta]=function(e){return function(){return e.apply(this.doc,arguments)}}(mo.prototype[Ta]));return st(mo),aa.inputStyles={textarea:Sa,contenteditable:ba},aa.defineMode=function(e){aa.defaults.mode||"null"==e||(aa.defaults.mode=e),Mt.apply(this,arguments)},aa.defineMIME=function(e,t){Tt[e]=t},aa.defineMode("null",(function(){return{token:function(e){return e.skipToEnd()}}})),aa.defineMIME("text/plain","null"),aa.defineExtension=function(e,t){aa.prototype[e]=t},aa.defineDocExtension=function(e,t){mo.prototype[e]=t},aa.fromTextArea=function(e,t){if((t=t?H(t):{}).value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),null==t.autofocus){var r=A();t.autofocus=r==e||null!=e.getAttribute("autofocus")&&r==document.body}function n(){e.value=l.getValue()}var i;if(e.form&&(tt(e.form,"submit",n),!t.leaveSubmitMethodAlone)){var o=e.form;i=o.submit;try{var a=o.submit=function(){n(),o.submit=i,o.submit(),o.submit=a}}catch(e){}}t.finishInit=function(t){t.save=n,t.getTextArea=function(){return e},t.toTextArea=function(){t.toTextArea=isNaN,n(),e.parentNode.removeChild(t.getWrapperElement()),e.style.display="",e.form&&(nt(e.form,"submit",n),"function"==typeof e.form.submit&&(e.form.submit=i))}},e.style.display="none";var l=aa((function(t){return e.parentNode.insertBefore(t,e.nextSibling)}),t);return l},function(e){e.off=nt,e.on=tt,e.wheelEventPixels=kn,e.Doc=mo,e.splitLines=xt,e.countColumn=P,e.findColumn=V,e.isWordChar=Y,e.Pass=F,e.signal=it,e.Line=Ut,e.changeEnd=fi,e.scrollbarModel=An,e.Pos=ce,e.cmpPos=de,e.modes=Lt,e.mimeModes=Tt,e.resolveMode=Nt,e.getMode=At,e.modeExtensions=Ot,e.extendMode=Wt,e.copyState=zt,e.startState=Ht,e.innerMode=Dt,e.commands=Io,e.keyMap=No,e.keyName=Do,e.isModifierKey=zo,e.lookupKey=Wo,e.normalizeKeyMap=Oo,e.StringStream=Pt,e.SharedTextMarker=fo,e.TextMarker=uo,e.LineWidget=ao,e.e_preventDefault=ut,e.e_stopPropagation=ct,e.e_stop=ft,e.addClass=O,e.contains=N,e.rmClass=S,e.keyNames=So}(aa),aa.version="5.21.0",aa})),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}((function(e){"use strict";function t(e){for(var t={},r=0;r<e.length;++r)t[e[r].toLowerCase()]=!0;return t}e.defineMode("css",(function(t,r){var n=r.inline;r.propertyKeywords||(r=e.resolveMode("text/css"));var i,o,a=t.indentUnit,l=r.tokenHooks,s=r.documentTypes||{},u=r.mediaTypes||{},c=r.mediaFeatures||{},d=r.mediaValueKeywords||{},f=r.propertyKeywords||{},h=r.nonStandardPropertyKeywords||{},p=r.fontProperties||{},g=r.counterDescriptors||{},m=r.colorKeywords||{},v=r.valueKeywords||{},y=r.allowNested,b=!0===r.supportsAtComponent;function w(e,t){return i=t,e}function x(e,t){var r=e.next();if(l[r]){var n=l[r](e,t);if(!1!==n)return n}return"@"==r?(e.eatWhile(/[\w\\\-]/),w("def",e.current())):"="==r||("~"==r||"|"==r)&&e.eat("=")?w(null,"compare"):'"'==r||"'"==r?(t.tokenize=k(r),t.tokenize(e,t)):"#"==r?(e.eatWhile(/[\w\\\-]/),w("atom","hash")):"!"==r?(e.match(/^\s*\w*/),w("keyword","important")):/\d/.test(r)||"."==r&&e.eat(/\d/)?(e.eatWhile(/[\w.%]/),w("number","unit")):"-"!==r?/[,+>*\/]/.test(r)?w(null,"select-op"):"."==r&&e.match(/^-?[_a-z][_a-z0-9-]*/i)?w("qualifier","qualifier"):/[:;{}\[\]\(\)]/.test(r)?w(null,r):"u"==r&&e.match(/rl(-prefix)?\(/)||"d"==r&&e.match("omain(")||"r"==r&&e.match("egexp(")?(e.backUp(1),t.tokenize=C,w("property","word")):/[\w\\\-]/.test(r)?(e.eatWhile(/[\w\\\-]/),w("property","word")):w(null,null):/[\d.]/.test(e.peek())?(e.eatWhile(/[\w.%]/),w("number","unit")):e.match(/^-[\w\\\-]+/)?(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?w("variable-2","variable-definition"):w("variable-2","variable")):e.match(/^\w+-/)?w("meta","meta"):void 0}function k(e){return function(t,r){for(var n,i=!1;null!=(n=t.next());){if(n==e&&!i){")"==e&&t.backUp(1);break}i=!i&&"\\"==n}return(n==e||!i&&")"!=e)&&(r.tokenize=null),w("string","string")}}function C(e,t){return e.next(),e.match(/\s*[\"\')]/,!1)?t.tokenize=null:t.tokenize=k(")"),w(null,"(")}function S(e,t,r){this.type=e,this.indent=t,this.prev=r}function L(e,t,r,n){return e.context=new S(r,t.indentation()+(!1===n?0:a),e.context),r}function T(e){return e.context.prev&&(e.context=e.context.prev),e.context.type}function M(e,t,r){return O[r.context.type](e,t,r)}function N(e,t,r,n){for(var i=n||1;i>0;i--)r.context=r.context.prev;return M(e,t,r)}function A(e){var t=e.current().toLowerCase();o=v.hasOwnProperty(t)?"atom":m.hasOwnProperty(t)?"keyword":"variable"}var O={top:function(e,t,r){if("{"==e)return L(r,t,"block");if("}"==e&&r.context.prev)return T(r);if(b&&/@component/.test(e))return L(r,t,"atComponentBlock");if(/^@(-moz-)?document$/.test(e))return L(r,t,"documentTypes");if(/^@(media|supports|(-moz-)?document|import)$/.test(e))return L(r,t,"atBlock");if(/^@(font-face|counter-style)/.test(e))return r.stateArg=e,"restricted_atBlock_before";if(/^@(-(moz|ms|o|webkit)-)?keyframes$/.test(e))return"keyframes";if(e&&"@"==e.charAt(0))return L(r,t,"at");if("hash"==e)o="builtin";else if("word"==e)o="tag";else{if("variable-definition"==e)return"maybeprop";if("interpolation"==e)return L(r,t,"interpolation");if(":"==e)return"pseudo";if(y&&"("==e)return L(r,t,"parens")}return r.context.type},block:function(e,t,r){if("word"==e){var n=t.current().toLowerCase();return f.hasOwnProperty(n)?(o="property","maybeprop"):h.hasOwnProperty(n)?(o="string-2","maybeprop"):y?(o=t.match(/^\s*:(?:\s|$)/,!1)?"property":"tag","block"):(o+=" error","maybeprop")}return"meta"==e?"block":y||"hash"!=e&&"qualifier"!=e?O.top(e,t,r):(o="error","block")},maybeprop:function(e,t,r){return":"==e?L(r,t,"prop"):M(e,t,r)},prop:function(e,t,r){if(";"==e)return T(r);if("{"==e&&y)return L(r,t,"propBlock");if("}"==e||"{"==e)return N(e,t,r);if("("==e)return L(r,t,"parens");if("hash"!=e||/^#([0-9a-fA-f]{3,4}|[0-9a-fA-f]{6}|[0-9a-fA-f]{8})$/.test(t.current())){if("word"==e)A(t);else if("interpolation"==e)return L(r,t,"interpolation")}else o+=" error";return"prop"},propBlock:function(e,t,r){return"}"==e?T(r):"word"==e?(o="property","maybeprop"):r.context.type},parens:function(e,t,r){return"{"==e||"}"==e?N(e,t,r):")"==e?T(r):"("==e?L(r,t,"parens"):"interpolation"==e?L(r,t,"interpolation"):("word"==e&&A(t),"parens")},pseudo:function(e,t,r){return"word"==e?(o="variable-3",r.context.type):M(e,t,r)},documentTypes:function(e,t,r){return"word"==e&&s.hasOwnProperty(t.current())?(o="tag",r.context.type):O.atBlock(e,t,r)},atBlock:function(e,t,r){if("("==e)return L(r,t,"atBlock_parens");if("}"==e||";"==e)return N(e,t,r);if("{"==e)return T(r)&&L(r,t,y?"block":"top");if("interpolation"==e)return L(r,t,"interpolation");if("word"==e){var n=t.current().toLowerCase();o="only"==n||"not"==n||"and"==n||"or"==n?"keyword":u.hasOwnProperty(n)?"attribute":c.hasOwnProperty(n)?"property":d.hasOwnProperty(n)?"keyword":f.hasOwnProperty(n)?"property":h.hasOwnProperty(n)?"string-2":v.hasOwnProperty(n)?"atom":m.hasOwnProperty(n)?"keyword":"error"}return r.context.type},atComponentBlock:function(e,t,r){return"}"==e?N(e,t,r):"{"==e?T(r)&&L(r,t,y?"block":"top",!1):("word"==e&&(o="error"),r.context.type)},atBlock_parens:function(e,t,r){return")"==e?T(r):"{"==e||"}"==e?N(e,t,r,2):O.atBlock(e,t,r)},restricted_atBlock_before:function(e,t,r){return"{"==e?L(r,t,"restricted_atBlock"):"word"==e&&"@counter-style"==r.stateArg?(o="variable","restricted_atBlock_before"):M(e,t,r)},restricted_atBlock:function(e,t,r){return"}"==e?(r.stateArg=null,T(r)):"word"==e?(o="@font-face"==r.stateArg&&!p.hasOwnProperty(t.current().toLowerCase())||"@counter-style"==r.stateArg&&!g.hasOwnProperty(t.current().toLowerCase())?"error":"property","maybeprop"):"restricted_atBlock"},keyframes:function(e,t,r){return"word"==e?(o="variable","keyframes"):"{"==e?L(r,t,"top"):M(e,t,r)},at:function(e,t,r){return";"==e?T(r):"{"==e||"}"==e?N(e,t,r):("word"==e?o="tag":"hash"==e&&(o="builtin"),"at")},interpolation:function(e,t,r){return"}"==e?T(r):"{"==e||";"==e?N(e,t,r):("word"==e?o="variable":"variable"!=e&&"("!=e&&")"!=e&&(o="error"),"interpolation")}};return{startState:function(e){return{tokenize:null,state:n?"block":"top",stateArg:null,context:new S(n?"block":"top",e||0,null)}},token:function(e,t){if(!t.tokenize&&e.eatSpace())return null;var r=(t.tokenize||x)(e,t);return r&&"object"==typeof r&&(i=r[1],r=r[0]),o=r,t.state=O[t.state](i,e,t),o},indent:function(e,t){var r=e.context,n=t&&t.charAt(0),i=r.indent;return"prop"!=r.type||"}"!=n&&")"!=n||(r=r.prev),r.prev&&("}"!=n||"block"!=r.type&&"top"!=r.type&&"interpolation"!=r.type&&"restricted_atBlock"!=r.type?(")"!=n||"parens"!=r.type&&"atBlock_parens"!=r.type)&&("{"!=n||"at"!=r.type&&"atBlock"!=r.type)||(i=Math.max(0,r.indent-a),r=r.prev):i=(r=r.prev).indent),i},electricChars:"}",blockCommentStart:"/*",blockCommentEnd:"*/",fold:"brace"}}));var r=["domain","regexp","url","url-prefix"],n=t(r),i=["all","aural","braille","handheld","print","projection","screen","tty","tv","embossed"],o=t(i),a=["width","min-width","max-width","height","min-height","max-height","device-width","min-device-width","max-device-width","device-height","min-device-height","max-device-height","aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio","color","min-color","max-color","color-index","min-color-index","max-color-index","monochrome","min-monochrome","max-monochrome","resolution","min-resolution","max-resolution","scan","grid","orientation","device-pixel-ratio","min-device-pixel-ratio","max-device-pixel-ratio","pointer","any-pointer","hover","any-hover"],l=t(a),s=["landscape","portrait","none","coarse","fine","on-demand","hover","interlace","progressive"],u=t(s),c=["align-content","align-items","align-self","alignment-adjust","alignment-baseline","anchor-point","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","appearance","azimuth","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-repeat","background-size","baseline-shift","binding","bleed","bookmark-label","bookmark-level","bookmark-state","bookmark-target","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","clear","clip","color","color-profile","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","content","counter-increment","counter-reset","crop","cue","cue-after","cue-before","cursor","direction","display","dominant-baseline","drop-initial-after-adjust","drop-initial-after-align","drop-initial-before-adjust","drop-initial-before-align","drop-initial-size","drop-initial-value","elevation","empty-cells","fit","fit-position","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-offset","flow-from","flow-into","font","font-feature-settings","font-family","font-kerning","font-language-override","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-weight","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-gap","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-gap","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphens","icon","image-orientation","image-rendering","image-resolution","inline-box-align","justify-content","left","letter-spacing","line-break","line-height","line-stacking","line-stacking-ruby","line-stacking-shift","line-stacking-strategy","list-style","list-style-image","list-style-position","list-style-type","margin","margin-bottom","margin-left","margin-right","margin-top","marks","marquee-direction","marquee-loop","marquee-play-count","marquee-speed","marquee-style","max-height","max-width","min-height","min-width","move-to","nav-down","nav-index","nav-left","nav-right","nav-up","object-fit","object-position","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-style","overflow-wrap","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-policy","pause","pause-after","pause-before","perspective","perspective-origin","pitch","pitch-range","play-during","position","presentation-level","punctuation-trim","quotes","region-break-after","region-break-before","region-break-inside","region-fragment","rendering-intent","resize","rest","rest-after","rest-before","richness","right","rotation","rotation-point","ruby-align","ruby-overhang","ruby-position","ruby-span","shape-image-threshold","shape-inside","shape-margin","shape-outside","size","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","stress","string-set","tab-size","table-layout","target","target-name","target-new","target-position","text-align","text-align-last","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-style","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-height","text-indent","text-justify","text-outline","text-overflow","text-shadow","text-size-adjust","text-space-collapse","text-transform","text-underline-position","text-wrap","top","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","unicode-bidi","user-select","vertical-align","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","widows","width","word-break","word-spacing","word-wrap","z-index","clip-path","clip-rule","mask","enable-background","filter","flood-color","flood-opacity","lighting-color","stop-color","stop-opacity","pointer-events","color-interpolation","color-interpolation-filters","color-rendering","fill","fill-opacity","fill-rule","image-rendering","marker","marker-end","marker-mid","marker-start","shape-rendering","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-rendering","baseline-shift","dominant-baseline","glyph-orientation-horizontal","glyph-orientation-vertical","text-anchor","writing-mode"],d=t(c),f=["scrollbar-arrow-color","scrollbar-base-color","scrollbar-dark-shadow-color","scrollbar-face-color","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-3d-light-color","scrollbar-track-color","shape-inside","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","zoom"],h=t(f),p=t(["font-family","src","unicode-range","font-variant","font-feature-settings","font-stretch","font-weight","font-style"]),g=t(["additive-symbols","fallback","negative","pad","prefix","range","speak-as","suffix","symbols","system"]),m=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],v=t(m),y=["above","absolute","activeborder","additive","activecaption","afar","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","amharic","amharic-abegede","antialiased","appworkspace","arabic-indic","armenian","asterisks","attr","auto","avoid","avoid-column","avoid-page","avoid-region","background","backwards","baseline","below","bidi-override","binary","bengali","blink","block","block-axis","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","bullets","button","button-bevel","buttonface","buttonhighlight","buttonshadow","buttontext","calc","cambodian","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","cjk-earthly-branch","cjk-heavenly-stem","cjk-ideographic","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","contain","content","content-box","context-menu","continuous","copy","counter","counters","cover","crop","cross","crosshair","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","devanagari","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic","ethiopic-abegede","ethiopic-abegede-am-et","ethiopic-abegede-gez","ethiopic-abegede-ti-er","ethiopic-abegede-ti-et","ethiopic-halehame-aa-er","ethiopic-halehame-aa-et","ethiopic-halehame-am-et","ethiopic-halehame-gez","ethiopic-halehame-om-et","ethiopic-halehame-sid-et","ethiopic-halehame-so-et","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","ethiopic-halehame-tig","ethiopic-numeric","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","georgian","graytext","grid","groove","gujarati","gurmukhi","hand","hangul","hangul-consonant","hard-light","hebrew","help","hidden","hide","higher","highlight","highlighttext","hiragana","hiragana-iroha","horizontal","hsl","hsla","hue","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","japanese-formal","japanese-informal","justify","kannada","katakana","katakana-iroha","keep-all","khmer","korean-hangul-formal","korean-hanja-formal","korean-hanja-informal","landscape","lao","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-alpha","lower-armenian","lower-greek","lower-hexadecimal","lower-latin","lower-norwegian","lower-roman","lowercase","ltr","luminosity","malayalam","match","matrix","matrix3d","media-controls-background","media-current-time-display","media-fullscreen-button","media-mute-button","media-play-button","media-return-to-realtime-button","media-rewind-button","media-seek-back-button","media-seek-forward-button","media-slider","media-sliderthumb","media-time-remaining-display","media-volume-slider","media-volume-slider-container","media-volume-sliderthumb","medium","menu","menulist","menulist-button","menulist-text","menulist-textfield","menutext","message-box","middle","min-intrinsic","mix","mongolian","monospace","move","multiple","multiply","myanmar","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","octal","open-quote","optimizeLegibility","optimizeSpeed","oriya","oromo","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","persian","perspective","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","se-resize","searchfield","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","semi-condensed","semi-expanded","separate","serif","show","sidama","simp-chinese-formal","simp-chinese-informal","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","somali","source-atop","source-in","source-out","source-over","space","space-around","space-between","spell-out","square","square-button","start","static","status-bar","stretch","stroke","sub","subpixel-antialiased","super","sw-resize","symbolic","symbols","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","tamil","telugu","text","text-bottom","text-top","textarea","textfield","thai","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","tibetan","tigre","tigrinya-er","tigrinya-er-abegede","tigrinya-et","tigrinya-et-abegede","to","top","trad-chinese-formal","trad-chinese-informal","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","up","upper-alpha","upper-armenian","upper-greek","upper-hexadecimal","upper-latin","upper-norwegian","upper-roman","uppercase","urdu","url","var","vertical","vertical-text","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"],b=t(y),w=r.concat(i).concat(a).concat(s).concat(c).concat(f).concat(m).concat(y);function x(e,t){for(var r,n=!1;null!=(r=e.next());){if(n&&"../index.html"==r){t.tokenize=null;break}n="*"==r}return["comment","comment"]}e.registerHelper("hintWords","css",w),e.defineMIME("text/css",{documentTypes:n,mediaTypes:o,mediaFeatures:l,mediaValueKeywords:u,propertyKeywords:d,nonStandardPropertyKeywords:h,fontProperties:p,counterDescriptors:g,colorKeywords:v,valueKeywords:b,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=x,x(e,t))}},name:"css"}),e.defineMIME("text/x-scss",{mediaTypes:o,mediaFeatures:l,mediaValueKeywords:u,propertyKeywords:d,nonStandardPropertyKeywords:h,colorKeywords:v,valueKeywords:b,fontProperties:p,allowNested:!0,tokenHooks:{"/":function(e,t){return e.eat("../index.html")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=x,x(e,t)):["operator","operator"]},":":function(e){return!!e.match(/\s*\{/)&&[null,"{"]},$:function(e){return e.match(/^[\w-]+/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"]},"#":function(e){return!!e.eat("{")&&[null,"interpolation"]}},name:"css",helperType:"scss"}),e.defineMIME("text/x-less",{mediaTypes:o,mediaFeatures:l,mediaValueKeywords:u,propertyKeywords:d,nonStandardPropertyKeywords:h,colorKeywords:v,valueKeywords:b,fontProperties:p,allowNested:!0,tokenHooks:{"/":function(e,t){return e.eat("../index.html")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=x,x(e,t)):["operator","operator"]},"@":function(e){return e.eat("{")?[null,"interpolation"]:!e.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\b/,!1)&&(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"])},"&":function(){return["atom","atom"]}},name:"css",helperType:"less"}),e.defineMIME("text/x-gss",{documentTypes:n,mediaTypes:o,mediaFeatures:l,propertyKeywords:d,nonStandardPropertyKeywords:h,fontProperties:p,counterDescriptors:g,colorKeywords:v,valueKeywords:b,supportsAtComponent:!0,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=x,x(e,t))}},name:"css",helperType:"gss"})})),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}((function(e){"use strict";var t={autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0,caseFold:!0},r={autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1,caseFold:!1};e.defineMode("xml",(function(n,i){var o,a,l=n.indentUnit,s={},u=i.htmlMode?t:r;for(var c in u)s[c]=u[c];for(var c in i)s[c]=i[c];function d(e,t){function r(r){return t.tokenize=r,r(e,t)}var n=e.next();return"<"==n?e.eat("!")?e.eat("[")?e.match("CDATA[")?r(h("atom","]]>")):null:e.match("--")?r(h("comment","--\x3e")):e.match("DOCTYPE",!0,!0)?(e.eatWhile(/[\w\._\-]/),r(p(1))):null:e.eat("?")?(e.eatWhile(/[\w\._\-]/),t.tokenize=h("meta","?>"),"meta"):(o=e.eat("../index.html")?"closeTag":"openTag",t.tokenize=f,"tag bracket"):"&"==n?(e.eat("#")?e.eat("x")?e.eatWhile(/[a-fA-F\d]/)&&e.eat(";"):e.eatWhile(/[\d]/)&&e.eat(";"):e.eatWhile(/[\w\.\-:]/)&&e.eat(";"))?"atom":"error":(e.eatWhile(/[^&<]/),null)}function f(e,t){var r,n,i=e.next();if(">"==i||"/"==i&&e.eat(">"))return t.tokenize=d,o=">"==i?"endTag":"selfcloseTag","tag bracket";if("="==i)return o="equals",null;if("<"==i){t.tokenize=d,t.state=y,t.tagName=t.tagStart=null;var a=t.tokenize(e,t);return a?a+" tag error":"tag error"}return/[\'\"]/.test(i)?(t.tokenize=(r=i,(n=function(e,t){for(;!e.eol();)if(e.next()==r){t.tokenize=f;break}return"string"}).isInAttribute=!0,n),t.stringStartCol=e.column(),t.tokenize(e,t)):(e.match(/^[^\s\u00a0=<>\"\']*[^\s\u00a0=<>\"\'\/]/),"word")}function h(e,t){return function(r,n){for(;!r.eol();){if(r.match(t)){n.tokenize=d;break}r.next()}return e}}function p(e){return function(t,r){for(var n;null!=(n=t.next());){if("<"==n)return r.tokenize=p(e+1),r.tokenize(t,r);if(">"==n){if(1==e){r.tokenize=d;break}return r.tokenize=p(e-1),r.tokenize(t,r)}}return"meta"}}function g(e,t,r){this.prev=e.context,this.tagName=t,this.indent=e.indented,this.startOfLine=r,(s.doNotIndent.hasOwnProperty(t)||e.context&&e.context.noIndent)&&(this.noIndent=!0)}function m(e){e.context&&(e.context=e.context.prev)}function v(e,t){for(var r;;){if(!e.context)return;if(r=e.context.tagName,!s.contextGrabbers.hasOwnProperty(r)||!s.contextGrabbers[r].hasOwnProperty(t))return;m(e)}}function y(e,t,r){return"openTag"==e?(r.tagStart=t.column(),b):"closeTag"==e?w:y}function b(e,t,r){return"word"==e?(r.tagName=t.current(),a="tag",C):(a="error",b)}function w(e,t,r){if("word"==e){var n=t.current();return r.context&&r.context.tagName!=n&&s.implicitlyClosed.hasOwnProperty(r.context.tagName)&&m(r),r.context&&r.context.tagName==n||!1===s.matchClosing?(a="tag",x):(a="tag error",k)}return a="error",k}function x(e,t,r){return"endTag"!=e?(a="error",x):(m(r),y)}function k(e,t,r){return a="error",x(e,0,r)}function C(e,t,r){if("word"==e)return a="attribute",S;if("endTag"==e||"selfcloseTag"==e){var n=r.tagName,i=r.tagStart;return r.tagName=r.tagStart=null,"selfcloseTag"==e||s.autoSelfClosers.hasOwnProperty(n)?v(r,n):(v(r,n),r.context=new g(r,n,i==r.indented)),y}return a="error",C}function S(e,t,r){return"equals"==e?L:(s.allowMissing||(a="error"),C(e,0,r))}function L(e,t,r){return"string"==e?T:"word"==e&&s.allowUnquoted?(a="string",C):(a="error",C(e,0,r))}function T(e,t,r){return"string"==e?T:C(e,0,r)}return d.isInText=!0,{startState:function(e){var t={tokenize:d,state:y,indented:e||0,tagName:null,tagStart:null,context:null};return null!=e&&(t.baseIndent=e),t},token:function(e,t){if(!t.tagName&&e.sol()&&(t.indented=e.indentation()),e.eatSpace())return null;o=null;var r=t.tokenize(e,t);return(r||o)&&"comment"!=r&&(a=null,t.state=t.state(o||r,e,t),a&&(r="error"==a?r+" error":a)),r},indent:function(t,r,n){var i=t.context;if(t.tokenize.isInAttribute)return t.tagStart==t.indented?t.stringStartCol+1:t.indented+l;if(i&&i.noIndent)return e.Pass;if(t.tokenize!=f&&t.tokenize!=d)return n?n.match(/^(\s*)/)[0].length:0;if(t.tagName)return!1!==s.multilineTagIndentPastTag?t.tagStart+t.tagName.length+2:t.tagStart+l*(s.multilineTagIndentFactor||1);if(s.alignCDATA&&/<!\[CDATA\[/.test(r))return 0;var o=r&&/^<(\/)?([\w_:\.-]*)/.exec(r);if(o&&o[1])for(;i;){if(i.tagName==o[2]){i=i.prev;break}if(!s.implicitlyClosed.hasOwnProperty(i.tagName))break;i=i.prev}else if(o)for(;i;){var a=s.contextGrabbers[i.tagName];if(!a||!a.hasOwnProperty(o[2]))break;i=i.prev}for(;i&&i.prev&&!i.startOfLine;)i=i.prev;return i?i.indent+l:t.baseIndent||0},electricInput:/<\/[\s\w:]+>$/,blockCommentStart:"\x3c!--",blockCommentEnd:"--\x3e",configuration:s.htmlMode?"html":"xml",helperType:s.htmlMode?"html":"xml",skipAttribute:function(e){e.state==L&&(e.state=C)}}})),e.defineMIME("text/xml","xml"),e.defineMIME("application/xml","xml"),e.mimeModes.hasOwnProperty("text/html")||e.defineMIME("text/html",{name:"xml",htmlMode:!0})})),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}((function(e){"use strict";function t(e,t,r){return/^(?:operator|sof|keyword c|case|new|[\[{}\(,;:]|=>)$/.test(t.lastType)||"quasi"==t.lastType&&/\{\s*$/.test(e.string.slice(0,e.pos-(r||0)))}e.defineMode("javascript",(function(r,n){var i,o,a=r.indentUnit,l=n.statementIndent,s=n.jsonld,u=n.json||s,c=n.typescript,d=n.wordCharacters||/[\w$\xa1-\uffff]/,f=function(){function e(e){return{type:e,style:"keyword"}}var t=e("keyword a"),r=e("keyword b"),n=e("keyword c"),i=e("operator"),o={type:"atom",style:"atom"},a={if:e("if"),while:t,with:t,else:r,do:r,try:r,finally:r,return:n,break:n,continue:n,new:e("new"),delete:n,throw:n,debugger:n,var:e("var"),const:e("var"),let:e("var"),function:e("function"),catch:e("catch"),for:e("for"),switch:e("switch"),case:e("case"),default:e("default"),in:i,typeof:i,instanceof:i,true:o,false:o,null:o,undefined:o,NaN:o,Infinity:o,this:e("this"),class:e("class"),super:e("atom"),yield:n,export:e("export"),import:e("import"),extends:n,await:n,async:e("async")};if(c){var l={type:"variable",style:"variable-3"},s={interface:e("class"),implements:n,namespace:n,module:e("module"),enum:e("module"),type:e("type"),public:e("modifier"),private:e("modifier"),protected:e("modifier"),abstract:e("modifier"),as:i,string:l,number:l,boolean:l,any:l};for(var u in s)a[u]=s[u]}return a}(),h=/[+\-*&%=<>!?|~^]/,p=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function g(e,t,r){return i=e,o=r,t}function m(e,r){var n,i=e.next();if('"'==i||"'"==i)return r.tokenize=(n=i,function(e,t){var r,i=!1;if(s&&"@"==e.peek()&&e.match(p))return t.tokenize=m,g("jsonld-keyword","meta");for(;null!=(r=e.next())&&(r!=n||i);)i=!i&&"\\"==r;return i||(t.tokenize=m),g("string","string")}),r.tokenize(e,r);if("."==i&&e.match(/^\d+(?:[eE][+\-]?\d+)?/))return g("number","number");if("."==i&&e.match(".."))return g("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(i))return g(i);if("="==i&&e.eat(">"))return g("=>","operator");if("0"==i&&e.eat(/x/i))return e.eatWhile(/[\da-f]/i),g("number","number");if("0"==i&&e.eat(/o/i))return e.eatWhile(/[0-7]/i),g("number","number");if("0"==i&&e.eat(/b/i))return e.eatWhile(/[01]/i),g("number","number");if(/\d/.test(i))return e.match(/^\d*(?:\.\d*)?(?:[eE][+\-]?\d+)?/),g("number","number");if("../index.html"==i)return e.eat("*")?(r.tokenize=v,v(e,r)):e.eat("../index.html")?(e.skipToEnd(),g("comment","comment")):t(e,r,1)?(function(e){for(var t,r=!1,n=!1;null!=(t=e.next());){if(!r){if("/"==t&&!n)return;"["==t?n=!0:n&&"]"==t&&(n=!1)}r=!r&&"\\"==t}}(e),e.match(/^\b(([gimyu])(?![gimyu]*\2))+\b/),g("regexp","string-2")):(e.eatWhile(h),g("operator","operator",e.current()));if("`"==i)return r.tokenize=y,y(e,r);if("#"==i)return e.skipToEnd(),g("error","error");if(h.test(i))return e.eatWhile(h),g("operator","operator",e.current());if(d.test(i)){e.eatWhile(d);var o=e.current(),a=f.propertyIsEnumerable(o)&&f[o];return a&&"."!=r.lastType?g(a.type,a.style,o):g("variable","variable",o)}}function v(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=m;break}n="*"==r}return g("comment","comment")}function y(e,t){for(var r,n=!1;null!=(r=e.next());){if(!n&&("`"==r||"$"==r&&e.eat("{"))){t.tokenize=m;break}n=!n&&"\\"==r}return g("quasi","string-2",e.current())}function b(e,t){t.fatArrowAt&&(t.fatArrowAt=null);var r=e.string.indexOf("=>",e.start);if(!(r<0)){if(c){var n=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(e.string.slice(e.start,r));n&&(r=n.index)}for(var i=0,o=!1,a=r-1;a>=0;--a){var l=e.string.charAt(a),s="([{}])".indexOf(l);if(s>=0&&s<3){if(!i){++a;break}if(0==--i){"("==l&&(o=!0);break}}else if(s>=3&&s<6)++i;else if(d.test(l))o=!0;else{if(/["'\/]/.test(l))return;if(o&&!i){++a;break}}}o&&!i&&(t.fatArrowAt=a)}}var w={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,"jsonld-keyword":!0};function x(e,t,r,n,i,o){this.indented=e,this.column=t,this.type=r,this.prev=i,this.info=o,null!=n&&(this.align=n)}function k(e,t){for(var r=e.localVars;r;r=r.next)if(r.name==t)return!0;for(var n=e.context;n;n=n.prev)for(r=n.vars;r;r=r.next)if(r.name==t)return!0}var C={state:null,column:null,marked:null,cc:null};function S(){for(var e=arguments.length-1;e>=0;e--)C.cc.push(arguments[e])}function L(){return S.apply(null,arguments),!0}function T(e){function t(t){for(var r=t;r;r=r.next)if(r.name==e)return!0;return!1}var r=C.state;if(C.marked="def",r.context){if(t(r.localVars))return;r.localVars={name:e,next:r.localVars}}else{if(t(r.globalVars))return;n.globalVars&&(r.globalVars={name:e,next:r.globalVars})}}var M={name:"this",next:{name:"arguments"}};function N(){C.state.context={prev:C.state.context,vars:C.state.localVars},C.state.localVars=M}function A(){C.state.localVars=C.state.context.vars,C.state.context=C.state.context.prev}function O(e,t){var r=function(){var r=C.state,n=r.indented;if("stat"==r.lexical.type)n=r.lexical.indented;else for(var i=r.lexical;i&&")"==i.type&&i.align;i=i.prev)n=i.indented;r.lexical=new x(n,C.stream.column(),e,null,r.lexical,t)};return r.lex=!0,r}function W(){var e=C.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function z(e){return function t(r){return r==e?L():";"==e?S():L(t)}}function D(e,t){return"var"==e?L(O("vardef",t.length),se,z(";"),W):"keyword a"==e?L(O("form"),I,D,W):"keyword b"==e?L(O("form"),D,W):"{"==e?L(O("}"),te,W):";"==e?L():"if"==e?("else"==C.state.lexical.info&&C.state.cc[C.state.cc.length-1]==W&&C.state.cc.pop()(),L(O("form"),I,D,W,he)):"function"==e?L(be):"for"==e?L(O("form"),pe,D,W):"variable"==e?L(O("stat"),_):"switch"==e?L(O("form"),I,O("}","switch"),z("{"),te,W,W):"case"==e?L(H,z(":")):"default"==e?L(z(":")):"catch"==e?L(O("form"),N,z("("),we,z(")"),D,W,A):"class"==e?L(O("form"),ke,W):"export"==e?L(O("stat"),Te,W):"import"==e?L(O("stat"),Me,W):"module"==e?L(O("form"),ue,O("}"),z("{"),te,W,W):"type"==e?L(ne,z("operator"),ne,z(";")):"async"==e?L(D):S(O("stat"),H,z(";"),W)}function H(e){return E(e,!1)}function P(e){return E(e,!0)}function I(e){return"("!=e?S():L(O(")"),H,z(")"),W)}function E(e,t){if(C.state.fatArrowAt==C.stream.start){var r=t?U:G;if("("==e)return L(N,O(")"),J(ue,")"),W,z("=>"),r,A);if("variable"==e)return S(N,ue,z("=>"),r,A)}var n=t?j:R;return w.hasOwnProperty(e)?L(n):"function"==e?L(be,n):"class"==e?L(O("form"),xe,W):"keyword c"==e||"async"==e?L(t?B:F):"("==e?L(O(")"),F,z(")"),W,n):"operator"==e||"spread"==e?L(t?P:H):"["==e?L(O("]"),We,W,n):"{"==e?ee(Y,"}",null,n):"quasi"==e?S(V,n):"new"==e?L(function(e){return function(t){return"."==t?L(e?$:q):S(e?P:H)}}(t)):L()}function F(e){return e.match(/[;\}\)\],]/)?S():S(H)}function B(e){return e.match(/[;\}\)\],]/)?S():S(P)}function R(e,t){return","==e?L(H):j(e,t,!1)}function j(e,t,r){var n=0==r?R:j,i=0==r?H:P;return"=>"==e?L(N,r?U:G,A):"operator"==e?/\+\+|--/.test(t)?L(n):"?"==t?L(H,z(":"),i):L(i):"quasi"==e?S(V,n):";"!=e?"("==e?ee(P,")","call",n):"."==e?L(X,n):"["==e?L(O("]"),F,z("]"),W,n):void 0:void 0}function V(e,t){return"quasi"!=e?S():"${"!=t.slice(t.length-2)?L(V):L(H,K)}function K(e){if("}"==e)return C.marked="string-2",C.state.tokenize=y,L(V)}function G(e){return b(C.stream,C.state),S("{"==e?D:H)}function U(e){return b(C.stream,C.state),S("{"==e?D:P)}function q(e,t){if("target"==t)return C.marked="keyword",L(R)}function $(e,t){if("target"==t)return C.marked="keyword",L(j)}function _(e){return":"==e?L(W,D):S(R,z(";"),W)}function X(e){if("variable"==e)return C.marked="property",L()}function Y(e,t){return"async"==e?(C.marked="property",L(Y)):"variable"==e||"keyword"==C.style?(C.marked="property",L("get"==t||"set"==t?Z:Q)):"number"==e||"string"==e?(C.marked=s?"property":C.style+" property",L(Q)):"jsonld-keyword"==e?L(Q):"modifier"==e?L(Y):"["==e?L(H,z("]"),Q):"spread"==e?L(H):":"==e?S(Q):void 0}function Z(e){return"variable"!=e?S(Q):(C.marked="property",L(be))}function Q(e){return":"==e?L(P):"("==e?S(be):void 0}function J(e,t){function r(n,i){if(","==n){var o=C.state.lexical;return"call"==o.info&&(o.pos=(o.pos||0)+1),L((function(r,n){return r==t||n==t?S():S(e)}),r)}return n==t||i==t?L():L(z(t))}return function(n,i){return n==t||i==t?L():S(e,r)}}function ee(e,t,r){for(var n=3;n<arguments.length;n++)C.cc.push(arguments[n]);return L(O(t,r),J(e,t),W)}function te(e){return"}"==e?L():S(D,te)}function re(e,t){if(c){if(":"==e)return L(ne);if("?"==t)return L(re)}}function ne(e){return"variable"==e?(C.marked="variable-3",L(le)):"{"==e?L(J(oe,"}")):"("==e?L(J(ae,")"),ie):void 0}function ie(e){if("=>"==e)return L(ne)}function oe(e){return"variable"==e||"keyword"==C.style?(C.marked="property",L(oe)):":"==e?L(ne):void 0}function ae(e){return"variable"==e?L(ae):":"==e?L(ne):void 0}function le(e,t){return"<"==t?L(J(ne,">"),le):"["==e?L(z("]"),le):void 0}function se(){return S(ue,re,de,fe)}function ue(e,t){return"modifier"==e?L(ue):"variable"==e?(T(t),L()):"spread"==e?L(ue):"["==e?ee(ue,"]"):"{"==e?ee(ce,"}"):void 0}function ce(e,t){return"variable"!=e||C.stream.match(/^\s*:/,!1)?("variable"==e&&(C.marked="property"),"spread"==e?L(ue):"}"==e?S():L(z(":"),ue,de)):(T(t),L(de))}function de(e,t){if("="==t)return L(P)}function fe(e){if(","==e)return L(se)}function he(e,t){if("keyword b"==e&&"else"==t)return L(O("form","else"),D,W)}function pe(e){if("("==e)return L(O(")"),ge,z(")"),W)}function ge(e){return"var"==e?L(se,z(";"),ve):";"==e?L(ve):"variable"==e?L(me):S(H,z(";"),ve)}function me(e,t){return"in"==t||"of"==t?(C.marked="keyword",L(H)):L(R,ve)}function ve(e,t){return";"==e?L(ye):"in"==t||"of"==t?(C.marked="keyword",L(H)):S(H,z(";"),ye)}function ye(e){")"!=e&&L(H)}function be(e,t){return"*"==t?(C.marked="keyword",L(be)):"variable"==e?(T(t),L(be)):"("==e?L(N,O(")"),J(we,")"),W,re,D,A):void 0}function we(e){return"spread"==e?L(we):S(ue,re,de)}function xe(e,t){return"variable"==e?ke(e,t):Ce(e,t)}function ke(e,t){if("variable"==e)return T(t),L(Ce)}function Ce(e,t){return"extends"==t||"implements"==t?L(c?ne:H,Ce):"{"==e?L(O("}"),Se,W):void 0}function Se(e,t){return"variable"==e||"keyword"==C.style?("static"==t||"get"==t||"set"==t||c&&("public"==t||"private"==t||"protected"==t||"readonly"==t||"abstract"==t))&&C.stream.match(/^\s+[\w$\xa1-\uffff]/,!1)?(C.marked="keyword",L(Se)):(C.marked="property",L(c?Le:be,Se)):"*"==t?(C.marked="keyword",L(Se)):";"==e?L(Se):"}"==e?L():void 0}function Le(e,t){return"?"==t?L(Le):":"==e?L(ne,de):S(be)}function Te(e,t){return"*"==t?(C.marked="keyword",L(Oe,z(";"))):"default"==t?(C.marked="keyword",L(H,z(";"))):S(D)}function Me(e){return"string"==e?L():S(Ne,Oe)}function Ne(e,t){return"{"==e?ee(Ne,"}"):("variable"==e&&T(t),"*"==t&&(C.marked="keyword"),L(Ae))}function Ae(e,t){if("as"==t)return C.marked="keyword",L(Ne)}function Oe(e,t){if("from"==t)return C.marked="keyword",L(H)}function We(e){return"]"==e?L():S(J(P,"]"))}return W.lex=!0,{startState:function(e){var t={tokenize:m,lastType:"sof",cc:[],lexical:new x((e||0)-a,0,"block",!1),localVars:n.localVars,context:n.localVars&&{vars:n.localVars},indented:e||0};return n.globalVars&&"object"==typeof n.globalVars&&(t.globalVars=n.globalVars),t},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation(),b(e,t)),t.tokenize!=v&&e.eatSpace())return null;var r=t.tokenize(e,t);return"comment"==i?r:(t.lastType="operator"!=i||"++"!=o&&"--"!=o?i:"incdec",function(e,t,r,n,i){var o=e.cc;for(C.state=e,C.stream=i,C.marked=null,C.cc=o,C.style=t,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);;)if((o.length?o.pop():u?H:D)(r,n)){for(;o.length&&o[o.length-1].lex;)o.pop()();return C.marked?C.marked:"variable"==r&&k(e,n)?"variable-2":t}}(t,r,i,o,e))},indent:function(t,r){if(t.tokenize==v)return e.Pass;if(t.tokenize!=m)return 0;var i,o=r&&r.charAt(0),s=t.lexical;if(!/^\s*else\b/.test(r))for(var u=t.cc.length-1;u>=0;--u){var c=t.cc[u];if(c==W)s=s.prev;else if(c!=he)break}for(;("stat"==s.type||"form"==s.type)&&("}"==o||(i=t.cc[t.cc.length-1])&&(i==R||i==j)&&!/^[,\.=+\-*:?[\(]/.test(r));)s=s.prev;l&&")"==s.type&&"stat"==s.prev.type&&(s=s.prev);var d=s.type,f=o==d;return"vardef"==d?s.indented+("operator"==t.lastType||","==t.lastType?s.info+1:0):"form"==d&&"{"==o?s.indented:"form"==d?s.indented+a:"stat"==d?s.indented+(function(e,t){return"operator"==e.lastType||","==e.lastType||h.test(t.charAt(0))||/[,.]/.test(t.charAt(0))}(t,r)?l||a:0):"switch"!=s.info||f||0==n.doubleIndentSwitch?s.align?s.column+(f?0:1):s.indented+(f?0:a):s.indented+(/^(?:case|default)\b/.test(r)?a:2*a)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:u?null:"/*",blockCommentEnd:u?null:"*/",lineComment:u?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:u?"json":"javascript",jsonldMode:s,jsonMode:u,expressionAllowed:t,skipExpression:function(e){var t=e.cc[e.cc.length-1];t!=H&&t!=P||e.cc.pop()}}})),e.registerHelper("wordChars","javascript",/[\w$]/),e.defineMIME("text/javascript","javascript"),e.defineMIME("text/ecmascript","javascript"),e.defineMIME("application/javascript","javascript"),e.defineMIME("application/x-javascript","javascript"),e.defineMIME("application/ecmascript","javascript"),e.defineMIME("application/json",{name:"javascript",json:!0}),e.defineMIME("application/x-json",{name:"javascript",json:!0}),e.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),e.defineMIME("text/typescript",{name:"javascript",typescript:!0}),e.defineMIME("application/typescript",{name:"javascript",typescript:!0})})),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("../xml/xml"),require("../javascript/javascript"),require("../css/css")):"function"==typeof define&&define.amd?define(["../../lib/codemirror","../xml/xml","../javascript/javascript","../css/css"],e):e(CodeMirror)}((function(e){"use strict";var t={script:[["lang",/(javascript|babel)/i,"javascript"],["type",/^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^$/i,"javascript"],["type",/./,"text/plain"],[null,null,"javascript"]],style:[["lang",/^css$/i,"css"],["type",/^(text\/)?(x-)?(stylesheet|css)$/i,"css"],["type",/./,"text/plain"],[null,null,"css"]]};var r={};function n(e,t){var n=e.match(function(e){return r[e]||(r[e]=new RegExp("\\s+"+e+"\\s*=\\s*('|\")?([^'\"]+)('|\")?\\s*"))}(t));return n?/^\s*(.*?)\s*$/.exec(n[2])[1]:""}function i(e,t){return new RegExp((t?"^":"")+"</s*"+e+"s*>","i")}function o(e,t){for(var r in e)for(var n=t[r]||(t[r]=[]),i=e[r],o=i.length-1;o>=0;o--)n.unshift(i[o])}e.defineMode("htmlmixed",(function(r,a){var l=e.getMode(r,{name:"xml",htmlMode:!0,multilineTagIndentFactor:a.multilineTagIndentFactor,multilineTagIndentPastTag:a.multilineTagIndentPastTag}),s={},u=a&&a.tags,c=a&&a.scriptTypes;if(o(t,s),u&&o(u,s),c)for(var d=c.length-1;d>=0;d--)s.script.unshift(["type",c[d].matches,c[d].mode]);function f(t,o){var a,u=l.token(t,o.htmlState),c=/\btag\b/.test(u);if(c&&!/[<>\s\/]/.test(t.current())&&(a=o.htmlState.tagName&&o.htmlState.tagName.toLowerCase())&&s.hasOwnProperty(a))o.inTag=a+" ";else if(o.inTag&&c&&/>$/.test(t.current())){var d=/^([\S]+) (.*)/.exec(o.inTag);o.inTag=null;var h=">"==t.current()&&function(e,t){for(var r=0;r<e.length;r++){var i=e[r];if(!i[0]||i[1].test(n(t,i[0])))return i[2]}}(s[d[1]],d[2]),p=e.getMode(r,h),g=i(d[1],!0),m=i(d[1],!1);o.token=function(e,t){return e.match(g,!1)?(t.token=f,t.localState=t.localMode=null,null):function(e,t,r){var n=e.current(),i=n.search(t);return i>-1?e.backUp(n.length-i):n.match(/<\/?$/)&&(e.backUp(n.length),e.match(t,!1)||e.match(n)),r}(e,m,t.localMode.token(e,t.localState))},o.localMode=p,o.localState=e.startState(p,l.indent(o.htmlState,""))}else o.inTag&&(o.inTag+=t.current(),t.eol()&&(o.inTag+=" "));return u}return{startState:function(){return{token:f,inTag:null,localMode:null,localState:null,htmlState:e.startState(l)}},copyState:function(t){var r;return t.localState&&(r=e.copyState(t.localMode,t.localState)),{token:t.token,inTag:t.inTag,localMode:t.localMode,localState:r,htmlState:e.copyState(l,t.htmlState)}},token:function(e,t){return t.token(e,t)},indent:function(t,r){return!t.localMode||/^\s*<\//.test(r)?l.indent(t.htmlState,r):t.localMode.indent?t.localMode.indent(t.localState,r):e.Pass},innerMode:function(e){return{state:e.localState||e.htmlState,mode:e.localMode||l}}}}),"xml","javascript","css"),e.defineMIME("text/html","htmlmixed")}));