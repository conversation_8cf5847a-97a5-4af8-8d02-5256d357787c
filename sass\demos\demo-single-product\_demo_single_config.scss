/*
Demo 10 Variables
*/
@include set(
    (
        header: (
			middle: (
				padding-top: 0,
                padding-bottom: 0,
                font-size: 1.4rem,
                font-weight: 600,
				background: transparent,
				logo: (
                    margin-top: 2.7rem,
                    margin-bottom: 2.7rem,
                    margin-right: 0,
				)
            ),
            transparent: (
                fixed: (
                    background: $white-color
                )
            ),
            sticky: (
                padding-top: 0,
                padding-bottom: 0,
            )
        ),
        product: (
            name: (
                font-size: 2.4rem,
                font-weight: 600,
                color: #2
            ),
            price: (
                font-size: 5rem,
                font-weight: 800,
                line-height: 1.2,
                letter-spacing: -.025em,
                margin-top: 1.1rem,
                margin-bottom: 2.7rem
            )
        ),
        menu: ( 
            ancestor: (
                letter-spacing: .1px,
                font-size: 1.4rem,
                padding: 4.2rem 2.9rem,
                font-weight: 600
            )
        ),
        footer: (
            font-family: $font-family,
            color: #666,
            middle: (
                padding: 9.5rem 0 0,
                widget: (
                    body: (
                        padding: .9rem 0 2.8rem,
                    )
                )
            ),
            bottom: (
                padding: 3rem 0
            ),
            copyright: (
                font-family: $font-family,
                font-size: 1.3rem,
                letter-spacing: 0,
                color: #777
            ),
            social-link: (
                width: 29px,
                height: 29px,
                font-size: 1.4rem,
                margin-bottom: 8rem,
                line-height: 2.6rem
            ),
            newsletter: (
                title: (
                    font-size: 3rem,
                    font-weight: 800,
                    letter-spacing: 0,
                    margin-bottom: 2.9rem,
                    margin-top: 2px,
                    color: #fefefe
                ),
                desc: (
                    font-size: 1.4rem,
                    max-width: 56rem,
                    line-height: 2.4rem,
                    color: #666,
                    margin-left: auto,
                    margin-right: auto,
                    margin-bottom: 3.4rem
                ),
                btn: (
                    font-size: 1.3rem,
                    padding: 1.2em 1.17em 1.07em,
                    border-top-left-radius: 0,
                    border-bottom-left-radius: 0
                ),
                form: (
                    max-width: 58rem
                )
            ),
        )
    )
)  