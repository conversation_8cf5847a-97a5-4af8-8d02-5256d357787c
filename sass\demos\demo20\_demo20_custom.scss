/* 
Demo 20
*/
//title
.title.title-underline {
    position: relative;
    &::after {
        content: '';
        display: block;
        border-bottom: 4px solid #d1d1d1;
        width: 80px;
        margin: auto;
        margin-top: 12px;
        bottom: -15px;
    }
}
.btn-underline i {
	margin-bottom: 0;
}
// Header
.header-top {
    .header-left  {
        position: relative;
        overflow: hidden;
        i {
            font-size: 1.7rem;
        }
    }
    .welcome-msg {
        padding: 1.4rem 0 1.1rem;
        letter-spacing: -.025em;
        .contact {
            margin-right: 2.2rem;
        }
    }
}
.header-middle {
    .header-search i {
        font-size: 20px;
    }
    .divider {
        height: 1.9rem;
        background-color: #C4C4C4;
    }
}
.menu-active-underline>li>a::before {
    bottom: 8px;
}
.cart-dropdown.type3 .cart-toggle {
    padding: 16px 12px 16px 12px;
    background-color: #222;
    i {
        font-size: 1.3rem;
        margin-right: 8px;
    }
    &:hover {
        background-color: $primary-color;
    }
}
// Main
.category-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, calc(100%/4));
	grid-auto-rows: auto;
	> * {
		float: left;
		padding: 1rem;
	}
	.banner, .category{
		&, & > a, figure, img {
			height: 100%;
		}
	}
	img {
		object-fit: cover;
	}
	.height-x2 {
		grid-row-end: span 2;
	}
	.height-x1 {
		grid-row-end: span 1;
	}
	.w-1 {
		grid-column-end: span 1;
	}
	.w-2 {
		grid-column-end: span 2;
	}
	.banner-content {
		position: absolute;
		text-align: center;
		border-radius: .3rem;
		padding: 23px 0px 23px 0px;
		background-color: rgba(255,255,255, .9);
		box-shadow: 0px 0px 0px 0px #FFFFFF inset;
		max-width: 240px;
		width: 100%;
	}
	.category-content {
		opacity: .9;
		bottom: 2rem;
	}
	.banner-title {
		color: #303030;
		font-size: 20px;
		font-weight: 700;
		line-height: 1em;
		letter-spacing: 0.5px;
		margin-bottom: 0;
	}
	.banner-subtitle {
		display: block;
		font-size: 18px;
		font-weight: 400;
		line-height: 1em;
		letter-spacing: 0px;
		color: #666;
		margin-bottom: 1rem;
	}
	.banner2 .banner-content {
		bottom: 2rem;
		max-width: 25rem;
	}
}
.category-badge .category-name {
    font-weight: 600;
    letter-spacing: -.025em;
    margin-bottom: 0;
}
.brand-section {
	.owl-carousel figure {
		height: 14.2rem;
		object-fit: cover;
		border: 1px solid #eee;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}
.post-frame {
	.post-content {
		margin-bottom: 2rem;
		letter-spacing: -.025em;
	}
	.btn {
		letter-spacing: -.025em;
	}
}
.footer {
    .widget.widget-info label, .widget.widget-info a {
        font-weight: 400;
    }
}
.page-title {
	margin-bottom: 0px;
}
// Product page
.singe-product {
	.title {
		font-size: 2.4rem;
	}
}
// Shop page
aside .owl-nav-top .owl-nav {
	direction: ltr;
	top: -3.8rem;
}
@include mq(lg, max) {
    .header-top {
        .header-left  {
            margin-right: 0;
        }
        .call {
            display: none;
        }
    }
    .header-middle {
        .header-left , .header-right {
            flex: none;
        }
        .header-center {
            flex: 1;
        }
    }
}
@include mq(md, max) {
    .header-top {
        .wishlist, .login-link {
            display: none;
        }
	}
	.category-grid {
		grid-template-columns: repeat(auto-fill, calc(100% / 2));
		.height-x2 {
			grid-row-end: span 2;
		}
	}
}
@include mq(xs, max) {
	.category-grid .height-x1 {
		min-height: 28rem;
	}
}
