/* -------------------------------------------
		Typography
---------------------------------------------- */
@include set-default(
	(
		base: (
			list-circle: (
				icon: (
					font-size: 1.1rem
				)
			)
		)
	)
);
h1,
h2,
h3,
h4,
h5,
h6,
p {
	font-weight: 400;
	margin: 0 0 2rem;
}
h1,
h2,
h3,
h4,
h5,
h6 {
	font-family: $font-family;
	font-weight: 600;
	line-height: 1.4;
	color: #222;
}
h1 {
	font-size: 4rem;
}
h2 {
	font-size: 3.4rem;
}
h3 {
	font-size: 3rem;
}
h4 {
	font-size: 2.4rem;
}
h5 {
	font-size: 1.8rem;
}
h6 {
	font-size: 1.5rem;
}
p {
	font-family: $second-font-family;
	font-size: 1.4rem;
	line-height: 1.86;
}
// List
.list {
	margin-left: 2rem;
	font-size: 1.4rem;
	line-height: 1.86;
	color: #666;
}