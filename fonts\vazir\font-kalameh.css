@font-face {
  font-family: kalameh;
  src: url('KalamehWeb_Black.eot');
  src: url('KalamehWeb_Black.eot?#iefix') format('embedded-opentype'),
       url('KalamehWeb_Black.woff2') format('woff2'),
       url('KalamehWeb_Black.woff') format('woff'),
       url('KalamehWeb_Black.ttf') format('truetype');
  font-weight: normal;
}
@font-face {
  font-family: kalameh;
  src: url('KalamehWeb_Bold.eot');
  src: url('KalamehWeb_Bold.eot?#iefix') format('embedded-opentype'),
       url('KalamehWeb_Bold.woff2') format('woff2'),
       url('KalamehWeb_Bold.woff') format('woff'),
       url('KalamehWeb_Bold.ttf') format('truetype');
  font-weight: bold;
}
@font-face {
  font-family: kalameh;
  src: url('KalamehWeb_Regular.eot');
  src: url('KalamehWeb_Regular.eot?#iefix') format('embedded-opentype'),
       url('KalamehWeb_Regular.woff2') format('woff2'),
       url('KalamehWeb_Regular.woff') format('woff'),
       url('KalamehWeb_Regular.ttf') format('truetype');
  font-weight: 100;
}
@font-face {
  font-family: kalameh;
  src: url('KalamehWeb_thin.eot');
  src: url('KalamehWeb_thin.eot?#iefix') format('embedded-opentype'),
       url('KalamehWeb_thin.woff2') format('woff2'),
       url('KalamehWeb_thin.woff') format('woff'),
       url('KalamehWeb_thin.ttf') format('truetype');
  font-weight: 300;
}
