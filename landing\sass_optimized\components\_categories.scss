﻿/* -------------------------------------------
    Categories
        Base
        - Default
        - Light Category
        - دسته بندی آیکن
        - Ellipse Category
        - Group Category
            - Image
            - Icon
        - Banner Category (Masonry)
        - Overlay Category
        - Block Category
---------------------------------------------- */
// Base
.category {
    position: relative;
    font-size: 1rem;
    img {
        display: block;
        width: 100%;
        height: auto;
        margin-left: auto;
        margin-right: auto;
    }
    .category-name {
        margin-bottom: .3rem;
        font: {
            size: 1.4rem;
            weight: 600;
        }
        line-height: 1.2;
        color: inherit;
        text-transform: uppercase;
        a {
            color: inherit;
            &:hover {
                text-decoration: underline;
            }
        }
    }
    .category-count {
        font-size: 1.3rem;
        line-height: 1.2;
    }
}
.category-content {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    transition: background .3s;
}
.category-absolute {
    .category-content {
        cursor: pointer;
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 25%;
        min-height: 5rem;
    }
}
// Default
.category-default {
    color: #fff;
    &:hover {
        .category-content {
            background-color: rgba( $primary-color, .8 );
        }
    }
    .category-count {
        display: block;
        opacity: 0;
        line-height: 0;
        visibility: hidden;
        transition: opacity .3s, line-height .3s, visibility .3s;
    }
    &:hover {
        .category-name {
            margin-bottom: .3rem;
        }
        .category-count {
            visibility: visible;
            line-height: 1.2;
            opacity: 1;
        }
    }
    .category-content {
        background-color: rgba( 38, 38, 38, .8 );
    }
}
// Light Category
.category-light {
    color: #999;
    &:hover {
        .category-content {
            background-color: rgba( $primary-color, .8 );
        }
    }
    .btn {
        position: absolute;
        opacity: 0;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        font: {
            size: 1.3rem;
            weight: 600;
        }
        transition: opacity .3s, bottom .3s, transform .3s;
    }
    .category-name {
        color: #222;
        transition: color .3s;
    }
    .category-count {
        text-transform: uppercase;
        transition: color .3s, opacity .3s;
    }
    .category-content {
        bottom: 2rem;
        height: 27.7%;
        background-color: rgba( 255, 255, 255, .8 );
        &:hover {
            .btn {
                bottom: calc(50% - 5px);
                transform: translate(-50%, 100%);
                opacity: 1;
            }
            .category-count {
                opacity: 0;
            }
        }
    }
    &:hover {
        color: #fff;
        .category-name {
            color: #fff;
        }
    }
}
// دسته بندی آیکن
// Boxed
// Inline
.category-icon {
    padding: 2.4rem .5rem;
    border: 2px solid #e1e1e1;
    color: #222;
    text-align: center;
    transition: border .3s;
    svg {
        display: block;
        margin-bottom: 1.5rem;
        margin-left: auto;
        margin-right: auto;
        height: 4.2rem;
        fill: #666;
        transition: .3s;
    }
    i {
        transition: transform .3s;
    }
    .category-name {
        margin: 0;
        color: #222;
    }
    &:hover {
        border-color: $primary-color;
        svg, i {
            fill: $primary-color;
            transform: translateY(-4px);
        }
        .category-content {
            background: transparent;
        }
    }
}
.category-icon-inline {
    display: inline-flex;
    align-items: center;
    .category-media {
        font-size: 3.2rem;
        line-height: 1;
        color: #444;
        transition: color .3s;
        i {
            margin-right: .8rem;
        }
        svg {
            display: block;
            margin-right: 1rem;
            width: 3.5rem;
            height: 3.9rem;
            stroke: #444;
            fill: #444;
            transition: stroke .3s;
        }
    }
    .category-name {
        margin: 0;
        text-transform: none;
        text-align: left;
        font-size: 1.3rem;
        font-weight: inherit;
        font-family: inherit;
        line-height: 1.08;
        color: inherit;
        transition: color .3s;
    }
    &:hover {
        .category-media,
        .category-name {
            color: $primary-color;
        }
        svg {
            stroke: $primary-color;
            fill: $primary-color;
        }
        i, svg {
            animation: slideUpShorter .6s;
        }
    }
}
@keyframes slideUpShorter {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-4px);
    }
    100% {
        transform: translateY(0);
    }
}
// Ellipse Category
.category-ellipse {
    .category-media {
        border-radius: 50%;
        overflow: hidden;
        -webkit-mask-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAA5JREFUeNpiYGBgAAgwAAAEAAGbA+oJAAAAAElFTkSuQmCC)
    }
    .category-content {
        background-color: #fff;
    }
    .category-name {
        letter-spacing: -.025em;
        color: #222;
        &:hover {
            color: $primary-color;
        }
    }
    .category-count {
        opacity: 0;
        color: #999;
        transition: opacity .3s;
    }
    &:hover {
        .category-count {
            opacity: 1;
        }
    }
}
// Group Category
.category-group-image,
.category-group-icon {
    display: flex;
    align-items: center;
    border: 1px solid $border-color;
    > * {
        flex: 1;
    }
}
// Image
.category-group-image {
    color: #666;
    .category-content {
        position: static;
        padding: 2rem 0;
        background: transparent;
        text-align: left;
        align-items: flex-start;
    }
    .category-name {
        margin-bottom: 1.5rem;
        text-transform: none;
        font: {
            size: 1.6rem;
            weight: 400;
        }
        color: #222;
    }
    .category-list {
        font: {
            family: $second-font-family;
            size: 1.3rem;
        }
        line-height: 1.2;
        li:not(:last-child) {
            margin-bottom: .5rem;
        }
         a {
            position: relative;
            display: inline-block;
            transition: text-decoration .3s;
            &:hover {
                color: #222;
                text-decoration: underline;
            }
            &:active {
                color: $primary-color;
            }
        }
    }
    &:hover {
        .category-name {
            margin-bottom: 1.5rem;
        }
        .category-content {
            background: transparent;
        }
    } 
}
// Icon
.category-group-icon {
    display: flex;
    align-items: center;
    border: 0;
    > * {
        flex: 1;
    }
    color: #fff;
    .category-media {
        svg {
            display: block;
            height: 5.6rem;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 1.8rem;
            fill: #fff;
            transition: transform .3s;
        }
    }
    > a {
        padding-right: 1rem;
        &:hover svg {
            transform: translateY(-.5rem);
            // animation-iteration-count: infinite;
        }
    }
    .category-content {
        padding-top: 4rem;
        padding-bottom: 4rem;
        background: transparent;
        text-align: left;
        align-items: flex-start;
    }
    .category-name {
        margin-bottom: 0;
        text-align: center;
        letter-spacing: -.025em;
        color: #fff;
    }
    .category-list {
        font: {
            family: $second-font-family;
            size: 1.3rem;
        }
        line-height: 1.2;
        li {
            white-space: nowrap;
            &::before {
                content: '\f105';
                padding-right: .8rem;
                font-family: 'Font Awesome 5 Free';
                font-size: 1.3rem;
                font-weight: 600;
                color: #999;
            }
            &:not(:last-child) {
                margin-bottom: .5rem;
            }
        }
        a {
            position: relative;
            display: inline-block;
            transition: text-decoration .3s;
            &:hover {
                color: inherit;
                text-decoration: underline;
            }
        }
    }
    &:hover {
        .category-name {
            margin-bottom: 0;
        }
        .category-content {
            background: transparent;
        }
    } 
}
// Banner Category (Masonry)
.category-banner {
    overflow: hidden;
    color: #222;
    &.text-white {
        color: #fff;
        .btn {
            color: #fff;
        }
    }
    .category-content {
        top: 4.5rem;
        left: 5rem;
        bottom: auto;
        width: auto;
        height: auto;
        align-items: flex-start;
        z-index: 1;
        background-color: transparent;
        transition: top .3s, padding .3s;
    }
    .category-name {
        text-transform: none;
        text-align: left;
        font: {
            size: 2rem;
            family: inherit;
        }
        letter-spacing: -.025em; 
    }
    .category-count {
        visibility: visible;
        opacity: 1;
        font-size: 1.4rem;
        line-height: 1.2;
        transition: color .3s;
        z-index: 1;
    }
    .btn {
        position: absolute;
        opacity: 0;
        bottom: 0;
        left: 0;
        transition: transform .3s, opacity .3s;
    }
    &:hover {
        .category-content {
            top: 3rem;
            padding-bottom: 3rem;
            background-color: transparent;
        }
        .btn {
            opacity: 1;
        }
    }
}
// Badge Category
.category-badge {
    .category-name {
        margin: 0;
        padding: 1.8rem 0;
        color: #222;
    }
    .btn {
        position: absolute;
        padding: 1.28em 0;
        top: 100%;
        left: 0;
        opacity: 0;
        height: 100%;
        transition: top .3s, opacity .3s;
    }
    .category-content {
        left: 2rem;
        top: 2rem;
        bottom: auto;
        width: 19.6rem;
        height: auto;
        z-index: 1;
        overflow: hidden;
        background-color: #fff;
        &:hover {    
            .btn {
                top: 0;
                opacity: 1;
            }
        }
    }
}
// Overlay Category
.category-overlay {
    .category-content {
        align-items: center;
        width: 100%;
    }
    .category-count {
        padding: 0;
        line-height: 0;
        opacity: 0;
        transform: translateY(-1rem);
        transition: transform .3s, opacity .3s, line-height .3s, padding .3s;
    }
    .category-name {
        margin: 0;
    } 
    &:hover {
        .category-content {
            padding: 0;
            top: 50%;
        }
        .category-count {
            padding-top: 1rem;
            transform: translateY(0);
            opacity: 1;
        }
    }
}
// Block Category
.category-block {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 10.7rem;
    padding: 1.5rem;
    color: #fff;
    background-color: #333;
    transition: background-color .3s;
    .category-name {
        margin: 0;
        text-align: center;
        font-weight: 400;
        line-height: 1.7rem;
        text-transform: none;
    }
    &:hover {
        background-color: $primary-color;
        .category-name {
            color: #fff;
        }
    }
}
