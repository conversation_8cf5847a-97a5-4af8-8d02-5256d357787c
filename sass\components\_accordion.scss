﻿/* -------------------------------------------
    Accordion
---------------------------------------------- */
// Default
.accordion {
	overflow: hidden;
	.collapsed,
	.expanding {
		display: none;
		// overflow: hidden;
		// height: 0px;
	}
}
.card-header {
	text-transform: capitalize;
	font: {
		size: 1.8rem;
		weight: 600;
	}
	line-height: 1;
	color: $dark-color;
	a {
		display: flex;
		align-items: center;
		position: relative;
		padding: 1.8rem .5rem;
		&:hover {
			color: $primary-color;
			span {
				color: $primary-color;
			}
		}
		&::after {
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			right: .5rem;
			font: {
				family: "riode";
				size: 1.8rem;
				weight: 400;
			}
			letter-spacing: -.01em;
			color: $dark-color;
		}
	}
	.collapse {
		color: $primary-color;
	}
}
.expand {
	&::after {
		content: '\e953';
	}
}
.collapse {
	&::after {
		content: '\e952';
	}
}
.card-body {
	padding: 1.3rem .5rem;
}
.card {
	background-color: transparent;
}
// Simple
.accordion-simple {
	.card {
		border-top: 1px solid $border-color;
	}
}
// Gutter
.accordion-gutter-md {
	.card {
		&:not(:last-child) {
			margin-bottom: 10px;
		}
	}
}
.accordion-gutter-sm {
	.card {
		&:not(:last-child) {
			margin-bottom: 2px;
		}
	}
}
// Toggle Icon Plus
.accordion-plus {
	.expand {
		&::after {
			content: '\f2c3';
		}
	}
	.collapse {
		&::after {
			content: '\f28f';
		}
	}
}
// Boxed Style
.accordion-boxed {
	.card-header a,
	.card-body {
		padding-left: 2rem;
		padding-right: 2rem;
	}
	.card-header {
		a::after {
			right: 2.3rem;
		}
	}
}
// Border Style
.accordion-border {
	.card {
		border-width: 1px 1px 0;
		border-style: solid;
		border-color: $border-color;
		&:last-child {
			border-bottom: 1px solid $border-color;
		}
	}
} 
// Background Style
.accordion-background {
	.card {
		background-color: $bg-secondary-white-color;
		border-color: $white-color;
	}
}
// با سایه Style
.accordion-dropshadow {
	box-shadow: 0 0 15px 0 rgba(0,0,0,.12);
	.card {
		background-color: $bg-secondary-white-color;
		border-color: $white-color;
	}
}
// Icon Style
.accordion-icon {
	.card-header {
		i {
			margin-right: 1.5rem;
			font: {
				size: 1.8rem;
				weight: 400;
			}
			letter-spacing: -.01em;
			line-height: 0;
		}
	}
}
// Card Background Style
.accordion-card-bg {
	.card {
		border: 1px solid #f2f3f5;
	}
	.card-header {
		a {
			padding-top: 1.7rem;
			padding-bottom: 1.7rem;
			background-color: #f2f3f5;
		}
	}
	&.accordion-primary {
		.card {
			border: 0;
			background-color: #f2f3f5;
		}
		.card-header {
			a {
				padding-top: 1.8rem;
				padding-bottom: 1.8rem;
				background-color: #37c;
				color: $white-color;
				&::after {
					color: $white-color;
				}
			}
		}
	}
}
// Set active color when Card is expanded.
.accordion-color {
	.collapse {
		color: $primary-color;
	}
}
// Card Border
.accordion-card-border {
	.card {
		border: 1px solid #dae1e5;
	}
}