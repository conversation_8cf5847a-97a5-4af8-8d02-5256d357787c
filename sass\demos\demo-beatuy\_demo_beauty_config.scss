/* 
Demo beauty
*/
@include set(
    (
        base: (
            body: (
                font-family: 'vazir'
            ),
            title: (
                font-size: 2.4rem,
                font-weight: 700,
                line-height: 0.8,
                letter-spacing: -0.6px,
                text-transform: none,
                margin-bottom: 4.5rem,
            )
        ),
        header: (
            top: (
                border-bottom: 1px solid #e1e1e1,
                font-size: 1.3rem,
                _links-gap: 2.5rem,
                color: rgba(34, 34, 34, 0.8),
                wishlist: (
                    margin-right: 0,
                    icon: (
                        font-size: 1.4rem,
                        margin-right: 1.2rem
                    )
                ),
                login: (
                    icon: (
                        margin-right: .8rem,
                        font-size: 1.4rem
                    )
                )
            ),
            middle: (
                padding-top: 2.5rem,
                padding-bottom: 2.5rem,
                letter-spacing: 0,
            ),
            contact: (
                icon: (
                    font-size: 1.6rem,
                )
            ),
            help: (
                icon: (
                    font-size: 1.6rem,
                    margin-left: 3px
                )
            ),
            call: (
                letter-spacing: -.1px,
                label: (
                    _gap: 0,
                    font-size: false,
                    font-weight: inherit,
                    line-height: false,
                    text-transform: capitalize,
                    margin-right: .3rem
                ),
                icon: (
                    margin: 0 0 0 1rem,
                    font-size: 1.4rem
                )
            ),
            sticky: (
                padding-top: 1.6rem,
                padding-bottom: 1.6rem
            )
        ),
        menu: (
            ancestor: (
                _gap: 3.5rem,
                padding: 1.6rem 0,
                font-family: false,
                font-size: 14px,
                font-weight: 700,
                letter-spacing: inherit,
                line-height: 1.2,
                text-transform: capitalize,
                color: false,
                _active-color: false,
            ),
        ),
        product: (
            body: (
                padding-top: 1.7rem,
            ),
            name: (
                font-family: 'vazir',
            ),
            price: (
                font-family: 'vazir',
                letter-spacing: -.025em,
            ),
            rating: (
                font-family: 'vazir'
            ),
            label: (
                font-weight: 'vazir'
            )
        ),
        post: (
            meta: (
                margin-bottom: .5rem
            ),
            title: (
                margin-bottom: .5rem,
                line-height: 1.5
            ),
            content: (
                _row-count: 2,
            ),
            btn: (
                _icon-gap: .5rem
            ),
            calendar: (
                font-family: 'vazir',
                color: #222
            )
        ),
        widget: (
            title: (
                border-bottom: 1px solid $border-color-light
            )
        ),
        footer: (
			middle: (
				padding: 82px 0px 40px 0px
			),
			bottom: (
				padding: 2.8rem 0
			),
			about: (
                logo: (
					margin-top: -1px,
                    margin-bottom: 1.9rem,
                ),
                p: (
                    margin-bottom: 2.7rem,
                    letter-spacing: -.000 8em
                )
            ),
			copyright: (
                color: #666,
                font-weight: 400,
				letter-spacing: -.01em
			),
			social-link: (
				width: 2.9rem,
				height: 2.9rem
			)
		)
    )
);