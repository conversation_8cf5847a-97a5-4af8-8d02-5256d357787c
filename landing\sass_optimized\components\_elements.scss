/* -------------------------------------------
    Element List
---------------------------------------------- */
$elements : (accordian, banner, blog, button, cta, icon-box, portfolio, category, product, tab, testimonial, title, typography, video);
.elements {
    > div {
        margin-bottom: 2rem;
        > a {
            display: block;
        }
    }
}
// Element
.element {
    display: flex;
    position: relative;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.2rem 0 2.4rem;
    border: 2px solid #e4eaec;
    p {
        margin: 0;
        text-align: center;
        color: #222;
        font: {
            size: 1.4rem;
            weight: 600;
        }
        line-height: 1.2;
        text-transform: uppercase;
    }
}
