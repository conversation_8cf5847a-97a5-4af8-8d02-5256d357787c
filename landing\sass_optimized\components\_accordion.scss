﻿/* -------------------------------------------
    Accordion
---------------------------------------------- */
// Default
.accordion {
	overflow: hidden;
	.collapsed,
	.expanding {
		display: none;
		// overflow: hidden;
		// height: 0px;
	}
}
.card-header {
	text-transform: uppercase;
	font: {
		size: 1.5rem;
		weight: 600;
	}
	letter-spacing: -.01em;
	line-height: 1;
	color: #222;
	a {
		display: flex;
		align-items: center;
		position: relative;
		padding: 1.8rem .5rem;
		&:hover {
			color: $primary-color;
		}
		&::after {
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			right: .4rem;
			font: {
				family: 'LineAwesome';
				size: 1.4rem;
				weight: 400;
			}
			letter-spacing: -.01em;
			color: #222;
		}
	}
}
.expand {
	&::after {
		content: '\f110';
	}
}
.collapse {
	&::after {
		content: '\f113';
	}
}
.card-body {
	padding: 1.3rem .5rem;
}
.card {
	background-color: #fff;
}
// Simple
.accordion-simple {
	.card {
		border-top: 1px solid #ebebeb;
		&:last-child {
			border-bottom: 1px solid #ebebeb;
		}
	}
}
// Gutter
.accordion-gutter-md {
	.card {
		&:not(:last-child) {
			margin-bottom: 10px;
		}
	}
}
.accordion-gutter-sm {
	.card {
		&:not(:last-child) {
			margin-bottom: 2px;
		}
	}
}
// Toggle Icon Plus
.accordion-plus {
	.card-header {
		a {
			&::after {
				font: {
					size: 1.7rem;
					weight: 400;
				}
				color: #666;
			}
		}
	}
	.expand {
		&::after {
			content: '\f2c2';
		}
	}
	.collapse {
		&::after {
			content: '\f28e';
		}
	}
}
// Boxed Style
.accordion-boxed {
	.card-header a,
	.card-body {
		padding-left: 2rem;
		padding-right: 2rem;
	}
	.card-header {
		a::after {
			right: 2rem;
		}
	}
}
// Border Style
.accordion-border {
	.card {
		border-top: 1px solid #ebebeb;
		border-right: 1px solid #ebebeb;
		border-right: 1px solid #ebebeb;
		&:last-child {
			border-bottom: 1px solid #ebebeb;
		}
	}
} 
// Background Style
.accordion-background {
	.card {
		background-color: #fff;
		border-color: #fff;
	}
}
// با سایه Style
.accordion-dropshadow {
	box-shadow: 0 0 15px 0 rgba(0,0,0,.12);
	.card {
		background-color: #fff;
		border-color: #fff;
	}
}
// Icon Style
.accordion-icon {
	.card-header {
		i {
			margin-right: 1.5rem;
			font: {
				size: 1.8rem;
				weight: 400;
			}
			letter-spacing: -.01em;
			line-height: 0;
		}
	}
}
// Card Background Style
.accordion-card-bg {
	.card {
		border: 1px solid #f2f3f5;
	}
	.card-header {
		a {
			padding-top: 1.7rem;
			padding-bottom: 1.7rem;
			background-color: #f2f3f5;
		}
	}
	&.accordion-primary {
		.card {
			border: 0;
			background-color: #f2f3f5;
		}
		.card-header {
			a {
				padding-top: 1.8rem;
				padding-bottom: 1.8rem;
				background-color: $primary-color;
				color: #fff;
				&::after {
					color: #fff;
				}
			}
		}
	}
}
// Set active color when Card is expanded.
.accordion-color {
	.collapse {
		color: $primary-color;
	}
}
// Card Border
.accordion-card-border {
	.card {
		border: 1px solid #dae1e5;
	}
}