!function(g){g.fn.gMap=function(e,o){switch(e){case"addMarker":return g(this).trigger("gMap.addMarker",[o.latitude,o.longitude,o.content,o.icon,o.popup]);case"centerAt":return g(this).trigger("gMap.centerAt",[o.latitude,o.longitude,o.zoom]);case"clearMarkers":return g(this).trigger("gMap.clearMarkers")}var w=g.extend({},g.fn.gMap.defaults,e);return this.each(function(){var c=new google.maps.Map(this);g(this).data("gMap.reference",c);var e=new google.maps.Geocoder;w.address?e.geocode({address:w.address},function(e,o){e&&e.length&&c.setCenter(e[0].geometry.location)}):w.latitude&&w.longitude?c.setCenter(new google.maps.LatLng(w.latitude,w.longitude)):g.is<PERSON>rray(w.markers)&&0<w.markers.length?w.markers[0].address?e.geocode({address:w.markers[0].address},function(e,o){e&&0<e.length&&c.setCenter(e[0].geometry.location)}):c.setCenter(new google.maps.LatLng(w.markers[0].latitude,w.markers[0].longitude)):c.setCenter(new google.maps.LatLng(34.885931,9.84375)),c.setZoom(w.zoom),c.setMapTypeId(google.maps.MapTypeId[w.maptype]);var o={scrollwheel:w.scrollwheel,disableDoubleClickZoom:!w.doubleclickzoom};!1===w.controls?g.extend(o,{disableDefaultUI:!0}):0!==w.controls.length&&g.extend(o,w.controls,{disableDefaultUI:!0}),c.setOptions(o);var a,n,p=new google.maps.Marker;(a=new google.maps.MarkerImage(w.icon.image)).size=new google.maps.Size(w.icon.iconsize[0],w.icon.iconsize[1]),a.anchor=new google.maps.Point(w.icon.iconanchor[0],w.icon.iconanchor[1]),p.setIcon(a),w.icon.shadow&&((n=new google.maps.MarkerImage(w.icon.shadow)).size=new google.maps.Size(w.icon.shadowsize[0],w.icon.shadowsize[1]),n.anchor=new google.maps.Point(w.icon.shadowanchor[0],w.icon.shadowanchor[1]),p.setShadow(n)),g(this).bind("gMap.centerAt",function(e,o,a,n){n&&c.setZoom(n),c.panTo(new google.maps.LatLng(parseFloat(o),parseFloat(a)))});var m,s,h=[];g(this).bind("gMap.clearMarkers",function(){for(;h[0];)h.pop().setMap(null)}),g(this).bind("gMap.addMarker",function(e,o,a,n,s,t){var r,i,g=new google.maps.LatLng(parseFloat(o),parseFloat(a)),l=new google.maps.Marker({position:g});if(s?((r=new google.maps.MarkerImage(s.image)).size=new google.maps.Size(s.iconsize[0],s.iconsize[1]),r.anchor=new google.maps.Point(s.iconanchor[0],s.iconanchor[1]),l.setIcon(r),s.shadow&&((i=new google.maps.MarkerImage(s.shadow)).size=new google.maps.Size(s.shadowsize[0],s.shadowsize[1]),i.anchor=new google.maps.Point(s.shadowanchor[0],s.shadowanchor[1]),p.setShadow(i))):(l.setIcon(p.getIcon()),l.setShadow(p.getShadow())),n){"_latlng"===n&&(n=o+", "+a);var d=new google.maps.InfoWindow({content:w.html_prepend+n+w.html_append});google.maps.event.addListener(l,"click",function(){m&&m.close(),d.open(c,l),m=d}),t&&google.maps.event.addListenerOnce(c,"tilesloaded",function(){d.open(c,l)})}l.setMap(c),h.push(l)});function t(a){return function(e,o){e&&0<e.length&&g(r).trigger("gMap.addMarker",[e[0].geometry.location.lat(),e[0].geometry.location.lng(),a.html,a.icon,a.popup])}}for(var r=this,i=0;i<w.markers.length;i++)(s=w.markers[i]).address?("_address"===s.html&&(s.html=s.address),e.geocode({address:s.address},t(s))):g(this).trigger("gMap.addMarker",[s.latitude,s.longitude,s.html,s.icon,s.popup])})},g.fn.gMap.defaults={address:"",latitude:0,longitude:0,zoom:1,markers:[],controls:[],scrollwheel:!1,doubleclickzoom:!0,maptype:"ROADMAP",html_prepend:'<div class="gmap_marker">',html_append:"</div>",icon:{image:"http://www.google.com/mapfiles/marker.png",shadow:"http://www.google.com/mapfiles/shadow50.png",iconsize:[20,34],shadowsize:[37,34],iconanchor:[9,34],shadowanchor:[6,34]}}}(jQuery);