﻿/* -------------------------------------------
    Blog
        - Default
        - Video
        - List
        - Image gap
        - Ovelay
        - Large
        - فیلتر
---------------------------------------------- */
// Variables
@include set-default(
	(
		post: (
            padding: false,
            font-family: $font-family,
            detail: (
                padding: 2.3rem 0 2rem,
            ),
            // Info
            meta: (
                margin-bottom: .6rem,
                font-family: inherit,
                font-size: 1.3rem,
                text-transform: false,
                font-weight: inherit,
                line-height: 1.2,
                letter-spacing: false,
                color: $grey-color,
                info: (
                    font-weight: 600,
                    color: $dark-color
                )
            ),
             // Title
            title: (
                margin-bottom: .5rem,
                text-transform: capitalize,
                font-family: false,
                font-size: 1.8rem,
                font-weight: 700,
                line-height: 1.5,
                letter-spacing: false,
                color: false,
            ),
            // Content
            content: (
                margin-bottom: 1.8rem,
                text-transform: false,
                font-family: false,
                font-size: false,
                font-weight: false,
                line-height: 1.72,
                letter-spacing: 0,
                _row-count: 3,
                color: $body-color,
            ),
            // Calendar
            calendar: (
                width: 4.5rem,
                height: 4.7rem,
                background: rgba(255,255,255,.8),
                color: $dark-color,
                border: false,
                font-weight: 600,
                line-height: 1,
                letter-spacing: false,
                border-radius: .3rem,
                day: (
                    font-size: 1.6rem
                ),
                month: (
                    font-size: 1rem
                )
            ),
            // Button
            btn: (
                _icon-gap: 8px
            )
        )
    )
);
// Default
.post {
    @include print_css( post );
    .btn {
        i {
            @include css( margin-left , post, btn, _icon-gap );
            &::before {
                margin: 0;
            }
        }
    }
    .post-details > *:last-child {
        margin-bottom: 0;
    }
}
.post-calendar {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    @include print_css( post, calendar );
    .post-day {
        display: block;
        margin-bottom: 1px;
        @include print_css( post, calendar, day );
    }
    .post-month {
        display: block;
        margin-left: 2px;
        @include print_css( post, calendar, month );
    }
}
.post-media {
    position: relative;
    width: 100%;
    margin-bottom: 0;
    overflow: hidden;
    border-radius: .3rem;
    img {
        display: block;
        width: 100%;
        height: auto;
        transition: transform .3s;
    }
    .post-calendar {
        position: absolute;
        left: 2rem;
        top: 2rem;
    }
    .owl-dots {
        bottom: 2.5rem;
        .owl-dot {
            span {
                border-color: $bg-secondary-white-color;
                background-color: transparent;
            }
            &:hover span {
                border-color: $bg-secondary-white-color;
                background-color: $bg-secondary-white-color;
            }
        } 
    }
}
.post-details {
    @include print_css( post, detail );
}
.post-meta {
    @include print_css( post, meta );
    a, span {
        @include print_css( post, meta, info );
    }
    a {
        text-transform: capitalize;
        &:hover {
            color: $primary-color;
        }
    }
    span {
        transition: color .3s;
    }
    .post-author { 
        text-transform: uppercase;
    }
    .post-comment {
        @include print_css ( post, meta );
        &:hover {
            span {
                color: inherit;
            }
        }
    }
}
.post-title {
    // white-space: nowrap;
    // overflow: hidden;
    // text-overflow: ellipsis;
    @include print_css( post, title );
}
.post-content {
    @include text-block( #{get(post, content, _row-count)} );
    @include print_css( post, content );
}
@include mq('sm') {
    .post-classic .post-title {
        font-size: 2.4rem;
    }
}
.post-sm .post-details {
    padding: 2.2rem .3rem 2rem;
}
.inner-video {
    .post-media {
        position: relative;
    }
    .video-play {
        display: inline-block;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);
        transition: color .3s, opacity .3s;
        font: {
            family: 'Font Awesome 5 Free';
            size: 6rem;
        }
        color: $white-color;
        cursor: pointer;
        z-index: 10;
        &::before {
            content: '\f144';
        }
    }
    video  {
        display: none;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    &.playing,
    &.paused {
        video {
            display: block;
        }
        .video-play {
            opacity: 0;
            &:before {
                content: '\f28b';
            }
        }
        .post-media {
            background-color: $black-bg;
            &:hover {
                .video-play {
                    opacity: 1;
                }
            }
        }
        img {
            visibility: hidden;
        }
    }
    &.paused {
        .video-play {
            opacity: 1;
        }
        .video-play {
            &:before {
                content: '\f144';
            }
        }
    }
}
// List
.post-list {
    margin-bottom: 2rem;
    .post-details {
        padding: 2rem 0; 
    }
    img {
        min-height: 20rem;
        object-fit: cover;
    }
}
@include mq(sm) {
    .post-list {
        display: flex;
        align-items: center;
        .post-media {
            margin-right: 2rem;
        }
        .post-details,
        .post-media {
            width: calc(50% - 1rem);
        }
    }
}
.post-list-xs {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    margin-left: 2px;
    .post-calendar {
        color: $light-color;
        border-color: $light-color;
        background-color: transparent;
    }
    .post-details {
        flex: 1;
        margin: 0 2rem 0 0 ;
        padding: 0;
    }
    .post-title {
        @include text-block();
        margin-bottom: 0;
        text-transform: none;
        font: {
            size: inherit;
            weight: 400
        }
        line-height: 1.69;
        color: inherit;
        white-space: normal;
        &:hover {
            a {
                color: $white-color;
            }
        }
    }
}
.post-list-sm {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    .post-media {
        max-width: 9rem;
        flex: 0 0 9rem;
        height: 9rem;
        img {
            height: 100%;
            object-fit: cover;
        }
    }
    .post-details {
        padding: 0;
        margin-left: 1.6rem;
    }
    .post-meta {
        margin-bottom: .4rem;
        a {
            font-size: 1.3rem;
            font-weight: 400;
            color: $grey-color;
        }
    }
    .post-title {
        margin-bottom: 0;
        white-space: normal;
        text-transform: none;
        font-size: 1.4rem;
        font-weight: 600;
        letter-spacing: 0;
    }
}
.post-col {
	.post {
		margin: 0;
		padding: 1rem .3rem;
	}
}
// Image-gap
.post-image-gap {
    padding: 2rem;
    background-color: $bg-secondary-white-color;
    transition: box-shadow .3s;
    .post-details {
        padding-bottom: 1rem;
    }
    &:hover {
        box-shadow: 0px 0px 7px 0px rgba(0,0,0,.1);
    }
}
// Overlay
.post-mask {
    position: relative;
    &::before {
        content: '';
        display: block;
        height: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        opacity: .75;
        background: rgba(51,51,51,.5);
        transition: all 0.3s;
        pointer-events: none;
        z-index: 1;
    }
    img {
        min-height: 19rem;
    }
    &.gradient {
        &::before {
            height: 50%;
            background: linear-gradient(to bottom, rgba(125,185,232,0) 0%, $black-color 100%);
        }
    }
    .post-details {
        position: absolute;
        padding: 0;
        left: 3rem;
        right: 3rem;
        bottom: 2.2rem;
        z-index: 2;
        transform: translateY(4rem);
        transition: transform .4s;
    }
    .post-meta {
        color: $white-color;
        a, span {
            color: inherit;
        }
        a {
            font-weight: 400;
        }
    }
    .post-title {
        margin: 0;
        //white-space: normal;
        text-transform: none;
        line-height: 1.32;
        color: $white-color;
        a:hover {
            color: $white-color;
        }
    }
    .btn-link {
        margin-top: 1.5rem;
        opacity: 0;
        transition: opacity .4s;
    }
    &:hover {    
        .post-media {
            a::before {
                opacity: .1;
            }
        }
        .post-details {
            transform: translateY(0);
        }
        .btn-link {
            opacity: 1;
        }
    }
}
//Post Frame
.post-frame {
    padding: 2rem 2rem 0;
    box-shadow: 0px 5px 20px 3px rgba(0, 0, 0, 0.05);
    .post-details {
        padding: 2.2rem 0 2rem;
    }
    // .post-title {
    //     letter-spacing: 0;
    // }
    .post-content {
        margin-bottom: 1.8rem;
        line-height: 1.72;
    }
}
// فیلتر
ul.blog-filters {
    padding-top: 4rem;
    margin-bottom: 2rem;
    text-transform: uppercase;
    font-weight: 700;
    color: #333;
    border-top: 1px solid $border-color;
    li {
        margin-right: 2rem;
        margin-left: 2rem;
    }
    .nav-filter {
        letter-spacing: 0;
    }
}
.blog-filters {
    span {
        margin-left: .8rem;
        color: #aaa;
    }
    li {
        padding: 0;
        margin-right: .8rem;
        line-height: 2.143;
        border-bottom: 2px solid transparent;
    }
    a.nav-filter {
        border-width: 2px;
    }
}
.post-outer {
    .post-details {
        position: relative;
        background-color: $bg-secondary-white-color;
        width: calc(100% - 100px);
        min-width: 75%;
        margin-left: auto;
        margin-right: auto;
        border-radius: 1rem;
        text-align: center;
        transform: translateY(-50%);
        @include print_css(post, detail);
        .btn {
            font-size: 1.4rem;
            font-weight: 600;
            letter-spacing: -.025em;
        }
    }
    .post-meta {
        color: $dark-color;
    }
    .post-title {
        letter-spacing: -.025em;
        margin-bottom: 1.8rem;
    }
}
//blog-centerzoom-carousel
.owl-carousel.blog-centerzoom-carousel { 
    .post {
        position: relative;
    }
    .post-details {
        background-color: #1d1d1d;
    }
    .btn {
        font-size: 13px;
    }
    .owl-nav {
        .owl-prev {
            top: 80.5%;
            left: 10.5%;
        }
        .owl-next {
            top: 80.5%;
            right: 10.5%;
        }
    }
    @include mq(lg) {
        .owl-item {
            padding-bottom: 300px;
        }
        .post-details {
            position: absolute;
        }
        .owl-item{
            .post-media {
                transform: scale(.75);
                transition: transform .2s; 
                transform-origin: top center;        
            }
            .post-details {
                width: 75%;
                top: 75%;
                transition: width .2s, top .2s , left .2s;
            }
        }
        .center {  
            .post-media {
                transform: scale(1.5);
            }
            .post-details {
                top: 150%;
                width: 150%;
                left: -25%;
            }
        }
        .owl-item:not(.active) + .owl-item:not(.active),
        .active + .owl-item:not(.active) {
            .post-media {
                transform-origin: top left;
            }
        }
        .owl-item:not(.active) + .active{
            .post-media {
                transform-origin: top left;
            }
        }    
        .center + .active{
            .post-media {
                transform-origin: top right;
            }
            .post-details {
                left: 25%;
            }
        }
    }
}