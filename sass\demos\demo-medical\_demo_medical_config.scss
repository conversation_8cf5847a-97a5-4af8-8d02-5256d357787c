/* 
Demo 4
*/
@include set(
    (
        header: (
            top: (
                border-bottom: 1px solid #e1e1e1,
                font-size: 1.3rem,
                _links-gap: 2rem,
                color: rgba(34, 34, 34, 0.8),
                wishlist: (
                    margin-right: 0,
                    icon: (
                        font-size: 1.5rem,
                        margin-right: .9rem
                    )
                ),
                login: (
                    icon: (
                        margin-right: .9rem,
                    )
                )
            ),
            middle: (
                padding-top: 2.5rem,
                padding-bottom: 2.5rem
            ),
            contact: (
                icon: (
                    font-size: 1.6rem,
                )
            ),
            help: (
                icon: (
                    font-size: 1.6rem,
                    margin-left: 3px
                )
            ),
            call: (
                label: (
                    _gap: 0,
                    font-size: false,
                    font-weight: inherit,
                    line-height: false,
                    text-transform: capitalize,
                    margin-right: .3rem
                ),
                icon: (
                    margin: 0 .7rem 0 0,
                    font-size: 1.5rem
                )
            )
        ),
        menu: (
            ancestor: (
                _gap: 3rem,
                padding: 1.6rem 0,
                font-family: false,
                font-size: 14px,
                font-weight: 700,
                letter-spacing: inherit,
                line-height: 1.2,
                text-transform: capitalize,
                color: false,
                _active-color: false,
            ),
        ),
        base: (
            title: (
                display: block,
                font-size: 3rem,
                font-weight: 700,
                line-height: 1.2,
                letter-spacing: -.05em,
                margin-bottom: 0,
                color: #2
                text-transform: none
            )
        ),
        product: (
            list-sm: (
                name: (
                    -webkit-line-clamp: 1
                )
            )
        ),
        post: (
            meta: (
                margin-bottom: .5rem
            ),
            title: (
                margin-bottom: .5rem,
                line-height: 1.5
            ),
            content: (
                _row-count: 2,
            ),
            calendar: (
                background: #363737,
                color: #fff
            ),
            btn: (
                _icon-gap: .5rem
            )
        ),
        widget: (
            title: (
                border-bottom: 1px solid $border-color-light
            )
        ),
        footer: (
            top: (
                padding: 3.5rem 0 3.1rem,
                border-bottom: false
            ),
            middle: (
                border-bottom: false,
                padding: 75px 0 26px,
                widget: (    
                    title: (
                        text-transform: initial,
                        padding: 0,
                        font-size: 1.6rem,
                        color: #fff,
                        font-family: $font-family,
                        letter-spacing: 0,
                        font-weight: 600,
                        margin-bottom: 0,
                        line-height: 1.2
                    ),
                    label: (
                        color: #777,
                        font-family: $font-family,
                        margin-bottom: .8rem,
                        font-weight: 700,
                        letter-spacing: 0,
                        line-height: 1.2
                    ),
                    body: (
                        font-size: 1.3rem,
                        color: #a6a6a6,
                        font-family: $font-family,
                    ),
                    list-item: (
                        display: flex,
                        flex-direction: column,
                        color: #fff,
                        margin-bottom: 15px,
                        letter-spacing: normal,
                    )
                )
            ),
            newsletter: (
                title: (
                    margin-bottom: 0,
                    font-size: 1.6rem,
                    letter-spacing: 0,
                    line-height: 1.2
                ),
                desc: (
                    margin-bottom: 2rem,
                    font-size: 1.3rem,
                    letter-spacing: normal,
                    line-height: 1.86,
                    color: #a6a6a6,
                )
            ),
            bottom: (
                padding: 3.3rem 0 3.2rem 0,
                border-top: 1px solid #333,
            ),
            social-link: (
                display: flex,
                align-items: center,
                justify-content: center,
                width: 4rem,
                height: 4rem,
                font-size: 1.6rem,
                color: #999,
                border-color: #666,
                margin-right: 8px
            ),
            copyright: (
                font-weight: 400,
                color: #666,
                letter-spacing: normal
            ),
        )
    )
);