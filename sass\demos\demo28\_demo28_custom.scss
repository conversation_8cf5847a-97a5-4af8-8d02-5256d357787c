/* 
Demo 28
*/
/* Header */
.header-middle {
    .header-left  {
        flex: none;
    }
    .header-right {
        flex: none;
    }
    .header-center {
        flex: 1;
    }
    .nav.nav-tabs {
        border-right: 1px solid #353535;
        border-bottom: 1px solid #353535;
        .nav-menu {
            border: none;
        }
        .nav-item {
            margin-right: 0;
        }
        .nav-link {
            padding: 36.5px 20px 36.5px 20px;
            font-size: 1.4rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0px;
            background-color: #232323;
            color: #fff;
            &.active {
                background-color: #353535;
                color: #fff;
            }
        }
    }
    .icon-box {
        margin-right: 2rem;
        .icon-box-icon, p {
            color: #fff;
        }
        .icon-box-title {
            color: #aaa;
        }
        &:hover {
            .icon-box-title, .icon-box-icon, p {
                color: $primary-color;
            }
        }
    }
    .divider {
        background-color: #353535;
        margin-right: 2rem;
    }
    .wishlist, .cart-toggle {
        color: #fff;
        &:hover {
            color: $primary-color;
        }
    }
}
.mobile-menu-toggle {
    color: #fff;
}
.header-search.hs-simple {
    margin-right: 18px;
    input.form-control {
        border-color: transparent;
    }
    .btn-search i {
        font-size: 1.8rem;
        &::before {
            font-weight: 900;
        }
    }
}
.header-bottom {
    .tab-content {
        background-color: transparent;
    }
    .menu {
        > li {
            margin-right: 0;
        }
    }
}
.header .search-toggle i {
    margin: -5px 2px 0 3px;
    font-size: 2.4rem;
    color: #444;
    transition: color .3s;
}
.header-search:hover .search-toggle i { color: $primary-color; }
.header .login { line-height: 1; }
.cart-dropdown:hover .minicart-icon { color: $primary-color; }
.header-middle .main-nav { margin: 0 2rem; font-size: 1.4rem; }
.header-bottom .menu > li > a { 
    &:hover, &:focus {
        opacity: 1;
        color: #fff;
    }
}
.header-bottom .menu, .header-bottom .header-center { overflow-x: auto; }
.header-bottom .menu::-webkit-scrollbar { height: 8px; }
.header-bottom .menu::-webkit-scrollbar-thumb { background: #6c6a6a; border-radius: 10px; }
.header-bottom .menu::-webkit-scrollbar-track { background: #323232; border-radius: 10px; width: 100%; }
@include mq('xl', 'max') {
    .has-center .header-center {
        margin-left: 0rem;
        margin-right: 0rem;
    }
}
@media (min-width: 992px) and (max-width: 1093px) {
    .header-middle .header-left  {
        flex: 0 0 35%;
        max-width: 35%;
    }
}
@include mq('lg', 'max') {
    .header-middle {
        padding: 20px 0; 
        .nav.nav-tabs, .header-bottom {
            display: none;
        }
    }
}
/* Top Banner */
.intro-banner {
    padding: 138px 0 136px;
    .banner-title {
        max-width: 50rem;
        padding-right: 3px;
        font-size: 5rem;
        font-weight: 800;
        line-height: 1;
        text-indent: -10px;
        margin-bottom: 12px;
    }
    p { font-size: 1.6rem; }
    .btn:hover, .btn:active, .btn:focus {
        background: $dark-color;
        border-color: $dark-color;
    }
}
/* Creative Grid */
.creative-grid-section {
    .category-name {
        margin-bottom: 8px;
        letter-spacing: normal;
    }
    .category img {
        border-radius: 0;
    }
    .category-banner:not(.text-white) .category-count {
        color: #999;
    }
    .height-1 { height: 21.5rem; }
    .height-2 { height: 41rem; }
}
/* Icon Boxes */
.icon-boxes-section {
    .icon-box-icon { 
        font-size: 3.2rem;
        margin-bottom: 0px; 
    }
    i {
        width: 52px;
        line-height: 52px;
    }
    .icon-box-title {
        margin-bottom: 0;
        font-size: 1.5rem;
        line-height: 1em;
        letter-spacing: 0.15px;
        text-transform: none;
    }
    .icon-box p {
        font-size: 1.4rem;
        line-height: 1.3;
        color: #999;
    }
}
/* Category Banner Section */
.category-banner-section {
    .category-media { 
        margin-bottom: 2rem; 
        overflow: hidden;
        img {
            border-radius: 0;
        }
    }
    .category-name {
        font-size: 2rem;
        color: $secondary-color;
        text-transform: none;
        
    }
    .btn-link {
        color: #999;
        font-weight: 400;
        letter-spacing: 0;
        text-transform: none;
    }
}
/* Instagram Section */
.instagram a::before { background: #e82d7c; }
/* Instagram */
.instagram {
    border-radius: 0;
    a {
        &::after {
            transform: translate(-50%, -50%) rotate(90deg) scale(2);
            transition: all .3s;
        }
    }
    &:hover {
        a {
            &::after {
                transform: translate(-50%, -50%) rotate(0deg) scale(1);
            }
        }
    }
}
/* Footer */
.newsletter-form {
    padding: 0 6.1%;
}
.newsletter-form form { 
    height: 48px; 
}
.newsletter-form .form-control { 
    background: $white-color;
    font-style: italic;
    color: #999;
    border: none;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
}
.newsletter-form .btn {
    padding: 1em 1.9em;
    border-radius: 0 5px 5px 0;
}
.newsletter-form .icon-box-icon { font-size: 4.5rem; }
.newsletter-form .icon-box-title { 
    margin-bottom: 5px; 
    font-size: 2rem;
    text-transform: uppercase; 
    font-weight: 700;
    line-height: 0.9em;
    letter-spacing: 0.4px;
}
.newsletter-form .icon-box p { 
    font-size: 1.4rem;
    line-height: 1.2em;
    letter-spacing: -0.13px;
}
.footer-middle .widget:not(.widget-about) { margin-top: .4rem; }
.widget-about p { letter-spacing: inherit; }
.footer-bottom {
	&::after {
		content: '';
		display: block;
		position: absolute;
		top: 0;
		left: 50%;
		width: 100%;
		max-width: 1180px;
		height: 1px;
		background-color: #333;
		transform: translateX(-50%);
	}
}
/* Responsive */
@include mq('xl', 'max') {
    .header-bottom .header-center { margin: 0; }
    .header .login { margin-right: 1.5rem; }
    .menu>li>a {
        padding-left: .5rem;
        padding-right: 1rem;
    }
}
@include mq('lg') {
    .newsletter-form .icon-box { margin-right: 2.5rem; }
    .newsletter-form .icon-box-title, .newsletter-form .icon-box p { line-height: 1; }
}
@include mq('lg','max') {
    .header-bottom {
        display: none;
    }
    .footer-middle {
        padding: 6rem 0 2rem;
        .widget-about p {
            margin-bottom: 1.5rem;
        }
        br {
            display: none;
        }
    }
}
@include mq('md') {
    .intro-banner .banner-content { width: 59.9%; }
}
@include mq('sm') {
    .newsletter-form .icon-box-icon { margin-right: 2.5rem; }
}
@include mq('sm', 'max') {
    .intro-banner {
        .banner-title {
            font-size: 4rem;
        }
        .banner-subtitle {
            font-size: 2.5rem;
        }
    }
    .newsletter-form {
        .icon-box { display: block; }
        .icon-box-content { text-align: center; }
        form {
            display: block;
            height: auto;
            .form-control {
                border-top-right-radius: 5px;
                border-bottom-right-radius: 5px;
                margin-bottom: 1.5rem;
            }
            .btn {
                border-top-right-radius: 5px;
                border-bottom-right-radius: 5px;
            }
        }
    }
}
// Shop page
.shop-banner {
    padding: 7rem 1.5rem 6.3rem 9.1%;
    .banner-subtitle {
        margin-bottom: .7rem;
        font-size: 2em;
    }
    .banner-title {
        margin-bottom: 1.1rem;
        font-size: 3.6em;
    }
    p {
        margin-bottom: 2.7rem;
        font-size: 1.8em;
    }
}
// Product page
.single-product .product-form {
    .btn-cart {
        background-color: $secondary-color;
        &:hover:not(:disabled) {
            background-color: darken( $secondary-color, 7% );
        }
        &:disabled {
            background-color: lighten( $secondary-color, 10% );
        }
    }
}