﻿/* -------------------------------------------
    Products 
        - Default
        - Classic
        - Slideup
            - SlideUp-Content
            - SlideUp
        - Gallery
        - List
            - List
            - Small
        - Product فیلتر
---------------------------------------------- */
// Variables
@include set-default(
	(
		product: (
            font-family: "'Open Sans', sans-serif",
            font-size: 1.4rem,
            color: #999,
            body: (
                padding-top: 1.8rem,
                padding-bottom: 2rem
            ),
            label: (
                margin-bottom: .5rem,
                padding: .5rem 1.1rem,
                font-family: false,
                font-size: 1.1rem,
                border-radius: 2px,
                font-weight: 600,
                line-height: 1,
                letter-spacing: false,
                color: #fff,
                text-transform: uppercase,
                new: (
                    color: false,
                    background: $primary-color
                ),
                sale: (
                    color: false,
                    background: $secondary-color
                ),
                top: (
                    color: false,
                    background: $primary-color
                ),
                stock: (
                    color: false,
                    background: #999
                )
            ),
            دسته بندی: (
                margin-bottom: .4rem,
                font-family: false,
                font-size: 1.1rem,
                font-weight: 400,
                line-height: 1,
                letter-spacing: false,
                text-transform: uppercase,
                color: false
            ),
            name: (
                margin-bottom: .6rem,
                font-family: inherit,
                font-size: 1.4rem,
                font-weight: 400,
                line-height: false,
                letter-spacing: false,
                text-transform: false,
                color: #222
            ),
            price: (
                margin-bottom: .6rem,
                font-family: false,
                font-size: 1.5rem,
                font-weight: 600,
                line-height: false,
                letter-spacing: false,
                text-transform: false,
                color: #222
            ),
            rating: (
                margin-bottom: 1.5rem,
                font-size: 14px,
                _star-color: $secondary-color
            ),
            variation: (
                width: 2.6rem,
                height: 2.6rem,
                color: #2
            transition: .3s,
                _active-border: false,
                _active-box-shadow: 0 0 0 3px #999,
                _active-outline: #fff solid 2px,
                // custom types
                _color-border: 1px solid $border-color,
                _image-border: 0,
            ),
            list-sm: (
                name: (
                    color: #666,
                ),
                _image-width: 10rem
            )
        )
    )
);
.equal-height {
    .product {
        height: 100%;
    }
}
// Product Shadow
.product-shadow {
    transition: box-shadow .3s;
    &:hover {
        box-shadow: 0 15px 25px -20px rgba(0,0,0,0.5);
    }
    // .product-details {
    //     padding-left: .5rem;
    //     padding-right: .5rem;
    // }
}
// Product Label Round
.product-label-round {
    .product-label {
        padding: .5rem 1.1rem;
        border-radius: 1rem;
    }
}
.text-center {
    .product-details {
        padding-left: .5rem;
        padding-right: .5rem;
    }
}
// Default
.product-wrap {
    margin-bottom: 1rem;
}
.product {
    position: relative;
    @include print_css( product );
    transition: .3s;
    &:hover {
        .product-action,
        .product-action-vertical {
            visibility: visible;
            opacity: 1;
        }
    }
}
.product-media:hover {
    img:first-child { opacity: 0 }
    img:last-child { opacity: 1 }
}
// Product Media
.product-media {
    position: relative;
    margin-bottom: 0;
    img {
        display: block;
        width: 100%;
        height: auto;
        transition: all .5s;
    }
    img:last-child {
        position: absolute;
        opacity: 0;
        left: 0;
        right: 0;
        top: 0;
        left: 0;
        object-fit: cover;
    }
    img:first-child {
        position: relative;
        opacity: 1;
    }
    transition: box-shadow .3s;
}
// Product Label
.product-label-group {
    position: absolute;
    right: 2rem;
    top: 2rem;
    max-width: 10rem;
}
.product-label {
    display: inline-block;
    text-align: center;
    @include print_css( product, label );
    &.label-new {
        @include print_css( product, label, new );
    }
    &.label-sale {
        @include print_css( product, label, sale );
    }
    &.label-top {
        @include print_css( product, label, top );
    }
    &.label-stock {
        @include print_css( product, label, stock );
    }
}
// Product Icon Button
.btn-product-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3.6rem;
    height: 3.6rem;
    margin-bottom: .5rem;
    border: 1px solid #e1e1e1;
    border-radius: 50%;
    background-color: white;
    color: #999;
    font: {
        size: 1.8rem;
        weight: 700;
    }
    transition: border-color .3s, color .3s, background-color .3s;
    &.btn-cart i {
       margin-bottom: .2rem;
    }
    &:hover {
        border-color: $primary-color;
        color: #fff;
        background-color: $primary-color;
    }
}
// Product Button
.btn-product {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    padding: 1.14em 0;
    background-color: $primary-color;
    color: #fff;
    font: {
        size: 1.4rem;
        weight: 700;
    }
    line-height: 1.2;
    letter-spacing: -.025em;
    text-transform: uppercase;
    transition: opacity .3s;
    &:hover {
        color: #fff;
    }
}
// QuickView Button
.btn-quickview {
    opacity: .8;
    transition: opacity .3s;
    &:hover {
        opacity: 1;
    }
    i {    
        //transform: translateX(1em);
        font: {
            size: 1.4rem;
            weight: 700;
        }
    }
}
// WishList Button
.btn-wishlist {
    line-height: 1;
    i::before {
        margin: 0;
    }
}
// Product Action Vertical Container
.product-action-vertical {
    position: absolute;
    top: 15px;
    left: 15px;
    transition: opacity .3s, visibility .3s;
    opacity: 0;
    visibility: hidden;
}
// Product Action Container
.product-action {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    background-color: transparent;
    z-index: 10;
    transition: opacity .3s, visibility .3s;
    opacity: 0;
    visibility: hidden;
}
// Product Details
.product-details {
    position: relative;
    @include print_css( product, body );
    > .btn-wishlist {
        position: absolute;
        top: 1.5rem;
        right: 0;
        color: #999;
        z-index: 1;
        font: {
            size: 1.8rem;
        }
        i {
            display: inline-block;
            margin: 0;
            transition: transform .3s;
        }
        &:hover {
            i {
                transform: rotateY(180deg);
            }
            color: $primary-color;
        }
    }
    >:last-child {
        margin-bottom: 0;
    }
}
// Product Category
.product-cat {
    @include print_css( product, category );
    a {
       display: inline-block;
       &:hover {
           color: $primary-color;
       }
    }
}
// Product Name
.product-name {
    @include print_css( product, name );
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    a {
        // transition: color .3s;
        &:hover {
            color: $primary-color;
        }
    }
}
// Product Price
.product-price {
    @include print_css( product, price );
    .old-price {
        color: #ccc;
    }
    .new-price {
        margin-right: 1rem;
        text-decoration: none;
    }
}
// Product Rating
.ratings-container {
    display: flex;
    align-items: center;
    margin-left: 1px;
    line-height: 1;
    @include print_css( product, rating );
}
.ratings-full {
    cursor: pointer;
}
.ratings-full,
.ratings {
    position: relative;
    font-family: 'LineAwesome';
}
.ratings-full {
    margin-right: 1rem;
    &::before {
        content: "\f318 " "\f318 " "\f318 " "\f318 " "\f318";
        color: rgba(0, 0, 0, 0.16);
    }
    &:hover {
        .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
    }
}
.ratings {
    position: absolute;
    top: 0;
    left: 0;
    white-space: nowrap;
    overflow: hidden;
    &::before {
        content: "\f318 " "\f318 " "\f318 " "\f318 " "\f318";
        @include css( color, product, rating, _star-color );
    }
}
.rating-reviews {
    margin: 2px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 1.1rem;
    color: #ccc;
    letter-spacing: 0;
    text-transform: uppercase;
    &:hover {
        color: $primary-color;
    }
}
// Product Variables
.product-variations {
    display: flex;
    // issue element-products
    .ratings-container + &{ 
        margin-top: -5px;
    }
    > a:not(.size-guide) {
        display: inline-block;
        position: relative;
		margin: 3px 5px 3px 0;
        padding: 0 5px;
        border-radius: 2px;
		background-color: transparent;
		background-size: cover;
		font-size: 1.3rem;
		font-family: inherit;
		text-align: center; // issue: if use only button, no need
		vertical-align: middle;
		cursor: pointer;
		@include print_css( product, variation );
        @include css(line-height, product-single, variation, height);
		&:last-child {
			margin-right: 0;
        }
        border: 1px solid $border-color;
    }
    .color:not(.active) {
        border: none;
    }
	> a.active,
	> a:not(.size-guide):hover {
		border: 1px solid;
		box-shadow: inset 0 0 0 2px #fff;
	}
}
// Product Details
.product-details,
.product-hide-details {
    .product-action {
        position: relative;
        visibility: visible;
        opacity: 1;
        .btn-wishlist {
            position: static;
        }
    }
    .btn-cart {
        flex: 1;
        min-width: 9.5rem;
        max-width: 12.3rem;
        height: 4rem;
        padding: .97em 0;
        font-size: 1.3rem;
        transition: color .3s, background-color .3s, border-color .3s;
        z-index: 1;
    }
    .btn-quickview {
        margin: 0 0 0 .8rem;
    }
    .btn-product-icon {
        width: 4rem;
        height: 4rem;
        border: 0;
        border-radius: 0;
        background-color: #eee;
        color: #222;
        transition: color .3s, background-color .3s, visibility .3s;
        i {
           font-size: 1.8rem;
        }
        &:hover {
            color: #fff;
            background-color: $primary-color;
        }
    }
    .btn-cart {
        color: #fff;
        background-color: #444;
        &:hover {
            color: #fff;
            background-color: $primary-color;
        }
    }
}
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .product-hide-details,
    .product-details {
        .btn-cart {
            flex: 0 0 12.3rem;
        }
    }
    @include mq( sm,  max ) {
        .product-hide-details,
        .product-details {
            .btn-cart {
                flex: 0 0 9.5rem;
            }
        }
    } 
}
// Product Classic
.product-classic {
    .btn-quickview {
        opacity: 1;
    }
    .btn-wishlist {
        margin: 0 0 0 .8rem;
    }
    .btn-wishlist,
    .btn-quickview {
        transform: translateX(-200%);
        opacity: 0;
        visibility: hidden;
        transition: transform .3s, opacity .3s, visibility .3s, color .3s, background-color .3s, border-color .3s;
    }
    .btn-quickview {
        transition: transform .5s, opacity .5s, visibility .5s, color .3s, background-color .3s, border-color .3s;
    }
    .btn-cart {
        color: #222;
        background-color: #eee;
    }
    &:hover {
        .btn-wishlist,
        .btn-quickview {
            transform: translateX(0);
            opacity: 1;
            visibility: visible;
        }
        .btn-cart {
            color: #fff;
            background-color: #444;
            &:hover {
                color: #fff;
                background-color: $primary-color;
            }
        }
    }
    .product-details {
        padding-left: 0;
    }
}
// Slideup Content
.product-slideup-content {
    overflow: hidden;
    .product-hide-details {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        transition: transform .3s;
        background-color: #fff;
    }
    .btn-wishlist {
        position: static;
        margin: 0 .8rem 0 0;
    }
    .product-details {
        padding-bottom: 1px;
        background-color: #fff;
        transition: transform .3s;
    }
    .btn-wishlist,
    .btn-quickview {
        opacity: 0;
        visibility: hidden;
        transition: transform .4s, opacity .3s, visibility .3s, color .3s, background-color .3s;
    }
    .btn-quickview {
        transform: translateX(-200%);
    }
    .btn-wishlist {
        transform: translateX(200%);
    }
    &:hover {
        .btn-wishlist,
        .btn-quickview {
            transform: translateX(0);
            opacity: 1;
            visibility: visible;
        }
    }
}
// SlideUp
.product-slideup {
    .btn-wishlist {
        position: static;
        margin: 0 .8rem 0 0;
    }
    .product-details {
        padding-bottom: 0;
        background-color: #fff;
        transition: transform .3s;
    }
    .ratings-container {
        margin-bottom: 0;
    }
    .btn-wishlist,
    .btn-quickview {
        opacity: 0;
        visibility: hidden;
        transition: transform .4s, opacity .3s, visibility .3s, color .3s, background-color .3s;
    }
    .btn-quickview {
        transform: translateX(-200%);
    }
    .btn-wishlist {
        transform: translateX(200%);
    }
    .product-action {
        display: flex;
        position: absolute;
        padding-top: 1.5rem;
        top: 100%;
        bottom: auto;
        visibility: hidden;
        opacity: 0;
    }
    &:hover {
        transform: translateY(-58px);
        .btn-wishlist,
        .btn-quickview {
            transform: translateX(0);
            opacity: 1;
            visibility: visible;
        }
        .product-action {
            visibility: visible;
            opacity: 1;
        }
    }
}
// Gallery
.product-image-gap {
    padding: .9rem;
    border: 1px solid $border-color;
    background-color: #fff;
    .product-details {
        padding: 1.8rem 1rem 1rem;
    }
    .ratings-container {
        margin-bottom: 0;
    }
    &:hover {
        box-shadow: 0 5px 30px rgba(0,0,0,0.05);
    }
}
// List 
.product-list {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    .product-media {
        flex: 0 0 26rem;
        max-width: 26rem;
        margin: 0 2rem 0 0;
    }
    .product-details {
        padding: 0;
        flex: 1;
    }
    .product-label {
        font-size: 1.1rem;
    }
    .btn-quickview {
        opacity: 1;
    }
    .btn-wishlist {
        margin: 0 0 0 .8rem;
    }
    .btn-wishlist,
    .btn-quickview {
        transform: translateX(-200%);
        opacity: 0;
        visibility: hidden;
        transition: transform .3s, opacity .3s, visibility .3s, color .3s, background-color .3s;
    }
    .btn-quickview {
        transition: transform .5s, opacity .5s, visibility .5s, color .3s, background-color .3s;
    }
    .btn-cart {
        color: #222;
        background-color: #eee;
    }
    .ratings-container {
        margin-bottom: .8rem; 
    }
    .product-name {
        font: {
            size: 1.8rem;
            weight: 600;   
        }
        letter-spacing: -.025em;
    }
    .product-price {
        font: {
            size: 1.8rem;
            weight: 600;  
        }
    }
    .product-short-desc {
        @include text-block(3);
        margin-bottom: 1.3rem;
        text-transform: none;
        font-size: 1.4rem;
        line-height: 1.78;
        color: #666;
    }
    &:hover {
        .btn-wishlist,
        .btn-quickview {
            transform: translateX(0);
            opacity: 1;
            visibility: visible;
        }
        .btn-cart {
            color: #fff;
            background-color: #444;
            &:hover {
                color: #fff;
                background-color: $primary-color;
            }
        }
    }
}
// List 
.product-list-sm {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    .product-media {
        flex: 0 0 get(product, list-sm, _image-width);
        @include css(max-width, product, list-sm, _image-width);
        margin: 0 0 0 1rem;
    }
    .product-details {
        padding: 0;
    }
    .product-name {
        @include text-block();
        margin-bottom: .7rem;
        white-space: normal;
        @include print_css( product, list-sm, name);
        &:hover {
            color: $primary-color;
        }
    }
    .ratings-container {
        margin: 0;
    }
    .product-price {
        font-size: 1.4rem;
    }
    & + .product-list {
        margin-top: 2rem;
    }
    .btn-cart {
        margin-top: 2rem;
        height: 3.3rem;
        max-width: 11.7rem;
        border: 1px solid #d7d7d7;
        color: $secondary-color;
        background: transparent;
        font-size: 1.3rem;
        &:hover {
            border-color: $secondary-color;
            background: $secondary-color;
        }
    }
}
// Other Styles
.product {
    &.text-center {
        .product-cat {
            padding: 0;
        }
        .product-price,
        .product-variations,
        .ratings-container,
        .product-action {
            justify-content: center;
        }
    }
    &.shadow-media:hover {
        .product-media {
            box-shadow: 0px 20px 20px -16px rgba(0,0,0,0.5); 
        }
    }
}
@include mq(sm, max) {
    .product-list {
        display: block;
        .product-media {
            margin: 0;
            max-width: 100%;
        }
        .product-details {
            padding: 1.8rem 0 2rem;
        }
    }
    .product-action-vertical {
        top: 10px;
        left: 10px;
    }
}
@include mq(xs, max) {
    .product-details,
    .product-hide-details {
        .btn-cart {
            height: 3.6rem;
            padding: .85em 0;
        }
    }
    .product-action {
        .btn-wishlist,
        .btn-quickview {
            max-height: 3.6rem;
            padding: 1rem 0;
        }
    }
}
@include mq(375px, max) {
    .product-details,
    .product-hide-details {
        .btn-quickview {
            margin-left: 2px;
        }
        .btn-wishlist {
            margin-left: 2px;
        }
    }
    .product-slideup-content,
    .product-slideup {
        .btn-wishlist {
            margin-right: 2px;
        }
    }
}
// Product فیلتر
.product-filters {
    margin-right: 1.5rem;
}
