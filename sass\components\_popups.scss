/* -------------------------------------------
    Popup
        - Popup
        - Popup Close
        - Popup Arrow
        - Popup Content
        - Popup - Login
        - Popup - Newsletter
        - Popup - Product
        - Popup - Image PhotoSwipe
        - Popup Zoom, Flip effect
        - Responsive
---------------------------------------------- */
// Popup
.mfp-wrap {
    z-index: 3000;
}
.mfp-bg {
    z-index: 3000;
    background: #000;
    opacity: 0;
    transition: opacity .3s ease-out;
    &.mfp-ready {
        opacity: .7;
    }
    &.mfp-removing {
        opacity: 0;
    }
}
.mfp-container {
    padding: 4rem 2rem;
    video {
        width: 100%;
        height: 100%;
    }
}
// Popup Close
.mfp-close {
    transform: rotateZ(45deg);
    transition: transform .3s;
    width: 18px;
    height: 18px;
    opacity: 1;
    .mfp-wrap & {
        top: 20px;
        right: 20px;
    }
    .mfp-content & {
        top: -25px;
        right: 0;
    }
    .mfp-image-holder &,
    .mfp-iframe-holder & {
        top: 15px;
        right: 0;
    }
    // .mfp-removing &, // issue
    span {
        display: none;
    }
    &::before,
    &::after {
        content: '';
        display: block;
        position: absolute;
        background-color: #fff;
        width: 2px;
        height: 100%;
        top: 0;
        left: calc(50% - 1px);
    }
    &::before {
        width: 100%;
        height: 2px;
        top: calc(50% - 1px);
        left: 0;
    }
    &:hover, &:focus {
        transform: rotateZ(135deg) scale(1.5);
    }
}
// Popup Arrow
.pswp__button--arrow--left ,
.pswp__button--arrow--right,
.mfp-arrow {
    width: 4.8rem;
    height: 4.8rem;
    color: #fff;
    border-radius: 50%;
    font-size: 2.4rem;
    font-weight: 900;
    font-family: "riode";
    text-align: center;
    opacity: .5;
    right: 10%;
    transition: opacity .3s;
    border: 2px solid;
}
.pswp__ui > button {
    &::before {
        background: transparent;
        position: static;
        line-height: 44px;
    }
    &.pswp__button--arrow--left::before {
        margin-right: 3px;
    }
    &.pswp__button--arrow--right::before {
        margin-left: 3px;
    }
    &:hover {
        opacity: 1;
    }
}
.pswp__button--arrow--right::before {
    content: "\e950";
    font-family: 'riode';
}
.mfp-arrow::before,
.mfp-arrow::after {
    content: "\f054";
    position: static;
    display: inline;
    margin: 0;
    border: 0;
}
.mfp-arrow::after {
    content: none;
}
button.mfp-arrow {
    border: 2px solid;
}
.pswp__button--arrow--left ,
.mfp-arrow-left  {
    left: 10%;
    &::before {
        content: "\e951";
        font-family: 'riode';
    }
}
// Popup Content
.mfp-content > * {
    position: relative;
    margin: auto;
    background-color: $bg-secondary-white-color;
    box-shadow: 5px 5px 20px rgba(0,0,0,.1);
    opacity: 0;
    .mfp-ready & {
        opacity: 1;
    }
    .mfp-removing & {
        opacity: 0;
    }
}
// Popup - Login
.login-popup {
    max-width: 490px;
    padding: 4rem;
    border-radius: .3rem;
    .tab {
        font-size: 1.8rem;
        color: $light-color;
        .nav-item {
            flex: none;
        }
    }
    .nav-item .nav-link {
        padding: 0 0 1px;
        color: $grey-color;
        font-size: 1.8rem;
        font-weight: 700;
        letter-spacing: -.025em;
        line-height: 2.43;
        transition: font-size .3s, color .3s;
        &.active {
            font-size: 3rem;
        }
    }
    // .nav-item {
    //     margin-bottom: -1px;
    //     &:not(:last-child) {
    //         margin-right: 1px;
    //     }
    //     &.show .nav-link,
    //     .nav-link.active {
    //         color: $dark-color;;
    //         border-bottom-color: transparent;
    //     }    
    // }
    .delimiter {
        margin: 0 .8rem;
    }
    .tab-pane {
        padding: 0;
    }
    form {
        margin-bottom: 2.2rem;
    }
    .form-group {
        margin-bottom: 1.8rem;
        label {
            display: inline-block;
            margin-bottom: 8px;
            font: {
                size: 1.3rem;
            }
            line-height: 1;
        }
        .form-control {
            padding: 1.2rem 1.5rem 1.1rem;
            height: 3.7rem;
            font-size: 1.3rem;
            color: $grey-color;
            border-radius: .3rem;
            border-color: $border-color;
        }
    }
    .form-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        margin-bottom: 2.3rem;
        font-size: 1.3rem;
    }
    .custom-checkbox + label {
        padding-left: 2.6rem;
    }
    .btn {
        height: 4.7rem;
        padding: 0;
        font: {
            size: 1.3rem;
        }
    }
    .lost-link {
        text-align: right;
        color: $grey-color;
        font-size: 1.3rem;
        &:hover {
            color: $primary-color;
        }
    }
    .form-choice label {
        display: flex;
        align-items: center;
        margin-bottom: 1.3rem;
        font-size: 1.3rem;
        color: $body-color;
        &::after, &::before {
            content: '';
            height: 1px;
            flex: 1;
            background: $border-color;
        }
        &::after {
            margin-left: 3rem;
        }
        &::before {
            margin-right: 3rem;
        }
    }
    .social-link {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 3.6rem;
        height: 3.6rem;
        font-size: 1.4rem;
        border-radius: .3rem;
        color: $white-color;
        &.social-google {
            background-color: #db402c;
        }
        &.social-facebook {
            background-color: #3b5998;
        }
        &.social-twitter {
            background-color: #1ab2e8;
        }
    }
}
.container > .login-popup {
    margin: 0 auto;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}
// Popup - Newsletter
.newsletter-popup {
    text-align: center;
    background-size: cover;
    border-radius: 1rem;
    .btn {
        min-width: auto;
        padding: .5em 1.5em;
        font-size: 1.3rem;
    }
    p {
        max-width: 100%;
        margin: 0 .2rem 2.5rem;
        font-size: 1.4rem;
        line-height: 1.7;
    }
    label {
        display: inline;
        font-size: 1.3rem;
        &::before {
            border-color: $body-color;
        }
    }
    .email {
        border: 0;
        color: $grey-color;
        background: $lighter-color;
    }
    .form-control {
        border: 1px solid $dark-color;
        background-color: $bg-secondary-white-color;
        border-left: none;
    }
    .input-wrapper-inline {
        margin-bottom: 2.6rem;
        height: 4.6rem;
        .form-control {
            margin: 0;
            padding-left: 1.2rem;
            padding-right: 1.2rem;
            border-radius: .3rem 0 0 .3rem;
        }
        .btn {
            margin: 0;
            border-radius: 0 .3rem .3rem 0; 
        }
    }
    .custom-checkbox + label {
        padding-left: 2.6rem;
    }
}
.newsletter-pop1 {
    display: flex;
    max-width: 72rem;
    background-position: 60% center;
    h4 {
        margin-bottom: .6rem;
        font-size: 2rem;
        font-weight: 800;
        line-height: 1.2;
    }
    h2 {
        margin-bottom: .7rem;
        font-size: 3.2rem;
        line-height: 1.2;
        letter-spacing: -.04375em;
        span {
            font-weight: 800;
        }
    }
    .newsletter-content {
        margin-left: auto;
        max-width: 41.4rem;
        padding: 7.1rem 4.2rem 5rem;
    }
    @include mq(md, max) {
        .newsletter-content {
            max-width: 36rem;
            padding-left: 2rem;
            padding-right: 2rem;
        }
        .login-popup { // Login Popup
            max-width: 500px;
            padding: 3.5rem 2rem;
        }
    }
    @include mq(sm, max) {
        background-image: none !important;
        .newsletter-content {
            margin-left: auto;
            margin-right: auto;
        }
    }
}
//newsletter-pop2
.newsletter-pop2 {
    max-width: 50rem;
    img {
        border-radius: 1rem 1rem 0 0;
        min-height: 200px;
        object-fit: cover;
    }
    h2 {
        margin: 2.5rem 0 .5rem;
        font-size: 24px;
        line-height: 1.25;
    }
    p {
        font-size: 14px;
        line-height: 1.7;
    }
    .input-wrapper-inline {
        max-width: 38rem;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 17px;
    }
    .social-links {
        margin: 1.7rem 0 3rem;
    }
    .social-link:not(:hover),
    .social-link:not(:hover):before {
        color: $grey-color;
    }
    .newsletter-content {
        padding: 0 2rem;
    }
}
//newsletter-pop3
.newsletter-pop3 {
    border-radius: 1rem;
    max-width: 54rem;
    padding: 5.4rem 4rem 2.8rem;
    h2 {
        font-size: 24px;
        line-height: 1.25;
        margin-bottom: 0;
    }
    p {
        margin: 8px 21px 24px;
        font-size: 14px;
        line-height: 1.7;
    }
    .input-wrapper-inline {
        flex-direction: column;
        align-items: center;
        height: unset;
        margin-bottom: 4.8rem;
        .btn {
            margin-top: 2rem;
            padding: 1.4rem 4.4rem;
            border-radius: .3rem;
        }
    }
    .form-control {
        border: 1px solid $border-color;
        max-width: 46rem;
        &::placeholder {
            text-align: center;
        }
    }
    .form-check {
        justify-content: space-between;
        align-items: center;
    }
    .form-privacy {
        text-decoration: underline;
    }
    @include mq(sm, max) {
        padding: 5.4rem 2rem 2.8rem;
        p {
            margin: 8px 5px 24px;
        }
        .form-check {
            flex-direction: column;   
        }
    }
}
//newsletter-pop4
.newsletter-pop4 {
    position: relative;
    border-radius: 100%;
    background: transparent;
    max-width: 580px;
    height: 580px;
    &::before {
        position: absolute;
        content: '';
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        border-radius: 100%;
        background-color: #191919;
        opacity: .6;
    }
    h4 {
        font-size: 26px;
        line-height: 1.15;
        margin-bottom: 0;
    }
    h2 {
        font-size: 36px;
        line-height: 1.5;
    }
    p {
        font-size: 14px;
        line-height: 1.7;
    }
    .input-wrapper-inline {        
        max-width: 40.6rem;
        margin-left: auto;
        margin-right: auto;
        .form-control {
            border-color: #fff;
            border-radius: 0 23px 23px 0;
        }
        .btn {
            border-radius: 23px 0 0 23px;
        }
    }
    @include mq('490px' , max) {
        padding: 0 2rem;
        &::before {
            border-radius: 100px;
        }
        h4 {
            margin-bottom: 2.5rem;
        }
    }
}
//newsletter-pop5
.newsletter-pop5 {
    position: relative;
    max-width: 50rem;
    >figure img {
        min-height: 57rem;
        object-fit: cover;
    }
    h2 {
        font-weight: 600;
        line-height: 1;
    }
    p {
        font-size: 14px;
        line-height: 1.7;
        margin-bottom: 23px;
    }
    .newsletter-content {
        top: 51%;
        left: 50%;
        max-width: 100%;
        transform: translateX(-50%);
    }
    .form-control { 
        border-color: $border-color;
    }
    .input-wrapper-inline {
        width: 38rem;
        max-width: 100%;
        margin-bottom: 18px;
    }
    .form-checkbox {
        margin-bottom: 26px;
    }
    .social-links {
        position: relative;
        &::before,
        &::after {
            position: absolute;
            content: '';
            top: 50%;
            border-top: 1px solid $border-color;
            width: 10.6rem;
        }
        &:before {
            left: -100%;
        }
        &::after {
            left: -100%;
        }
        .social-link {
            width: 36px;
            height: 36px;
            line-height: 36px;
            border: none;
            border-radius: 3px;
            color: $white-color;
        }
        .fa-google {
            background-color: #db402c;
        }  
        .social-link.social-linkedin:hover {
            background: #db552c;
            border-color: #db552c;
        } 
        .fa-facebook-f {
            background-color: #3b5998;
        }
        .fa-twitter {
            background-color: #1ab2e8;
        }
    }
    @include mq(sm , max) {
        .social-links {
            &::after,
            &:before {
                display: none;
            }
        }
        h2 {
            line-height: 1.1;
        }
        .newsletter-content { 
            top: 45%;
            padding: 0 2rem;
        }
    }
}
// Popup - Product
.mfp-product {
    .mfp-container {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 4rem 2rem 4rem;
    }
    .mfp-content {
        max-width: 98.8rem;
        margin: auto;
    }
    .product {
        background-color: $bg-secondary-white-color;
        margin-bottom: 2rem;
        padding: 3rem 1.5rem;
        border-radius: 1rem;
        // overflow: hidden;
    }
    .product-single .product-details {
        padding: 0;
    }
    .product-details {
        position: absolute;
        overflow-y: auto;
        height: 100%;
        top: 0;
    }
    .mfp-close {
        position: absolute;
        top: -2.7rem;
        right: 0;
    }
    .owl-nav {
        .owl-next, .owl-prev {
            width: 3.6rem;
            height: 3.6rem;
            font-size: 3rem;
        }
        .owl-next {
            right: 1rem;
        }
        .owl-prev {
            left: 1rem;
        }
    }
}
//dark-theme
.dark-theme {
    .mfp-content {
        .product-divider {
            border-color: $border-color;
        }
        .product {
            background-color: $primary-color-dark;
        }
    }
    .newsletter-popup {
        label,
        .form-privacy {
            color: #999;
        }
    }
}
// Popup - Image PhotoSwipe
.pswp__bg {
    background-color: rgba(0,0,0,.7);
}
.pswp__img--placeholder--blank {
    background-color: #F2F3F5;
}
.pswp__ui--fit .pswp__caption, .pswp__ui--fit .pswp__top-bar {
    background-color: transparent;
}
.pswp__caption__center {
    text-align: center;
}
// Popup Zoom, Flip effect
.mfp-ready.mfp-zoom-popup .mfp-content{
    transform: scale(1); 
}
.mfp-zoom-popup .mfp-content, .mfp-removing.mfp-zoom-popup .mfp-content {
    transition: .2s ease-in-out; 
    transform: scale(0.8); 
}
.mfp-ready.mfp-flip-popup .mfp-content {
    transform: translateY(0) perspective( 600px ) rotateX( 0 ); 
}
.mfp-flip-popup .mfp-content, .mfp-removing.mfp-flip-popup .mfp-content {
    transition: .3s ease-out;
    transform: translateY(-20px) perspective( 600px ) rotateX( 20deg );
}
// Popup Video
.mfp-content video {
    width: 100%;
    height: 100%;
}
.mfp-video-popup .mfp-content { max-width: 1000px; }
.mfp-video-popup {
    z-index: 10000;
}
// Popup Img
.mfp-img-popup .mfp-content {
    width: unset;
}
// Responsive
@include mq(md, max) {
    .mfp-product .product {
        padding: 2rem 1rem;
    }
    .mfp-arrow {
        color: #444;
    }
    .mfp-product .product-details {
        position: relative;
    }
}
@include mq(md) {
    .mfp-product {
        .row {
            margin: 0 -1.5rem;
            > .col-md-6 {
                padding-left: 1.5rem;
                padding-right: 1.5rem;
            }
        }
        .product-gallery {
            margin-bottom: 0;
        }
        .pr-md-3 {
            padding-right: 1.5rem !important;
        }
    }
}