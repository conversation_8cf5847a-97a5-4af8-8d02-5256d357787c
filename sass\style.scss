﻿@charset 'UTF-8';
/*
1. config
2. mixins
3. plugins
4. base
5. components
6. header
7. footer
8. demo
*/
/* 1. config */
@import 'config/variables';
/* 2. mixins */
@import 'mixins/breakpoints'; 
@import 'mixins/core';
@import 'mixins/buttons';
/* 3. plugins */
@import 'components/slider';
@import 'components/nouislider';
/* 4. base */
@import 'base/base';
@import 'base/helper';
@import 'base/type';
@import 'base/layout';
@import 'base/grid';
@import 'base/spacing';
/* 5, components */
@import 'components/accordion';
@import 'components/alerts';
@import 'components/animation';
@import 'components/banners';
@import 'components/blog';
@import 'components/buttons';
@import 'components/categories';
@import 'components/comments';
@import 'components/counter';
@import 'components/elements';
@import 'components/font-icons';
@import 'components/forms';
@import 'components/icon-boxes';
@import 'components/icons';
@import 'components/instagram';
@import 'components/member';
@import 'components/minipopup';
@import 'components/overlay';
@import 'components/page-header';
@import 'components/pagination';
@import 'components/popups';
@import 'components/products';
@import 'components/product-single';
@import 'components/social-icons';
@import 'components/sidebar';
@import 'components/sidebar-shop';
@import 'components/store';
@import 'components/tabs';
@import 'components/testimonials';
@import 'components/tooltip';
@import 'components/titles';
@import 'components/vendor';
@import 'components/widgets';
@import 'components/hotspot';
/* 6. header */
@import 'base/header/header';
@import 'base/header/dropdown';
@import 'base/header/menu';
/* 7. footer */
@import 'base/footer/footer';
/* 8. Pages */
@import 'pages/about';  
@import 'pages/account';   
@import 'pages/buttons';
@import 'pages/categories';
@import 'pages/coming';
@import 'pages/contact';
@import 'pages/error';
@import 'pages/products';
@import 'pages/cta';
@import 'pages/titles';
@import 'pages/instagrams';
@import 'pages/blog';
@import 'pages/shop';
@import 'pages/product-single';
@import 'pages/post-single';
@import 'pages/animation';
@import 'pages/wishlist';
@import 'pages/cart';
@import 'pages/checkout';
@import 'pages/order';
@import 'pages/icons';
@import 'pages/faq';
@import 'pages/compare';
@import 'pages/countdown';
@import 'pages/counter';
@import 'pages/banner-effect';
@import 'pages/popcode';
@import 'pages/banner';
@import 'pages/floating';
@import 'pages/hotspot';
@import 'pages/video';
@import 'pages/svg-floating';
@import 'pages/slider';
@import 'pages/subcategory';
@import 'pages/creative-grid';
@import 'pages/breadcrumb';
@import 'pages/vendor';
@import 'pages/product-banner';
@import 'pages/products-single-product';
@import 'pages/product-grid';
@import 'pages/product-tab';
@import 'pages/image-box';