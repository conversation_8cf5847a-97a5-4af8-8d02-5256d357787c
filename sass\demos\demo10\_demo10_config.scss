/*
Demo 10 Variables
*/
@include set(
    (
        base: (
            title: (
                font-family: $second-font-family,
                text-transform: uppercase,
                font-size: 2rem,
                font-weight: 600,
                letter-spacing: 1.3px,
                line-height: 1.2,
            )
        ),
        header: (
            letter-spacing:  .085em,
            font-family: $second-font-family,
            border-bottom: 1px solid #393839,
            top: (
                font-size: 11px,
                font-weight: 400,
                background: transparent,
                color: #fff,
                letter-spacing: inherit,
                border-bottom: 1px solid #393839,
            ),
            middle: (
                padding-top: 0rem,
                padding-bottom: 0rem,
                font-size: 1.4rem,
                font-weight: 400,
                color: #fff,
                background: transparent,
                login: (
                    font-size: 2.5rem,
                    margin-right: 2.8rem,
                    icon: (
                        font-size: 2.5rem,
                    )
                )
            ),
            cart: (
                label: (
                    margin: 0 .7rem 0 0,
                    font-weight: inherit
                ),
                count: (
                    color: #fff,
                    hover: (
                        color: $primary-color
                    )
                ),
                icon: (
                    color: #fff,
                    hover: (
                        border-color: #fff,
                        background: $white-color
                    )
                )
            ),
            login: (
                label: (
                    font-weight: inherit
                )
            ),
            sticky: (
                padding-top: 0,
                padding-bottom: 1px,
                background-color: #383839
            ),
            mmenu-toggle: (
                color: #fff
            )
        ),
        menu: ( 
            ancestor: (
                letter-spacing: .7px,
                font-size: 1.4rem,
                _gap: 0,
                padding: 49.5px 18.5px,
                font-weight: 600,
                text-transform: uppercase,
                _active-color: #fff
            ),
            submenu: (
                font-family: $third-font-family,
                font-weight: 400
            ),
            megamenu: (
                title: (
                    letter-spacing: -.025em
                )
            )
        ),
        product: (
            rating: (
                _star-color: #777
            ),
            price: (
                color: #222
            ),
            body: (
                padding-top: 1.8rem
            ),
            label: (
                background-color: #333,
                sale: (
                    background-color: #333
                )
            )
        ),
        post: (
            calendar: (
                border: 2px solid #5f5f5f,
                day: (
                    margin-bottom: 0
                )
            ),
            title: (
                line-height: 1.75
            )
        ),
        footer: (
            font-family: $font-family,
            color: #666,
            middle: (
                padding: 4.7rem 0 0,
                widget: (
                    title: (
                        margin-bottom: 1.8rem,
                        margin-top: 2px,
                        color: #E1E1E1
                    ),
                    label: (
                        color: #e1e1e1,
                        font-weight: 600,
                    ),
                    body: (
                        padding: 0,
                        color: #999
                    ),
                    list-item: (
                        margin-bottom: 1.5rem
                    )
                ),
            ),
            bottom: (
                padding: 3.3rem 0 2.9rem
            ),
            copyright: (
                font-family: $font-family,
                letter-spacing: 0,
                color: #666
            ),
            social-link: (
                font-size: 1.4rem
            ),
            newsletter: (
                title: (
                    color: #e1e1e1
                )
            )
        )
    )
)  