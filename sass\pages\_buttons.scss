/* -------------------------------------------
    Element <PERSON><PERSON> Page
---------------------------------------------- */
.section-buttons {
    .row {
        justify-content: center;
        text-align: center;
    }
    &.background-section {
        padding: 7.5rem 0 5.5rem;
    }
    .btn {
        margin-bottom: 2rem;
    }   
}
.btn-wrapper {
    display: flex;
    align-items:  center;
    justify-content: center;
    flex-wrap: wrap;
    .btn {
        margin-left: 1rem;
        margin-right: 1rem;
    }
    .btn-block {
        max-width: 18rem;
    }
}
//Responsive
@include mq(lg) {
    .btn-wrapper {
        .btn {
            margin: 1.4rem 0;
            &:not(:last-child) {
                margin-right: 2rem;
            }
        }
    }
}