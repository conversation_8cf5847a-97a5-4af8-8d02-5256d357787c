/* -------------------------------------------
    Popup
        - Popup
        - Popup Close
        - Popup Arrow
        - Popup Content
        - Popup - Login
        - Popup - Newsletter
        - Popup - Product
        - Popup - Image PhotoSwipe
        - Popup Zoom, Flip effect
        - Responsive
---------------------------------------------- */
// Popup
.mfp-wrap {
    z-index: 9999;
}
.mfp-bg {
    z-index: 9999;
    background: #000;
    opacity: 0;
    transition: opacity .3s ease-out;
    &.mfp-ready {
        opacity: .7;
    }
    &.mfp-removing {
        opacity: 0;
    }
}
.mfp-container {
    padding: 4rem 2rem;
}
// Popup Close
.mfp-close {
    transform: rotateZ(45deg);
    transition: opacity .3s;
    width: 18px;
    height: 18px;
    .mfp-wrap & {
        top: 20px;
        right: 20px;
    }
    .mfp-content & {
        top: -25px;
        right: 0;
    }
    .mfp-image-holder &,
    .mfp-iframe-holder & {
        top: 15px;
        right: 0;
    }
    // .mfp-removing &, // issue
    span {
        display: none;
    }
    &::before,
    &::after {
        content: '';
        display: block;
        position: absolute;
        background: #ccc;
        width: 2px;
        height: 100%;
        top: 0;
        left: calc(50% - 1px);
    }
    &::before {
        width: 100%;
        height: 2px;
        top: calc(50% - 1px);
        left: 0;
    }
}
// Popup Arrow
.pswp__button--arrow--left ,
.pswp__button--arrow--right,
.mfp-arrow {
    width: 4.8rem;
    height: 4.8rem;
    color: #fff;
    border-radius: 50%;
    font-size: 1.6rem;
    font-weight: 900;
    font-family: 'Font Awesome 5 Free';
    text-align: center;
    opacity: .5;
    right: 10%;
    transition: opacity .3s;
    border: 2px solid;
}
.pswp__ui > button {
    &::before {
        background: transparent;
        position: static;
        line-height: 44px;
    }
    &:hover {
        opacity: 1;
    }
}
.pswp__button--arrow--right::before {
    content: "\f054";
}
.mfp-arrow::before,
.mfp-arrow::after {
    content: "\f054";
    position: static;
    display: inline;
    margin: 0;
    border: 0;
}
.mfp-arrow::after {
    content: none;
}
button.mfp-arrow {
    border: 2px solid;
}
.pswp__button--arrow--left ,
.mfp-arrow-left  {
    left: 10%;
    &::before {
        content: "\f053";
    }
}
// Popup Content
.mfp-content > * {
    position: relative;
    margin: auto;
    background: #fff;
    box-shadow: 5px 5px 20px rgba(0,0,0,.1);
    opacity: 0;
    .mfp-ready & {
        opacity: 1;
    }
    .mfp-removing & {
        opacity: 0;
    }
}
// Popup - Login
.login-popup {
    max-width: 500px;
    padding: 3.5rem 5rem 4.8rem;
    .nav-link {
        padding: 0 0 1px;
        color: #222;
        font-size: 1.6rem;
        font-weight: 700;
        letter-spacing: -.025em;
        line-height: 2.43;
    }
    .nav-item {
        margin-bottom: -1px;
        &:not(:last-child) {
            margin-right: 1px;
        }
        &.show .nav-link,
        .nav-link.active {
            color: #222;;
            border-color: $primary-color;
        }    
    }
    .tab-pane {
        padding-top: 2.8rem;
        padding-bottom: 0;
    }
    form {
        margin-bottom: 2.3rem;
    }
    .form-group {
        margin-bottom: 1.8rem;
        label {
            display: inline-block;
            margin-bottom: 8px;
            font: {
                size: 1.3rem;
                family: $second-font-family;
            }
            line-height: 1;
        }
        .form-control {
            height: 3.7rem;
        }
    }
    .form-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 2.5rem;
        font-size: 1.3rem;
    }
    .btn {
        height: 3.9rem;
        padding: 0;
        font: {
            size: 1.3rem;
        }
    }
    .lost-link {
        text-align: right;
        color: $secondary-color;
        &:hover {
            text-decoration: underline;
        }
    }
    .form-choice {
        label {
            display: block;
            margin-bottom: .3rem;
            font-size: 1.3rem;
            color: #999;
        }
    }
}
// Popup - Newsletter
.newsletter-popup {
    display: flex;
    max-width: 70rem;
    text-align: center;
    background-position: 60% center;
    background-size: cover;
    h4 {
        margin-bottom: .9rem;
        font-size: 1.6rem;
    }
    h2 {
        margin-bottom: 1.2rem;
        font-size: 2.8rem;
        line-height: 1.07;
        letter-spacing: -.025em;
    }
    b {
        display: block;
        margin-top: .3rem;
        color: #222;
        font-size: 3rem;
        letter-spacing: .025em;
    }
    .btn {
        min-width: auto;
        padding: .5em 1.7em;
        font-size: 1.3rem;
    }
    p {
        max-width: 100%;
        margin-bottom: 1.8rem;
        padding: 0 .5rem;
        font-size: 1.3rem;
        line-height: 2rem;
    }
    label {
        color: #999;
        font-size: 1.3rem;
    }
    .email {
        border: 0;
        color: #999;
        background: #f4f4f4;
    }
    .form-control {
        border: 1px solid #ccc;
        background: #fff;
        border-left: none;
    }
}
.newsletter-content {
    margin-left: auto;
    max-width: 38.8rem;
    padding: 6.6rem 4.2rem 6.4rem;
}
// Popup - Product
.mfp-product {
    .mfp-container {
        padding: 4rem 3rem 2rem;
    }
    .mfp-content {
        max-width: 92.3rem;
        margin: auto;
    }
    .product {
        background-color: #fff;
        padding: 3rem 2rem;
    }
    .mfp-close {
        position: absolute;
        top: -2.7rem;
        right: 0;
    }
}
// Popup - Image PhotoSwipe
.pswp__bg {
    background-color: rgba(0,0,0,.7);
}
.pswp__img--placeholder--blank {
    background-color: #F2F3F5;
}
.pswp__ui--fit .pswp__caption, .pswp__ui--fit .pswp__top-bar {
    background-color: transparent;
}
.pswp__caption__center {
    text-align: center;
}
// Popup Zoom, Flip effect
.mfp-ready.mfp-zoom-popup .mfp-content{
    transform: scale(1); 
}
.mfp-zoom-popup .mfp-content, .mfp-removing.mfp-zoom-popup .mfp-content {
    transition: .2s ease-in-out; 
    transform: scale(0.8); 
}
.mfp-ready.mfp-flip-popup .mfp-content {
    transform: translateY(0) perspective( 600px ) rotateX( 0 ); 
}
.mfp-flip-popup .mfp-content, .mfp-removing.mfp-flip-popup .mfp-content {
    transition: .3s ease-out;
    transform: translateY(-20px) perspective( 600px ) rotateX( 20deg );
}
// Responsive
@include mq(md, max) {
    .mfp-product .product {
        padding: 2rem 1rem;
    }
    .mfp-arrow {
        color: #444;
    }
}
@include mq(md) {
    .mfp-product {
        .product-gallery {
            margin-bottom: 0;
        }
        div.product-details {
            // max-height: 53rem;
            overflow-y: auto;
            padding-bottom: 0;
        }
    }
}
@include mq(md, max) {
    .mfp-product div.product-details {
        margin-top: 2rem;
    }
    .newsletter-content {
        max-width: 36rem;
        padding-left: 2rem;
        padding-right: 2rem;
    }
    .login-popup { // Login Popup
        max-width: 500px;
        padding: 3.5rem 2rem;
    }
}