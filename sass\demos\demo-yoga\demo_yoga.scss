@charset 'UTF-8';
/*
1. config
2. mixins
3. plugins
4. base
5. components
6. header
7. footer
8. pages
9. demo
*/
/* 1. config */
@import '../../config/variables';
// Color
$primary-color: #3dbca7;
$secondary-color: #ffa372;
//Font
$font-family: vazir, $alt-font-family;
/* 2. mixins */
@import '../../mixins/breakpoints';
@import '../../mixins/core';
@import '../../mixins/buttons';
// Demo Variables
@import 'demo_yoga_config';
/* 3. plugins */
@import '../../components/slider';
@import '../../components/nouislider';
/* 4. base */
@import '../../base/base';
@import '../../base/helper';
@import '../../base/type';
@import '../../base/layout';
@import '../../base/grid';
@import '../../base/spacing';
/* 5, components */
@import '../../components/animation';
@import '../../components/banners';
@import '../../components/buttons';
@import '../../components/blog';
@import '../../components/categories';
@import '../../components/font-icons';
@import '../../components/forms';
@import '../../components/icon-boxes';
@import '../../components/icons';
@import '../../components/overlay';
@import '../../components/comments';
@import '../../components/alerts';
@import '../../components/pagination';
@import '../../components/page-header';
@import '../../components/minipopup';
@import '../../components/popups';
@import '../../components/products';
@import '../../components/product-single';
@import '../../components/sidebar';
@import '../../components/sidebar-shop';
@import '../../components/social-icons';
@import '../../components/tabs';
@import '../../components/tooltip';
@import '../../components/titles';
@import '../../components/widgets';
@import '../../components/hotspot';
/* 6. header */
@import '../../base/header/header';
@import '../../base/header/dropdown';
@import '../../base/header/menu';
/* 7. footer */
@import '../../base/footer/footer';
/* 8. Pages */
@import '../../pages/shop';
@import '../../pages/product-single';
/* 9. Demos */
@import 'demo_yoga_custom';