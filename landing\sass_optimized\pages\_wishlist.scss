/* -------------------------------------------
    Wishlist Page
---------------------------------------------- */
.shop-table {
    text-align: left;
    tbody {
        border-bottom: 1px solid $border-color;
    }
    td {
        padding: 1.5rem 0 1.5rem 1rem;
        border-top: 1px solid $border-color;
        font-size: 1.4rem;
    }
    th {
        padding: .3rem 0 .3rem;
        font-size: 1.6rem;
        font-weight: 600;
        line-height: 1.875;
        text-transform: uppercase;
        color: #222;
    }
    img {
        display: block;
        max-width: 100px;
    }
    .product-thumbnail a {
        display: block;
        width: 100px;
    }
    .remove {
        display: block;
        margin-right: -1rem;
        width: 3rem;
        height: 3rem;
        font-size: 1.7rem;
        text-align: center;
        color: #999;
        i {
            line-height: 3rem;
        }
        &:hover {
            color: $primary-color;
        }
    }
    .product-name { white-space: normal; }
    td.product-remove {
        padding-right: 0;
        width: 20px;
    }
}
@include mq('md') {
    .wishlist-table {
        td.product-price {
            width: 130px;
        }
        td.product-stock-status {
            width: 130px;
        }
    }
}
@include mq('lg') {
    .wishlist-table {
        td.product-price {
            width: 180px;
        }
        td.product-stock-status {
            width: 200px;
        }
        td.product-add-to-cart {
            width: 180px;
        }
    }
}
@include mq(md, max) {
    .shop-table {
        text-align: center;
        thead { display: none; }
        tbody, tr, td { display: block; }
        tbody {
            border: 1px solid $border-color;
        }
        tr {
            position: relative;
            padding: 4.2rem 0 4rem;
            & + tr {
                border-top: 1px solid $border-color;
            }
        }
        td {
            padding: 0;
            border: none;
        }
        .product-thumbnail {
            margin-bottom: 1rem;
        }
        .product-thumbnail a,
        .btn-product,
        td {
            margin-left: auto;
            margin-right: auto;
        }
        .product-stock-status {
            margin-bottom: 1rem;
        }
        .btn-product {
            max-width: 220px;
        }
        .product-remove {
            position: absolute;
            right: 2rem;
            top: .5rem;
            padding: .5rem;
            width: auto;
        }
        .product-quantity {
            margin-bottom: 1rem;
        }
    }
}
.wishlist-table {
    th.product-name {
        padding-left: 2px;
        width: 120px;
    }
    .wishlist-out-stock { color: $secondary-color; }
    .wishlist-in-stock { color: $primary-color; }
    .btn-product {
        padding: 1.4rem;
        background-color: #f2f3f5;
        color: #222;
        white-space: nowrap;
        font-size: 1.3rem;
        transition: color .3s, background-color .3s;
        &:hover {
            color: #fff;
            background-color: $primary-color;
        }
        &.btn-disabled {
            border-color: #f2f3f5 !important;
            background-color: #f2f3f5 !important;
        }
    }
}