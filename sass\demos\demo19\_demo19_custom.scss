/* 
Demo 19
*/
// Base
body {
	overflow-x: hidden;
}
.product-wrapper {
	.owl-theme {
		.owl-nav+.owl-dots {
			margin-top: 2rem;
		}
	}
}
.menu>li>a {
	font-weight: 600;
}
// Header
.header-middle {
	.divider {
		height: 1.8rem;
	}
	.wishlist i {
		font-size: 2.2rem;
	}
	.search-toggle i {
		font-size: 2rem;
	}
}
.header-right > *:not(:last-child):not(.mobile-search) { margin-right: 2.4rem; }
// Intro Slider
.intro-slider {
	figure img {
		height: calc(100vh - 103px) !important;
		object-fit: cover;
		object-position: top;
	}
	.banner-subtitle {
		margin-bottom: .7rem;
		font-size: 4em;
		letter-spacing: 1px;
	}
	.banner-title { font-size: 6em; }
	p { 
		font-size: 1.8em;
		margin-bottom: 3.3rem;
	}
	.btn { 
		padding: 1.25em 2.85em; 
		border-radius: 3px;
	}
	.h-divider {
		display: block;
		width: 4.9rem;
		height: 4px;
		color: #fff;
		margin-bottom: 2.2rem;
	}
	.banner-content {
		margin-top: 2px;
	}
	.duration {
		animation-duration: 30s;
	}
}
.intro-slide1 {
	.banner-content { left: 16.2%; }
	overflow: hidden;
}
.intro-slide2 {
	background-position: 75%;
	.banner-content { right: 10.45%; }
}
/* Banner Group */
.intro-banners {
    .banner-title { 
		font-size: 3.6em; 
		line-height: 1.15; 
		margin-bottom: 0px;
	}
    .banner-subtitle { 
		font-size: 1.6em;
		letter-spacing: -.02em;
	 }
    hr {
        width: 3.5rem;
        height: .4rem;
        margin: 1.7rem 0 1.8rem;
        border: none;
	}
	img {
		min-height: 30rem;
		object-fit: cover;
	}
	.btn {
		margin-bottom: 1px;
		i {
			margin-left: .7rem;
			margin-bottom: .3rem;
		}
	}
}
.banner-1 .banner-content { left: 10%; }
.banner-3 .banner-content { right: 4.1%; }
.banner-2 {
	.banner-title { font-size: 4.4em; white-space: nowrap; }
	.btn {
		padding: 1.07em 2.15em;
		font-size: 1.3rem;
	}
	h4 {
		font-size: 1.6em;
		line-height: 1.3;
		letter-spacing: -.3px;
	}
	p {
		font-size: 1.4em;
		color: #AAA;
		margin-bottom: 2.3rem;
	}
}
// Categories 
.categories {
	.category-content {
		height: 6.7rem;
		bottom: 0;
	}
	.category-name {
		font-size: 1.4rem;
	}
	figure {
		border-radius: 0 0 10px 10px;
		overflow: hidden;
	}
}
.category-classic:hover .category-content {
	background-color: $primary-color;
}
// Banner Group
.banner-group {
	img {
		min-height: 20rem;
		object-fit: cover;
		object-position: %30 center;
	}
	.banner-title {
		font-size: 3em;
		line-height: 1;
		color: #222;
	}
	h4 {
		color: #666;
		font-size: 1.6em;
		line-height: 2;
		letter-spacing: -.3px;
	}
	hr {
		height: 2px;
		margin: 1.5rem 0 1.3rem 2px;
		width: 3.5rem;
	}
}
.banner1 {
	.banner-content { right: 5.6%; }
}
.banner2 {
	.banner-content { right: 5.6%; }
	h4 {
		color: #aaa;
	}
	.banner-subtitle {
		color: #ccc;
	}
}
// Video Banner 
.video-banner {
	padding: 8.5rem 0 8.4rem;
	.banner-title { 
		font-size: 3em; 
		letter-spacing: .9px;
	}
	.banner-subtitle {
		margin-bottom: .8rem;
		font-size: 1.6em;
	}
	.btn-play {
		padding: 1rem 1.2rem;
		border-width: 1px;
		font-size: 3rem;
		width: 61px;
		height: 61px;
		line-height: 4.2rem;
		color: #fff;
	}
	.d-icon-play-solid {
		font-size: 3.5rem;
	}
}
.instagram {
	border-radius: 0;
	&.appear-animate {
		will-change: filter, opacity;
	}
}
// Footer
.footer {
    .widget {
        &:not(.widget-about) {
            margin-top: .5rem;
        }
    }
    .widget-about p {
        margin-bottom: 2.7rem;
    }
    .social-link {
        line-height: 3rem;
        border-width: 1px;
    }
    .footer-info {
        max-width: 48rem;
    }
}
.logo-footer {
    max-width: 15.4rem;
}
.widget-about {
    label {
        display: block;
        margin-bottom: .5rem;
        font-size: 1.4rem;
    }
    .widget-body {
        margin-bottom: .6rem;
        li { margin-bottom: 2.2rem; }
    }
}
.widget-newsletter {
    .footer & {
        padding-top: .7rem;
    }
    p { margin-bottom: 2.5rem; line-height: 1.54; }
    .input-wrapper {
        height: 5rem;
        input.form-control {
            padding-left: 1.6rem;
            letter-spacing: .000 5em;
            color: #686865;
			background-color: #2c2c2c;
			&::placeholder {
				color: #999;
			}
        }
        .btn {
            padding: 0 1.5rem;
            font-size: 1.3rem;
            font-weight: 400;
            font-family: $font-family;
			letter-spacing: .000 5em;
			border-radius: 0 .3rem .3rem 0;
            i {
                margin-left: .2rem;
            }
        }
    }
}
// Shop
.tag {
	padding: 1.5px .9rem;
	margin: .5rem 1rem 1rem 1.5px;
	line-height: 1.58;
	text-transform: capitalize;
}
.main.border-top {
	border-top: 1px solid #ddd;
}
aside {
    .service-list {
		padding: 0 1.8rem 0 1.4rem;
		.icon-box {
			padding-top: 2.3rem;
			padding-bottom: 1.6rem;
		}
		.icon-box-title {
			font-size: 1.4rem;
			margin-bottom: 0px;
		}
	}
    .banner-content {
        top: 10%;
    }
    .owl-nav-top .owl-nav {
        top: -3.5rem;
    }
} 
// product page
.accordion {
    .card-header {
        .collapse {
           color: #222;
        }
        a {
            padding: 2.7rem 0;
            &:after {
                content: '\e952';
            }
            &.collapse:after {
				content: '\e953';
				color: #222;
            }
        }
    }
    .description-title {
        font-size: 1.6rem;
    }
    .card-body {
        padding: 0;
    }
    ul {
        padding-inline-start: 0;
        li {
            list-style: none;
        }
    }
    #product-tab-description .description-title ~ p, .card-description .description-title ~ p {
        line-height: 1.6;
    }
}
// Responsive
@include mq(xxl, max) {
	.intro-banners .banner {
		font-size: .8rem;
	}
}
@include mq(xl, max) {
	.header-right > *:not(:last-child) { margin-right: 1.5rem; }
	.intro-slide1 .banner-content { left: 9rem; }
	.intro-slide2 .banner-content { right: 2rem; }
	.intro-banners .banner {
		font-size: .7rem;
	}
}
@include mq(1080px, max) {
	.header-right > *:not(.cart-dropdown) {
		margin-right: 1rem;
		label{ display: none; }
	}
}
@include mq(lg, max) {
	.intro-slider .banner { height: calc(100vh - 99px); }
	.wishlist { display: none; }
	.banner { font-size: .9rem; }
	.intro-banners .banner {
		font-size: .7rem;
	}
}
@include mq(md) {
	.mobile-menu-toggle {
		color: $primary-color;
	}
}
@include mq(sm, max) {
	.intro-slider .banner-subtitle::before {
		width: .8em;
		transform: translateX(calc(-100% - 1rem));
		.btn { font-size: 1.3rem;}
	}
	.intro-slide1 .banner-content,
	.intro-slide2 .banner-content { left: 2rem; right: auto; }
	.banner { font-size: .8rem; }
}
@include mq(xs, max) {
	.banner-group {
		img {
			min-height: 20rem;
			object-position: 55% center;
		}
		.banner-title { margin: 0 0 2rem; }
		.banner-content {
			text-align: center;
			flex-direction: column;
			left: 0;
			right: 0;
		}
	}
}
/* Instagram */
.instagram {
    a {
        &::after {
            transform: translate(-50%, -50%) rotate(90deg) scale(2);
            transition: all .3s;
        }
    }
    &:hover {
        a {
            &::after {
                transform: translate(-50%, -50%) rotate(0deg) scale(1);
            }
        }
    }
}
