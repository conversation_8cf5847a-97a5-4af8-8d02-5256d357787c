﻿/* -------------------------------------------
    @Component - Product Single
---------------------------------------------- */
// Variables
@include set-default(
    (
        product-single: (
            name: (
                margin-bottom: .6rem,
                font-size: 2.6rem,
                font-weight: 700,
                letter-spacing: -.025em,
                white-space: normal,
                color: $dark-color,
            ),
            دسته بندی : (
                color: $grey-color,
                font-size: 1.3rem,
                font-weight: 400,
                letter-spacing: -.025em
            ),
            price: (
                color: $secondary-color,
                font-size: 3rem,
                font-weight: 700,
                letter-spacing: -.025em
            ),
            old-price: (
                font-size: 2.4rem,
                font-weight: 600,
                text-decoration: false,
            ),
            product-meta: (
                color: $grey-color,
                font-size: 1.3rem
            ),
            product-short-desc: (
                font-family: false,
            ),
            label: (
                color: $dark-color,
            ),
            variation: (
                width: 3rem,
                height: 3rem,
            ),
            btn-cart: (
                max-width: 20.7rem,
                height: 4.5rem,
            ),
            rating: (
                review-color: $grey-color,
            )
        )
    )
);
// Product Gallery Thumbs
.product-thumbs-wrap,
.product-single-carousel {
    flex-basis: 100%;
    max-width: 100%;
}
.product-single-carousel .owl-nav .owl-prev,
.product-single-carousel .owl-nav .owl-next {
    color: $primary-color-dark;
    border: 0;
}
.product-image {
    position: relative;
}
// Product Thumbs Dots
.product-thumbs-wrap {
    position: relative;
    margin-top: 1rem;
    overflow: hidden;
    img {
        display: block;
    }
    button {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 2.4rem;
        border: 0;
        transition: opacity .3s, transform .3s;
        z-index: 1;
        opacity: 0;
        box-shadow: 0 0 1rem rgba(0,0,0,.1);
        -webkit-appearance: none;
        cursor: pointer;
    }
    &:hover button:not(.disabled) {
        opacity: .9;
        transform: none;
    }
    button:not(.disabled):hover {
        opacity: 1;
    }
    > button {
        display: none;
    }
}
.product-thumbs-wrap button,
.product-thumbs .owl-nav .owl-prev,
.product-thumbs .owl-nav .owl-next {
    background-color: $bg-secondary-white-color;
    color: $body-color;
    font-size: 1.6rem;
}
.product-thumbs-wrap button,
.product-thumb::before {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
}
.product-thumbs {
    transition: top .3s ease-out;
    display: flex;
    &.owl-carousel {
        margin: 0 -.5rem;
        width: calc(100% + 1rem);
    }
    .owl-stage {
        display: flex;
    }
    .owl-prev {
        transform: translateX(-100%);
        left: .5rem;
    }
    .owl-next {
        left: auto;
        right: .5rem;
        transform: translateX(100%);
    }
}
.product-thumb {
    position: relative;
    margin: 0 .5rem;
    cursor: pointer;
    overflow: hidden;
    &::before {
        content: '';
        transition: border-color .3s;
        border: 2px solid transparent;
    }
    &.active::before {
        border-color: $primary-color;
    }
    .btn-play {
        position: relative;
        display: block;
        z-index: 2;
        &:before {
            content: "";
            position: absolute;
            left: -100px;
            top: -100px;
            right: -100px;
            bottom: -100px;
            background: rgba($black-color,0.1);
            transition: background 0.3s;
        }
        &:after {
            content: '\f021';
            font-family: "Font Awesome 5 Free";
            font-size: 16px;
            font-weight: 800;
            color: #fff;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
            border: 2px solid #fff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            line-height: 36px;
            text-align: center;
            transition: box-shadow 0.3s;
        }
        &:hover {
            &:before {
                background: rgba(0,0,0,0.2);
            }
            &:after {
                box-shadow: 0 0 0 15px rgb(255 255 255 / 20%);
            }
        }
    }
    .btn-video:after {
        content: '\f04b';
    }
}
// Product Detail
.product-single {
    color: inherit;
    .product-details {
        padding: 0 0 3rem;
        background-color: transparent;
        // on product fullwidth page
        .container-fluid & {
            padding-top: 4px;
        }
        // on product with sidebar page
        aside + div & {
            padding-top: 2px;
        }
    }
    .product-cat {
        margin-bottom: 1.3rem;
        @include print_css( product-single, categories );
        span { margin-right: .7rem; }
    }
    .product-name {
        @include print_css( product-single, name );
    }
    .product-price {
        display: block;
        margin-bottom: 1.1rem;
        @include css(color, product-single, price, color);
        @include css(font-size, product-single, price, font-size);
        @include css(font-weight, product-single, price, font-weight);
        @include css(letter-spacing, product-single, price, letter-spacing);
        line-height: 1;
    }
    .old-price {
        @include css(font-size, product-single, old-price, font-size);
        @include css(font-weight, product-single, old-price, font-weight);
        @include css(text-decoration, product-single, old-price, text-decoration);
    }
    .rating-reviews {
        &:not(:hover) {
            @include css(color, product-single, rating, review-color);
        }
        font-size: 1.3rem;
    }
    .ratings-container {
        margin-bottom: 1.7rem;
        font-size: 14px;
        .ratings:before {
            color: $body-color;
        }
    }
    .ratings-full {
        margin-top: -2px;
    }
    label {
        @include css(color, product-single, label, color);
        font-weight: 600;
        text-transform: uppercase;
    }
    .product-action {
        display: inline-block;
        .btn-wishlist {
            position: relative;
        }
    }
    .divider {
        margin-left: -1rem;
        margin-right: 1.8rem;
    }
    .social-links {
        //margin-right: 3rem;
        color: $grey-color;
    }
    .social-link {
        border: none;
        &.social-facebook:hover {
            background-color: transparent;
            color: #3b5998;
        }
        &.social-twitter:hover {
            background-color: transparent;
            color: #1da1f2;
        }
        &.social-pinterest:hover {
            background-color: transparent;
            color: #bd081c;
        }
    }
    .product-footer {
        > * {
            margin-bottom: 1rem;
        }
    }
    .btn-wishlist,
    .btn-compare {
        display: inline-block;
        padding: .5rem 0;
        font-weight: 400;
        font-size: 1.4rem;
        background-color: transparent;
        transition: color .3s;
        text-transform: none;
        color: $body-color;
        i {
            display: inline-block;
            margin: 0 .5rem .3rem 0;
            vertical-align: middle;
            font-size: 1.8rem;
            line-height: 0;
        }
        &:hover {
            color: $primary-color;
        }
    }
    .btn-compare {
        i { 
            margin-right: .8rem;
            font-size: 2.1rem; 
        }
    }
    .quantity {
        font-weight: 700;
    }
    .product-meta + .product-countdown-container {
        margin-top: .4rem;
    }
    .product-countdown-container {
        display: inline-flex;
        align-items: center;
        margin-bottom: 2.5rem;
        padding: 1rem 1.6rem;
        border: 1px solid #444;
        background-color: #444;
        text-transform: none;
        line-height: 1;
        color: $white-color;
        border-radius: .3rem;
        label {
            margin-right: .5rem;
            text-transform: none;
            color: $white-color;
        }
        .product-sale-info {
            position: relative;
            padding-right: 1.5rem;
            i {
                margin-right: .8rem;
            }
            &::after {
                content: '';
                display: block;
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 1px;
                height: 2.4rem;
                background: rgba($bg-secondary-white-color,.2);
            }
            & + label {
                margin-left: 1.5rem;
            }
        }    
    }
    .product-label-group {
        top: 2rem;
        left: 2.5rem;
        z-index: 2;
        align-items: flex-start;
    }
    .product-label {
        color: $white-color;
    }
    // variation - list box type
    .product-variations{
        > a:not(.size-guide) {
            @include css(width, product-single, variation, width);
            @include css(height, product-single, variation, height);
        }
    }
    .color {
        border: 0;
    }
    .size {
        width: 3.6rem;
    }
    .accordion ul {
        list-style: none;
        padding-left: 0;
    }
    .rating-form, form {
        label {
            font-weight: 400;
            font-size: 1.3rem;
            text-transform: none;
            color: $body-color;
        }
    }
}
//dark-theme 
.dark-theme {
    .ratings-container .ratings:before {
        color: $primary-color;
    }
    .social-links {
        color: #666;
    }
    .btn-wishlist,
    .btn-compare {
        color: #999;
        &.added {
            color: #fff;
        }
    }
    .product-variation-clean:not(:hover) {
        color: #fff;
    }
    .product-form.btn-cart,
    .product-form.btn-external {
        &:disabled {
            background-color: #666;
            color: #aaa;
        }
    }
}
.product-meta {
    margin-bottom: 1.8rem;
    @include css(color, product-single, product-meta, color);
    @include css(font-size, product-single, product-meta, font-size);
    span {
        margin: 0  3px 0 1.9rem;
    }
}
// Product Form
.product-form {
    display: flex;
    align-items: flex-start;
    flex-wrap: nowrap;
    @if ( get(product-single, variation, height ) ) {
        line-height: get(product-single, variation, height);
    }
    @else if ( get(product, variation, height ) ) {
        line-height: get(product, variation, height);
    }
    margin: 0 0 1rem;
    // label
    > label {
        min-width: 7rem;
        margin-bottom: 1rem;
        text-transform: capitalize;
        font-size: 16px;
        font-weight: 400;
        
    }
    // select box type
    &.product-variations {
        line-height: 37px;
    }
    .select-box::before {
        right: 1.4rem;
        font-size: 1.3rem;
        color: $dark-color;
    }
    select {
        max-width: none;
        padding: .8rem 3rem .8rem 1.4rem;
        color: $dark-color;
        border-color: $border-color-secondary-light;
        font-size: 1.3rem;
    }
    // list box type
    .product-variations {
        display: block;
        margin-top: -3px;
        margin-bottom: -3px;
        }
    // quantity type
    &.product-qty {
        line-height: 4.5rem;
    }
    .quantity-minus {
        border-radius: .3rem 0 0 .3rem;
    }
    .quantity-plus {
        border-radius: 0 .3rem .3rem 0;
    }
    .input-group {
        margin-right: 1rem;
    }
    .btn-cart,
    .btn-external {
        border: 0;
        flex: 1;
        min-width: 13rem;
        font-size: 1.4rem;
        border-radius: .3rem;
        background-color: $primary-color;
        transition: background-color .3s;
        color: $white-color;
        cursor: pointer;
        @include css(max-width, product-single, btn-cart, max-width);
        @include css(height, product-single, btn-cart, height);
        &:disabled {
            background-color: #e4eaec;
            cursor: not-allowed;
            color: $grey-color;
        }
        i {
            margin-right: .8rem;
            margin-top: -1px;
            font-size: 1.8rem;
            line-height: 0;
            vertical-align: middle;
            &::before {
                margin: 0;
            }
        }
        &:hover:not(:disabled) {
            background-color: darken( $primary-color, 7% );
        }
    }
}
// external
.product-external {
    .product-short-desc {
        color: #ee8204;
    }
    .product-form button {
        background: #ee8204;
    }
}
// stock
.stock {
    display: inline-block;
    padding: 4px 2rem;
    margin-bottom: 2rem;
    border-radius: 2px;
    color: $white-color;
    font-weight: 600;
}
.in-stock {
    background: $secondary-color;
}
.out-of-stock {
    background: $light-color;
}
.product-single .product-label.label-stock {
    white-space: nowrap;
    background: $light-color;
}
// hurryup
.hurryup-bar {
    > p {
        margin-bottom: 0.5rem;
        color: $dark-color;
        background: transparent;
        padding: 0;
    }
    .bar {
        position: relative;
        display: block;
        width: 100%;
        max-width: 38rem;
        height: 14px;
        background: #eee;
        border-radius: 10px;
    }
    .stock-bar {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        display: block;
        background: $secondary-color;
        border-radius: 10px;
    }
}
// grouped control
.product-form-group {
    position: relative;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    flex: 1;
    > * {
        margin-bottom: 1rem;
    }
    // list box type
    .product-variations {
        margin-bottom: 7px;
    }
    // group
    tr {
        border-bottom: 1px solid $border-color-light;
    }
    td {
        padding: 2rem 0;
    }
    .item-name {
        padding-left: 2rem;
        color: $dark-color;
        line-height: 1.4;
    }
    .item-price {
        font-weight: 600;
        color: $dark-color;
        padding-left: 4.5rem;
    }
}
// product size guide
.size-guide {
    display: inline-flex;
    align-items: center;
    font-weight: 300;
    i {
        margin-right: .8rem;
        font-size: 2.1rem;
        color: $body-color;
    }
}
.select-box + .size-guide {
    margin-left: 10px;
}
// product variation price
.product-variation-price {
    display: none;
    padding-top: 25px;
    span {
        margin-bottom: 1rem;
        color: $dark-color;
        font-size: 2.4rem;
        font-weight: 700;
        letter-spacing: -.025em;
    }
}
.product-variation-clean {
    display: block;
    position: absolute;
    margin-top: 1rem;
    padding: .3em 1em;
    left: 0;
    top: calc(100% - 10px);
    font-size: 1rem;
    line-height: 1.6;
    background: $lighter-color;
    color: $black-color;
}
//related products
.related-products {
    margin-top: 6.5rem;
}
@include mq(lg) {
    .pg-vertical {
        .product-thumbs-wrap {
            order: -1;
            max-width: 109px;
            margin: 0 0 0 1rem;
        }
        .product-single-carousel {
            max-width: calc(100% - 119px);
        }
        .product-thumbs {
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }
        .product-thumb {
            margin: 0 0 1rem;
        }
        .thumb-up,
        .thumb-down {
            display: flex;
            width: 100%;
            height: 2.4rem;
        }
        .thumb-up{
            transform: translateY(-100%);
            i::before {
                content: "\f077";
            }
        }
        .thumb-down{
            top: auto;
            transform: translateY(100%);
            i::before {
                content: "\f078";
            }
        }
        .product-label-group {
            left: 14rem;
        }
    }
}
// Product Sticky Both (new)
.product-single.product-sticky-both {
    .p-sticky { top: 88px; }
    .product-details {
        padding: 0;
    }
}
.product-tabs.tab-nav-simple .nav-link {
    font-size: 2rem;
    font-weight: 700;
    text-transform: capitalize;
    color: $dark-color;
    letter-spacing: 0;
}