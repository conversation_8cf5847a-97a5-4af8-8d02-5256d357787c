﻿/* -------------------------------------------
    Dropdowns
        - Dropdown
        - Dropdown-expanded
        - CartDropdown
        - Category Dropdown
---------------------------------------------- */
// Variables
@include set_default(
    (
        header: (
            cart: (
                toggle: (
                    padding: .6rem 0,
                ),
                label: (
                    margin: 0 .7rem 0 0,
                    padding: false,
                    font-family: false,
                    font-size: false,
                    font-weight: inherit,
                    text-transform: uppercase,
                    letter-spacing: inherit,
                    color: false,
                    price: (
                        color: $primary-color
                    )
                ),
                icon: (
                    display: inline-block,
                    font-size: 1.3rem,
                    color: $primary-color,
                    hover: (
                        border-color: false,
                        background: $primary-color
                    )
                ),
                count: (
                    font-family: false,
                    font-size: 1.3rem,
                    font-weight: 600,
                    line-height: 25px,
                    color: $primary-color,
                    hover: (
                        color: #fff
                    )
                )
            ),
            دسته بندی: (
                toggle: (
                    padding: 1.3rem 1.55rem,
                    background: $primary-color,
                    icon: (
                        font-size: 2.2rem
                    ),
                    label: (
                        margin-left: .6rem
                    )
                )
            )
        )
    )
);
// Dropdown
.dropdown {
    position: relative;
    &:hover,
    &.show {
        .dropdown-box {
            visibility: visible;
            opacity: 1;
            top: 100%;
        }
        &::after {
            visibility: visible;
            opacity: 1;
            top: calc(100% - 20px);
            transform: translate3d(-50%, 0, 0);
        }
        .dropdown-box {
            transform: translate3d(0, 0, 0);
        }
        > a {
            color: $primary-color;
        }
    }
    &::after {
        content: '';
        position: absolute;
        z-index: 1000;
        left: 50%;
        top: -9999px;
        transform: translate3d(-50%, -8px, 0);
        border: 11px solid transparent;
        border-bottom: 11px solid #fff;
        transition: opacity .2s ease-out, transform .2s ease-out;
        visibility: hidden;
        opacity: 0;
        cursor: pointer;
    }
    a {
        display: flex;
        align-items: center;
        .dropdown-image {
            max-width: 1.4rem;
            margin-right: .7rem;
            height: auto;
        }
    }
    > a {
        line-height: 1.1;
        padding: 9px 0;
        &::after {
            display: inline-block;
            margin-left: 6px;
            font: {
                family: 'Font Awesome 5 Free';
                weight: 600;
                size: 8px;
            }
            line-height: 1;
            content: '\f078';
        }
    }
    li {
        &.active,
        &:hover {
            > a {
                color: $primary-color;
            }
        }
    }
    &.dir-up {
        &::after {
            border-bottom-color: transparent;
            border-top: 11px solid #fff;
            transform: translate3d(-50%, 8px, 0);
        }
        &:hover,
        &.show {
            .dropdown-box {
                top: auto;
                bottom: 100%;
            }
            &::after {
                top: auto;
                bottom: calc(100% - 20px);
                transform: translate3d(-50%, 0, 0);
            }
        }
    }
}
.dropdown-box {
    position: absolute;
    right: 0;
    top: -9999px;
    margin: 0;
    padding: .5rem 0;
    color: #666;
    background-color: #fff;
    box-shadow: 0 2px 35px rgba(0,0,0,0.1);
    z-index: 1000;
    visibility: hidden;
    opacity: 0;
    transition: transform .2s ease-out, opacity .2s, visibility .2s;
    transform: translate3d(0, -10px, 0);
    a {
        padding: .6rem 1rem;
    }
    li {
        font-size: inherit;
        line-height: 1.1;
    }
    .dir-up & {
        transform: translate3d(0, 10px, 0);
    }
}
// Dropdown-expanded
@include mq(lg) {
    .dropdown-expanded {
        &::after {
            content: none;
        }
        > a {
            display: none;
        }
        .dropdown-box {
            position: static;
            display: flex;
            visibility: visible;
            opacity: 1;
            background-color: transparent;
            box-shadow: none;
            border: 0;
            padding: 9px 0;
            transform: none;
            color: inherit;
            a {
                padding: 0;
            }
            > li {
                margin-right: 1.95rem;
                &:last-child {
                    margin-right: 0;
                }
            }
        }
    }
}
@include mq(lg, max) {
    .dropdown.dropdown-expanded {
        li:hover > a {
            color: $primary-color;
        }
    }
}
// CartDropdown
.cart-dropdown {
    .cart-toggle {
        @include print_css( header, cart, toggle );
        &::after {
            content: none;
        }
    }
    .cart-label {
        display: block;
        cursor: pointer;
        @include print_css( header, cart, label );
    }
    .cart-name {
        margin-right: 5px;
        &:after {
            content: '/';
            margin-left: 6px;
        }
    }
    .cart-price {
        @include print_css( header, cart, label, price );
    }
    .minicart-icon {
        @include print_css( header, cart, icon );
    }
    .minicart-icon2 {
        @include print_css( header, cart, icon );
    }
    .cart-count {
        display: inline-block;
        transition: color .4s;
        @include print_css( header, cart, count );
    }
    .cart-total {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
        margin-bottom: 2rem;
        border-top: 1px solid #edeef0;
        border-bottom: 1px solid #edeef0;
        font-size: 1.4rem;
        line-height: 2.93;
        label {
            margin-right: .3rem;
            line-height: inherit;
            color: #999; 
        }
        .price {
            font-weight: 600;
        }
    }
    .cart-action {
        column-count: 2;
        column-gap: 10px;
        .btn {
            padding: .53em 2em;
            line-height: 1.5;
        }
    }
    &:hover {
        .minicart-icon {
            @include print_css( header, cart, icon, hover );
            &::before {
                transform: rotateY(180deg);
            }
        }
        .cart-count {
            @include print_css( header, cart, count, hover );
        }
    }    
    .dropdown-box {
        padding: 2rem;
        min-width: 31.4rem;
    }
    .products {
        max-height: 200px;
        overflow-x: hidden;
        margin-right: -5px;
        padding-right: 5px;
    }
    &.cart-dropdown-white {
        .cart-label,
        .cart-price,
        .cart-count {
            color: #fff;
        }
        .minicart-icon {
            border-color: #fff;
        }
        &:hover {
            .minicart-icon {
                background-color: #fff;
            }
            .cart-count {
                color: $primary-color;
            }
        }
    }
    &.type2 {
        .cart-toggle {
            flex-direction: column;
            align-items: center;
        }
        .cart-label {
            margin: 0;
        }
        &:hover {
            color: $primary-color;
        }
        .cart-count {
            position: absolute;
            right: -7px;
            top: -6px;
            width: 1.9rem;
            height: 1.9rem;
            line-height: 1.8rem;
            font-size: 1.1rem;
            border-radius: 50%;
            background-color: $primary-color;
            color:  #fff;
            z-index: 1;
        }
    }
}
// Cart Product
.product.product-cart {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    font: {
        size: 1.3rem;
    }
    .product-media {
        max-width: 9rem;
        max-height: 9rem;
        margin-right: 1.1rem;
        a {
            padding: 0;
        }
    }
    .product-detail {
        flex: 1;
        margin: 0 1rem .5rem 0;
    }
    .product-name {
        white-space: normal;
        padding: 0;
        margin-bottom: .9rem;
        font-size: 1.3rem;
        line-height: 1.54;
        letter-spacing: -.022em;
    }
    .price-box {
        display: flex;
        align-items: center;
        font-weight: 600;
        line-height: 1;
    }
    .product-price {
        font-size: 1.3rem;
    }
    .product-quantity {
        margin-right: .6rem;
        color: #999;
        &::after {
            margin-left: .6rem;
            font-size: 1.4rem;
            content: 'x';
            text-transform: none;
            line-height: 0;
        }
    }
    .product-price {
        margin: 0;
        color: $secondary-color;
    }
    .btn-close {
        padding: 0;
        color: #999;
        transition: color .4s;
        &:hover {
            color: $primary-color;
        }
        i {
            font: {
                size: 1.3rem; 
                weight: 500;
            }
            &::before {
                margin: 0;
            }
        }
    }
    &:last-child {
        margin-bottom: 0;
    }
}
@include mq('lg', 'max') {
    .cart-dropdown {
        .cart-label {
            display: none;
        }
    }
}
@include mq('sm', 'max') {
    .sticky-footer {
        .dropdown-box {
            right: 1.5rem;
        }
    }
    .cart-dropdown {
        .product .product-media {
            margin-right: 1rem;
            max-width: 7rem;
            max-height: 7rem;
        }
        .dropdown-box {
            padding: 1.5rem;
            min-width: 27.8rem;
        }
        .product { margin-bottom: 1.5rem }
        .cart-total { font-size: 1.3rem }
        .cart-action .btn {
            padding: .53em 0;
            justify-content: center;
        }
    }
}
// Category Dropdown
.category-dropdown {
    > a {
        @include print_css( header, category, toggle );
        &::after {
            content: none;
        }
        i {
            @include print_css( header, category, toggle, icon );
        }
        span {
            @include print_css( header, category, toggle, label );
        }
    }
    .dropdown-box {
        padding: 0;
        left: 0;
        min-width: 28rem;
        box-shadow: none;
        background-color: #f4f4f4;
        transition: opacity .2s, z-index 0s, transform 0s;
        visibility: hidden;
        opacity: 0;
        transform: none;
        top: 100%;
    }
    &::before,
    &::after {
        left: 25px;
        transform: translate3d(-50%, 0, 0);
    }
    &::after {
        border-bottom-color: #f4f4f4;
        visibility: hidden;
        opacity: 0;
        top: calc(100% - 20px);
    }
    &.menu-fixed { 
        .dropdown-box,
        &::after {
            visibility: hidden;
            opacity: 0;
        }
    }
    &.dropdown.show {
        .dropdown-box {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: opacity .5s, z-index 0s, transform 0s;
        }
        .dropdown-box,
        &::after {
            visibility: visible;
            opacity: 1;
        }
    }
    &.has-border {
        &::after {
            border-bottom-color: #fff;
        }
        &::before {
            content: '';
            position: absolute;
            z-index: 1000;
            top: -9999px;
            transform: translateX(-50%);
            border: 11px solid transparent;
            border-bottom: 11px solid #e1e1e1;
            transition: opacity .4s ease;
            visibility: hidden;
            opacity: 0;
            cursor: pointer;
        }
        .dropdown-box {
            background-color: #fff;
            border: 1px solid #e1e1e1;
        }
        &.menu-fixed {
            &::before {
                visibility: visible;
                opacity: 1;
            }
        }
    }
}
.sticky-header:not(.fixed) {
    .category-dropdown.menu-fixed {
        &::after {
            top: 100%;
        }
        .dropdown-box {
            top: calc(100% + 20px);
        }
        .dropdown-box,
        &::after {
            visibility: visible;
            opacity: 1;
        }
        &.has-border {
            &::before {
                top: calc(100% - 1px);
                visibility: visible;
                opacity: 1;
            }
            @include only-for-retina(1.5) {
                &::before {
                    top: calc(100% - 2px);
                }
            }
        }
    }
}