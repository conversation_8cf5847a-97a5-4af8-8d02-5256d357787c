/* 
Demo Food2
*/
@include set(
    (
        base: (
            headings: (
                color: #333
            )
        ),  
        header: (
            top: (
                padding-top: 6px,
                padding-bottom: 6px,
                font-size: 1.3rem,
                _links-gap: 2rem
            ),
            middle: (
                padding-top: 2.8rem,
                font-size: 1.4rem,
                color: #333,
                login: (
                    icon: (
                        font-size: 2rem
                    )
                )
            ),
            call: (
                icon: (
                    margin: 0 0 2px 7px ,
                    font-size: 1.5rem
                )
            ),
            contact: (
                icon: (
                    margin: 0 .65rem 0 0,
                    font-size: 1.5rem
                )
            ),
            wishlist: (
                icon: (
                    font-size: 2.3rem
                )
            ),
            help: (
                icon: (
                    font-size: 1.6rem
                )
            ),
            main-nav: (
                margin: 0 0 0 2px
            ),
            sticky: (
                padding-top: 1.8rem,
                padding-bottom: 1.8rem
            )
        ),
        menu: (
            ancestor: (
                font-weight: 600
            )
        ),
        post: (
            detail: (
                padding: 2rem 0 2.5rem
            ),
            meta: (
                margin-bottom: 0px,
                info: (
                    font-weight: 400
                )
            ),
            title: (
                margin-bottom: 1.2rem
            ),
            btn: (
                _icon-gap: 6px
            )
        ),
        footer: (
            top: (
                padding: 3.5rem 0 3.1rem,
                border-bottom: false
            ),
            middle: (
                padding: 67px 0 17px,
                border-bottom: false,
                widget: (    
                    title: (
                        text-transform: initial,
                        padding: 0,
                        color: #fff,
                        margin-bottom: 1.6rem
                    ),
                    label: (
                        color: #777,
                        margin-bottom: 1rem,
                        font-weight: 700
                    ),
                    body: (
                        font-size: 1.3rem,
                        color: #a6a6a6,
                        font-family: $font-family,
                    ),
                    list-item: (
                        display: flex,
                        flex-direction: column,
                        color: #fff,
                        margin-bottom: 2rem                        
                    )
                )
            ),
            newsletter: (
                title: (
                    margin-bottom: 2rem,
                    font-size: 1.6rem,
                    letter-spacing: 0,
                    line-height: false
                ),
                desc: (
                    margin-top: -1px,
                    margin-bottom: 2rem,
                    font-size: 1.3rem,
                    letter-spacing: normal,
                    line-height: 2,
                    color: #999,
                ),
                btn: (
                    padding: 1.2em 1.38em 1.07em
                ),
                input: (
                    padding: 1rem 1.5rem
                )
            ),
            bottom: (
                padding: 3rem 1px 2.9rem 0,
                border-top: 1px solid #333,
            ),
            social-link: (
                display: flex,
                align-items: center,
                justify-content: center,
                width: 4rem,
                height: 4rem,
                font-size: 1.6rem,
                color: #999,
                border-color: #666,
                margin-right: 10px
            ),
            about: (
                logo: (
                    margin-bottom: 5rem
                )
            ),
            copyright: (
                font-weight: 400,
                color: #666,
                letter-spacing: normal
            ),
        )
    )
);