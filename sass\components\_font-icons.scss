﻿@font-face {
  font-family: 'riode';
  src:  url('../fonts/riode.eot?5gap68');
  src:  url('../fonts/riode.eot?5gap68#iefix') format('embedded-opentype'),
    url('../fonts/riode.ttf?5gap68') format('truetype'),
    url('../fonts/riode.woff?5gap68') format('woff'),
    url('../fonts/riode.svg?5gap68#riode') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}
[class^="d-icon-"], [class*=" d-icon-"] {
  display: inline-block;
  line-height: 1;
  &::before {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'riode' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}
.d-icon-truck:before { content: "\e900"; }
.d-icon-service:before { content: "\e901"; }
.d-icon-secure:before { content: "\e902"; }
.d-icon-lock:before { content: "\e903"; }
.d-icon-percent:before { content: "\e904"; }
.d-icon-layer:before { content: "\e905"; }
.d-icon-alert:before { content: "\e906"; }
.d-icon-database:before { content: "\e907"; }
.d-icon-money:before { content: "\e908"; }
.d-icon-shoppingbag:before { content: "\e909"; }
.d-icon-t-shirt1:before { content: "\e90a"; }
.d-icon-t-shirt2:before { content: "\e90b"; }
.d-icon-hat:before { content: "\e90c"; }
.d-icon-officebag:before { content: "\e90d"; }
.d-icon-handbag:before { content: "\e90e"; }
.d-icon-backpack:before { content: "\e90f"; }
.d-icon-shoes:before { content: "\e910"; }
.d-icon-pillow:before { content: "\e911"; }
.d-icon-dress:before { content: "\e912"; }
.d-icon-loungewear:before { content: "\e913"; }
.d-icon-desktop:before { content: "\e914"; }
.d-icon-laptop:before { content: "\e915"; }
.d-icon-gamepad1:before { content: "\e916"; }
.d-icon-wireless:before { content: "\e917"; }
.d-icon-projector:before { content: "\e918"; }
.d-icon-drone1:before { content: "\e919"; }
.d-icon-drone2:before { content: "\e91a"; }
.d-icon-radio:before { content: "\e91b"; }
.d-icon-battery:before { content: "\e91c"; }
.d-icon-plugin:before { content: "\e91d"; }
.d-icon-memory:before { content: "\e91e"; }
.d-icon-ball:before { content: "\e91f"; }
.d-icon-basketball1:before { content: "\e920"; }
.d-icon-babycare:before { content: "\e921"; }
.d-icon-card:before { content: "\e922"; }
.d-icon-gamepad2:before { content: "\e923"; }
.d-icon-camera1:before { content: "\e924"; }
.d-icon-camera2:before { content: "\e925"; }
.d-icon-babywear:before { content: "\e926"; }
.d-icon-abacus:before { content: "\e927"; }
.d-icon-pot:before { content: "\e928"; }
.d-icon-freezer:before { content: "\e929"; }
.d-icon-cook:before { content: "\e92a"; }
.d-icon-student:before { content: "\e92b"; }
.d-icon-دسته بندی:before { content: "\e92c"; }
.d-icon-basketball2:before { content: "\e92d"; }
.d-icon-watch:before { content: "\e92e"; }
.d-icon-tcard:before { content: "\e92f"; }
.d-icon-heartbeat:before { content: "\e930"; }
.d-icon-watch-round:before { content: "\e931"; }
.d-icon-washbowl:before { content: "\e932"; }
.d-icon-bridge-lamp:before { content: "\e933"; }
.d-icon-lamp:before { content: "\e934"; }
.d-icon-sofa:before { content: "\e935"; }
.d-icon-sofa2:before { content: "\e936"; }
.d-icon-bed:before { content: "\e937"; }
.d-icon-table:before { content: "\e938"; }
.d-icon-table-lamp:before { content: "\e939"; }
.d-icon-table-tv:before { content: "\e93a"; }
.d-icon-mirror:before { content: "\e93b"; }
.d-icon-volume:before { content: "\e93c"; }
.d-icon-bars2:before { content: "\e93d"; }
.d-icon-bars:before { content: "\e93e"; }
.d-icon-phone:before { content: "\e93f"; }
.d-icon-user:before { content: "\e940"; }
.d-icon-search:before { content: "\e941"; }
.d-icon-search2:before { content: "\e98c"; }
.d-icon-bag:before { content: "\e942"; }
.d-icon-map:before { content: "\e943"; }
.d-icon-info:before { content: "\e944"; }
.d-icon-refresh:before { content: "\e945"; }
.d-icon-left-arrow:before { content: "\e946"; }
.d-icon-right-arrow:before { content: "\e947"; }
.d-icon-down-arrow:before { content: "\e948"; }
.d-icon-up-arrow:before { content: "\e949"; }
.d-icon-ruler:before { content: "\e94a"; }
.d-icon-zoom:before { content: "\e94b"; }
.d-icon-right-circle:before { content: "\e94c"; }
.d-icon-left-circle:before { content: "\e94d"; }
.d-icon-up-circle:before { content: "\e94e"; }
.d-icon-down-circle:before { content: "\e94f"; }
.d-icon-angle-right:before { content: "\e950"; }
.d-icon-angle-left:before { content: "\e951"; }
.d-icon-angle-up:before { content: "\e952"; }
.d-icon-angle-down:before { content: "\e953"; }
.d-icon-star:before { content: "\e954"; }
.d-icon-star-full:before { content: "\e955"; }
.d-icon-heart:before { content: "\e956"; }
.d-icon-heart-full:before { content: "\e957"; }
.d-icon-close:before { content: "\e958"; }
.d-icon-play-circle:before { content: "\e959"; }
.d-icon-home:before { content: "\e95a"; }
.d-icon-filter-2:before { content: "\e95b"; }
.d-icon-switch:before { content: "\e95c"; }
.d-icon-switch-left-on:before { content: "\e95d"; }
.d-icon-switch-right-on:before { content: "\e95e"; }
.d-icon-filter-3:before { content: "\e95f"; }
.d-icon-check:before { content: "\e960"; }
.d-icon-alert-solid:before { content: "\e961"; }
.d-icon-headphone:before { content: "\e962"; }
.d-icon-mode-grid:before { content: "\e80e"; }
.d-icon-mode-list:before { content: "\e80f"; }
.d-icon-cancel:before { content: "\e82c"; }
.d-icon-instagram:before { content: "\f16d"; }
.d-icon-solid-check:before { content: "\f17c"; }
.d-icon-play-solid:before { content: "\f2bf"; }
.d-icon-mobile:before { content: "\f294"; }
.d-icon-minus:before { content: "\f28f"; }
.d-icon-plus:before { content: "\f2c3"; }
.d-icon-wifi:before { content: "\f37d"; }
.d-icon-times:before { content: "\f343"; }
.d-icon-times-circle:before { content: "\f345"; }
.d-icon-random:before { content: "\f2d1"; }
.d-icon-th-list:before { content: "\f33c"; }
.d-icon-rocket:before { content: "\f2e4"; }
.d-icon-map-marker:before { content: "\f27e"; }
.d-icon-birthday-cake:before { content: "\f147"; }
.d-icon-gift:before { content: "\f213"; }
.d-icon-female:before { content: "\f1de"; }
.d-icon-clock:before { content: "\f190"; }
.d-icon-comments:before { content: "\e97b"; }
.d-icon-rotate-left:before { content: "\f2e2"; }
.d-icon-rotate-right:before { content: "\f2e3"; }
.d-icon-reading:before { content: "\e963"; }
.d-icon-feeder:before { content: "\e964"; }
.d-icon-birthday-cake2:before { content: "\e965"; }
.d-icon-skirt:before { content: "\e966"; }
.d-icon-toy:before { content: "\e967"; }
.d-icon-butterfly:before { content: "\e968"; }
.d-icon-babycare2:before { content: "\e96a"; }
.d-icon-butterfly2:before { content: "\e96b"; }
.d-icon-dinner-set:before { content: "\e96c"; }
.d-icon-ball2:before { content: "\e96d"; }
.d-icon-apple:before { content: "\e96e"; }
.d-icon-icecream:before { content: "\e96f"; }
.d-icon-cake:before { content: "\e970"; }
.d-icon-meat:before { content: "\e971"; }
.d-icon-bread:before { content: "\e972"; }
.d-icon-cocktail:before { content: "\e973"; }
.d-icon-food:before { content: "\e974"; }
.d-icon-birthday-cake3:before { content: "\e975"; }
.d-icon-cash:before { content: "\e976"; }
.d-icon-service2:before { content: "\e977"; }
.d-icon-car:before { content: "\e978"; }
.d-icon-ball3:before { content: "\e979"; }
.d-icon-compare:before { content: "\e97a"; }
.d-icon-jar:before { content: "\e97b"; }
.d-icon-radish:before { content: "\e97c"; }
.d-icon-arrow-down:before { content: "\e97d"; }
.d-icon-arrow-left:before { content: "\e97e"; }
.d-icon-arrow-right:before { content: "\e97f"; }
.d-icon-arrow-up:before { content: "\e980"; }
.d-icon-earth:before { content: "\e981"; }
.d-icon-long-arrow-left:before { content: "\e982"; }
.d-icon-long-arrow-right:before { content: "\e983"; }
.d-icon-long-arrow-down:before { content: "\e984"; }
.d-icon-long-arrow-up:before { content: "\e985"; }
.d-icon-pill:before { content: "\e986"; }
.d-icon-cylinder:before { content: "\e987"; }
.d-icon-medical-bag:before { content: "\e988"; }
.d-icon-graph:before { content: "\e989"; }