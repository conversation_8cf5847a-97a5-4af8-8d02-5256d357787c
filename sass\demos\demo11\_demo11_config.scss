﻿/*
Demo 11 Variables
*/
@include set(
    (
        base: (
            title: (
                font-size: 2rem,
                font-weight: 700,
                line-height: 2.25,
                letter-spacing: -.000 9em,
                margin-bottom: 2rem,
                border: (
                    color: $lighter-color,
                    width: 1px
                )
            )
        ),
        header: (
            bottom: (
                color: #fff
            ),
            sticky: (
                padding-top: 0,
                padding-bottom: 0,
                background-color: #333,
                box-shadow: none
            ),
            inner-wrap: (
                padding-right: 1.7rem,
                background-color: #333
            ),
            main-nav: (
                margin: 0 1.8rem,
            ),
            دسته بندی: (
                toggle: (
                    padding: 1.5rem 1.5rem 1.5rem 1.6rem,
                    icon: (
                        font-size: 2.38rem,
                    )
                )
            )
        ),
        menu: (
            ancestor: (
                _gap: 3rem,
                text-transform: uppercase,
                font-weight: 600,
                padding: 2rem 0
            )
        ),
        product: (
            rating: (
                _star-color: #666
            ),
            body: (
                padding-bottom: 0
            ),
            name: (
                margin-bottom: .2rem
            ),
            price: (
                margin-bottom: .2rem
            )
        ),
        post: (
			body: (
				padding: 1.9rem 0 1.8rem,
			),
			meta: (
				margin-bottom: .8rem
			),
			info: (
				margin-bottom: .7rem
			),
			title: (
				text-transform: none,
				margin-bottom: 1.3rem,
				margin-left: -2px,
				padding-left: 2px,
				font-size: 1.6rem,
				letter-spacing: 0,
			),
			icon: (
				_gap: .6rem
			),
			detail: (
				padding: 2.3rem 0 1.8rem
			)
		),
        category-menu: (
            padding: 0.5rem 0 0.4rem,
            background-color: #f2f3f5,
            ancestor: ( 
                _split-line: 1px solid #e4e5e5,
                padding: 1.4rem 0 1.5rem 0.4rem,
                min-height: 4.4rem,
                line-height: 1,
                letter-spacing: -.025em,
                font-size: 1.4rem,
                text-transform: none,
                font-weight: 400,
                color: #222
            ),
            icon: (
                margin-right: .8rem,
                margin-bottom: .2rem,
                font-size: 1.8rem,
                color: #666
            )
        ),
        widget: (
            title: (
                border-bottom: 1px solid $border-color-light
            )
        ),
        footer: (
			middle: (
				padding: 82px 0px 40px 0px
			),
			bottom: (
				padding: 2.7rem 0 2.8rem
			),
			about: (
                logo: (
					margin-top: -1px,
                    margin-bottom: 1.9rem,
                ),
                p: (
                    margin-bottom: 2.7rem,
                    letter-spacing: -.000 8em
                )
            ),
			copyright: (
                color: #666,
                font-weight: 500,
				letter-spacing: -.01em
			),
			social-link: (
				width: 2.9rem,
				height: 2.9rem
			)
		)
    )
)